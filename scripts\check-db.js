/**
 * Script pour vérifier la connexion à la base de données
 * Usage: node scripts/check-db.js
 */

const { PrismaClient } = require('@prisma/client');

async function checkDatabase() {
    console.log('🔍 Vérification de la Base de Données');
    console.log('===================================');
    console.log('');

    const prisma = new PrismaClient();

    try {
        console.log('🔄 Test de connexion à la base de données...');
        
        // Test de connexion basique
        await prisma.$connect();
        console.log('✅ Connexion à la base de données réussie');

        // Vérifier les tables principales
        console.log('');
        console.log('📊 Vérification des tables:');
        
        const casCount = await prisma.cas.count();
        console.log(`• Table 'cas': ${casCount} enregistrements`);
        
        const blocageCount = await prisma.blocage.count();
        console.log(`• Table 'blocage': ${blocageCount} enregistrements`);
        
        const userCount = await prisma.user.count();
        console.log(`• Table 'user': ${userCount} enregistrements`);

        // Statistiques détaillées
        console.log('');
        console.log('📈 Statistiques détaillées:');
        
        const casRegularises = await prisma.cas.count({
            where: { regularisation: true }
        });
        console.log(`• Cas régularisés: ${casRegularises}/${casCount} (${casCount > 0 ? Math.round((casRegularises/casCount)*100) : 0}%)`);
        
        const blocageStats = {
            acceptes: await prisma.blocage.count({ where: { resolution: "ACCEPTE" } }),
            ajournes: await prisma.blocage.count({ where: { resolution: "AJOURNE" } }),
            rejetes: await prisma.blocage.count({ where: { resolution: "REJETE" } }),
            attente: await prisma.blocage.count({ where: { resolution: "ATTENTE" } })
        };
        
        console.log(`• Blocages acceptés: ${blocageStats.acceptes}`);
        console.log(`• Blocages ajournés: ${blocageStats.ajournes}`);
        console.log(`• Blocages rejetés: ${blocageStats.rejetes}`);
        console.log(`• Blocages en attente: ${blocageStats.attente}`);

        // Vérifier les utilisateurs admin
        console.log('');
        console.log('👥 Utilisateurs administrateurs:');
        const admins = await prisma.user.findMany({
            where: { role: 'ADMIN' },
            select: { id: true, username: true, role: true }
        });
        
        if (admins.length > 0) {
            admins.forEach(admin => {
                console.log(`• ${admin.username} (ID: ${admin.id})`);
            });
        } else {
            console.log('⚠️ Aucun utilisateur administrateur trouvé');
        }

        console.log('');
        console.log('✅ Vérification terminée avec succès !');
        console.log('');
        console.log('💡 Vous pouvez maintenant utiliser:');
        console.log('• node scripts/reset-data-direct.js (pour réinitialiser directement)');
        console.log('• node scripts/get-admin-token.js username password (pour obtenir un token)');

    } catch (error) {
        console.error('❌ Erreur lors de la vérification:', error.message);
        console.log('');
        console.log('💡 Solutions possibles:');
        console.log('• Vérifiez que la base de données est démarrée');
        console.log('• Vérifiez les variables d\'environnement (.env)');
        console.log('• Exécutez: npx prisma generate');
        console.log('• Exécutez: npx prisma db push');
    } finally {
        await prisma.$disconnect();
    }
}

// Exécuter le script
checkDatabase().catch(console.error);
