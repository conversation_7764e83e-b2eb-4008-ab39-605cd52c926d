import { NextRequest, NextResponse } from "next/server";
import { verifyToken } from "@/lib/auth";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token manquant" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload || userPayload.role !== "ADMIN") {
            return NextResponse.json(
                { error: "Accès non autorisé - Admin requis" },
                { status: 403 }
            );
        }

        console.log("🧪 Test du filtre par statut des cas");
        const startTime = performance.now();

        const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
        const tests = [];

        // Test 1: Récupérer tous les cas (sans filtre)
        console.log("📊 Test 1: Tous les cas");
        const allCasResponse = await fetch(`${baseUrl}/api/cas?page=1&pageSize=1000`, {
            headers: { 'Cookie': `token=${token}` }
        });

        let allCasData = null;
        if (allCasResponse.ok) {
            allCasData = await allCasResponse.json();
        }

        tests.push({
            test: "Tous les cas",
            status: allCasResponse.status,
            success: allCasResponse.ok,
            totalCount: allCasData?.pagination?.totalCount || 0
        });

        // Test 2: Filtre par statut REGULARISE
        console.log("🟢 Test 2: Cas régularisés");
        const regulariseResponse = await fetch(`${baseUrl}/api/cas?casStatus=REGULARISE&page=1&pageSize=1000`, {
            headers: { 'Cookie': `token=${token}` }
        });

        let regulariseData = null;
        if (regulariseResponse.ok) {
            regulariseData = await regulariseResponse.json();
        }

        tests.push({
            test: "Cas REGULARISE",
            status: regulariseResponse.status,
            success: regulariseResponse.ok,
            totalCount: regulariseData?.pagination?.totalCount || 0
        });

        // Test 3: Filtre par statut AJOURNE
        console.log("🟡 Test 3: Cas ajournés");
        const ajourneResponse = await fetch(`${baseUrl}/api/cas?casStatus=AJOURNE&page=1&pageSize=1000`, {
            headers: { 'Cookie': `token=${token}` }
        });

        let ajourneData = null;
        if (ajourneResponse.ok) {
            ajourneData = await ajourneResponse.json();
        }

        tests.push({
            test: "Cas AJOURNE",
            status: ajourneResponse.status,
            success: ajourneResponse.ok,
            totalCount: ajourneData?.pagination?.totalCount || 0
        });

        // Test 4: Filtre par statut NON_EXAMINE
        console.log("⚪ Test 4: Cas non examinés");
        const nonExamineResponse = await fetch(`${baseUrl}/api/cas?casStatus=NON_EXAMINE&page=1&pageSize=1000`, {
            headers: { 'Cookie': `token=${token}` }
        });

        let nonExamineData = null;
        if (nonExamineResponse.ok) {
            nonExamineData = await nonExamineResponse.json();
        }

        tests.push({
            test: "Cas NON_EXAMINE",
            status: nonExamineResponse.status,
            success: nonExamineResponse.ok,
            totalCount: nonExamineData?.pagination?.totalCount || 0
        });

        // Test 5: Filtre par statut REJETE
        console.log("🔴 Test 5: Cas rejetés");
        const rejeteResponse = await fetch(`${baseUrl}/api/cas?casStatus=REJETE&page=1&pageSize=1000`, {
            headers: { 'Cookie': `token=${token}` }
        });

        let rejeteData = null;
        if (rejeteResponse.ok) {
            rejeteData = await rejeteResponse.json();
        }

        tests.push({
            test: "Cas REJETE",
            status: rejeteResponse.status,
            success: rejeteResponse.ok,
            totalCount: rejeteData?.pagination?.totalCount || 0
        });

        // Vérifications de cohérence
        const totalFiltered = (regulariseData?.pagination?.totalCount || 0) +
                             (ajourneData?.pagination?.totalCount || 0) +
                             (nonExamineData?.pagination?.totalCount || 0) +
                             (rejeteData?.pagination?.totalCount || 0);

        const coherenceCheck = {
            totalAll: allCasData?.pagination?.totalCount || 0,
            totalFiltered,
            isCoherent: Math.abs((allCasData?.pagination?.totalCount || 0) - totalFiltered) <= 1, // Tolérance de 1 pour les cas edge
            message: ""
        };

        coherenceCheck.message = coherenceCheck.isCoherent 
            ? `✅ Cohérence vérifiée: ${coherenceCheck.totalAll} = ${coherenceCheck.totalFiltered}`
            : `❌ Incohérence: Total=${coherenceCheck.totalAll}, Somme filtrée=${coherenceCheck.totalFiltered}`;

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        const allTestsPassed = tests.every(test => test.success) && coherenceCheck.isCoherent;

        return NextResponse.json({
            success: true,
            message: "Test du filtre par statut terminé",
            allTestsPassed,
            tests,
            coherenceCheck,
            summary: {
                totalCas: allCasData?.pagination?.totalCount || 0,
                regularises: regulariseData?.pagination?.totalCount || 0,
                ajournes: ajourneData?.pagination?.totalCount || 0,
                nonExamines: nonExamineData?.pagination?.totalCount || 0,
                rejetes: rejeteData?.pagination?.totalCount || 0
            },
            performance: {
                duration,
                timestamp: new Date().toISOString()
            },
            recommendations: allTestsPassed ? [
                "✅ Le filtre par statut fonctionne correctement",
                "✅ Les totaux sont cohérents",
                "✅ Tous les statuts sont correctement filtrés"
            ] : [
                "⚠️ Problèmes détectés avec le filtre par statut",
                "🔍 Vérifiez les logs pour plus de détails",
                "🛠️ Corrigez les problèmes avant la mise en production"
            ]
        });

    } catch (error: any) {
        console.error('❌ Erreur dans test filtre statut:', error);
        return NextResponse.json({
            success: false,
            error: "Erreur lors du test du filtre par statut",
            details: error.message
        }, { status: 500 });
    }
}
