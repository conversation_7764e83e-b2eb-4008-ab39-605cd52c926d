import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyToken } from "@/lib/auth";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token manquant" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload) {
            return NextResponse.json(
                { error: "Token invalide" },
                { status: 401 }
            );
        }

        console.log("🧪 Test des APIs de statistiques...");
        const startTime = performance.now();

        const results = [];

        // Test 1: API regularisation-par-secteur
        try {
            console.log("📊 Test API regularisation-par-secteur...");
            const response = await fetch(
                `${
                    process.env.NEXTAUTH_URL || "http://localhost:3000"
                }/api/stats/regularisation-par-secteur`,
                {
                    headers: {
                        Cookie: `token=${token}`,
                    },
                }
            );

            if (response.ok) {
                const data = await response.json();
                results.push({
                    api: "regularisation-par-secteur",
                    success: true,
                    dataCount: Array.isArray(data) ? data.length : 0,
                    sampleData: Array.isArray(data) ? data.slice(0, 2) : data,
                });
            } else {
                results.push({
                    api: "regularisation-par-secteur",
                    success: false,
                    error: `HTTP ${response.status}: ${response.statusText}`,
                });
            }
        } catch (error: any) {
            results.push({
                api: "regularisation-par-secteur",
                success: false,
                error: error.message,
            });
        }

        // Test 2: API wilayas-secteurs
        try {
            console.log("📊 Test API wilayas-secteurs...");
            const response = await fetch(
                `${
                    process.env.NEXTAUTH_URL || "http://localhost:3000"
                }/api/stats/wilayas-secteurs`,
                {
                    headers: {
                        Cookie: `token=${token}`,
                    },
                }
            );

            if (response.ok) {
                const data = await response.json();
                results.push({
                    api: "wilayas-secteurs",
                    success: true,
                    dataCount: Array.isArray(data) ? data.length : 0,
                    sampleData: Array.isArray(data) ? data.slice(0, 2) : data,
                });
            } else {
                results.push({
                    api: "wilayas-secteurs",
                    success: false,
                    error: `HTTP ${response.status}: ${response.statusText}`,
                });
            }
        } catch (error: any) {
            results.push({
                api: "wilayas-secteurs",
                success: false,
                error: error.message,
            });
        }

        // Test 3: API cas-par-wilaya
        try {
            console.log("📊 Test API cas-par-wilaya...");
            const response = await fetch(
                `${
                    process.env.NEXTAUTH_URL || "http://localhost:3000"
                }/api/stats/cas-par-wilaya`,
                {
                    headers: {
                        Cookie: `token=${token}`,
                    },
                }
            );

            if (response.ok) {
                const data = await response.json();
                results.push({
                    api: "cas-par-wilaya",
                    success: true,
                    dataCount: Array.isArray(data) ? data.length : 0,
                    sampleData: Array.isArray(data) ? data.slice(0, 2) : data,
                });
            } else {
                results.push({
                    api: "cas-par-wilaya",
                    success: false,
                    error: `HTTP ${response.status}: ${response.statusText}`,
                });
            }
        } catch (error: any) {
            results.push({
                api: "cas-par-wilaya",
                success: false,
                error: error.message,
            });
        }

        // Test 4: Test de l'analyse complète
        console.log("🧪 Test 4: API analyse complète...");
        const analyseCompleteResponse = await fetch(
            `${baseUrl}/api/stats/analyse-complete`,
            {
                headers: { Cookie: `token=${token}` },
            }
        );

        const analyseCompleteResult = {
            status: analyseCompleteResponse.status,
            success: analyseCompleteResponse.ok,
            data: null as any,
            error: null as string | null,
        };

        if (analyseCompleteResponse.ok) {
            const analyseData = await analyseCompleteResponse.json();
            analyseCompleteResult.data = {
                totalCas: analyseData.data?.totalCas || 0,
                totalWilayas: analyseData.data?.totalWilayas || 0,
                hasTableauStatuts: !!analyseData.data?.tableauStatuts,
                hasTableauContraintes: !!analyseData.data?.tableauContraintes,
                secteurIncluded:
                    analyseData.data?.tableauContraintes?.some((dsa: any) =>
                        dsa.encrages?.some(
                            (encrage: any) =>
                                encrage.secteur &&
                                encrage.secteur !== "Secteur non défini"
                        )
                    ) || false,
            };
        } else {
            analyseCompleteResult.error = `Erreur ${analyseCompleteResponse.status}`;
        }

        results.push({
            test: "Analyse Complète",
            ...analyseCompleteResult,
        });

        // Test 5: Vérification de cohérence entre APIs
        console.log("🧪 Test 5: Cohérence entre APIs...");
        const coherenceTest = {
            simpleVsComplete: false,
            message: "",
        };

        if (simpleResult.data && analyseCompleteResult.data) {
            const simpleTotalCas = simpleResult.data.general?.totalCas || 0;
            const completeTotalCas = analyseCompleteResult.data.totalCas || 0;

            coherenceTest.simpleVsComplete =
                simpleTotalCas === completeTotalCas;
            coherenceTest.message = coherenceTest.simpleVsComplete
                ? `✅ Cohérence: ${simpleTotalCas} cas`
                : `❌ Incohérence: Simple=${simpleTotalCas}, Complète=${completeTotalCas}`;
        }

        results.push({
            test: "Cohérence APIs",
            status: 200,
            success: coherenceTest.simpleVsComplete,
            data: coherenceTest,
            error: null,
        });

        // Test 6: Vérification des données de base
        const baseCounts = {
            cas: await prisma.cas.count(),
            blocages: await prisma.blocage.count(),
            secteurs: await prisma.secteur.count(),
            problematiques: await prisma.problematique.count(),
        };

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        return NextResponse.json({
            success: true,
            message: "Tests des APIs de statistiques terminés",
            results,
            baseCounts,
            performance: {
                duration,
                timestamp: new Date().toISOString(),
            },
        });
    } catch (error: any) {
        console.error("❌ Erreur dans test APIs stats:", error);
        return NextResponse.json(
            {
                success: false,
                error: "Erreur lors des tests des APIs de statistiques",
                details: error.message,
            },
            { status: 500 }
        );
    }
}
