"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/statistiques/page",{

/***/ "(app-pages-browser)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   fetchApi: () => (/* binding */ fetchApi),\n/* harmony export */   getCas: () => (/* binding */ getCas),\n/* harmony export */   getChat: () => (/* binding */ getChat),\n/* harmony export */   sendMessage: () => (/* binding */ sendMessage)\n/* harmony export */ });\nasync function fetchApi(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { method = \"GET\", body } = options;\n    const response = await fetch(endpoint, {\n        method,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...options.headers\n        },\n        credentials: options.credentials || \"include\",\n        body: body ? JSON.stringify(body) : undefined\n    });\n    if (!response.ok) {\n        let errorMessage = \"HTTP error! status: \".concat(response.status);\n        let errorData = null;\n        try {\n            // Read the response body as text first\n            const errorText = await response.text();\n            if (errorText) {\n                try {\n                    // Try to parse the text as JSON\n                    errorData = JSON.parse(errorText);\n                    errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.error) || (errorData === null || errorData === void 0 ? void 0 : errorData.message) || errorText;\n                } catch (parseError) {\n                    // If parsing fails, use the raw text as the error message\n                    errorMessage = errorText || errorMessage;\n                }\n            }\n        } catch (readError) {\n            // If we can't read the response body, use the status\n            console.warn(\"Could not read error response body:\", readError);\n        }\n        // Log error details for debugging (but don't throw console.error)\n        if (true) {\n            console.warn(\"API Error Details:\", {\n                url: response.url,\n                status: response.status,\n                statusText: response.statusText,\n                errorMessage,\n                errorData\n            });\n        }\n        // Handle specific error cases\n        if (response.status === 401) {\n            throw new Error(\"Authentication required. Please log in again.\");\n        } else if (response.status === 403) {\n            throw new Error(\"Access denied. You don't have permission to perform this action.\");\n        } else if (response.status === 404) {\n            throw new Error(\"Resource not found.\");\n        } else if (response.status >= 500) {\n            throw new Error(\"Server error. Please try again later.\");\n        }\n        throw new Error(errorMessage);\n    }\n    if (response.status === 204) {\n        // No Content\n        return null;\n    }\n    return response.json();\n}\n// Add the following apiClient export:\nconst apiClient = {\n    get: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"GET\"\n        });\n    },\n    post: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"POST\",\n            body\n        });\n    },\n    put: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"PUT\",\n            body\n        });\n    },\n    delete: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"DELETE\"\n        });\n    }\n};\n// Chat Functions\nconst getChat = (casId)=>{\n    return apiClient.get(\"/api/chats/\".concat(casId));\n};\nconst sendMessage = (casId, content)=>{\n    return apiClient.post(\"/api/chats/\".concat(casId, \"/messages\"), {\n        content\n    });\n};\nasync function fetchPage(page, pageSize) {\n    const response = await fetch(\"/api/cas?page=\".concat(page, \"&pageSize=\").concat(pageSize), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        credentials: \"include\"\n    });\n    if (!response.ok) {\n        let errorMessage = \"API request failed with status \".concat(response.status);\n        try {\n            const errorText = await response.text();\n            if (errorText) {\n                try {\n                    const errorData = JSON.parse(errorText);\n                    errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.error) || (errorData === null || errorData === void 0 ? void 0 : errorData.message) || errorText;\n                } catch (e) {\n                    errorMessage = errorText;\n                }\n            }\n        } catch (e) {\n        // If we can't read the response, use the default message\n        }\n        if (true) {\n            console.warn(\"getCas API Error:\", {\n                status: response.status,\n                statusText: response.statusText,\n                url: response.url,\n                errorMessage\n            });\n        }\n        throw new Error(errorMessage);\n    }\n    return response.json();\n}\nasync function getCas() {\n    try {\n        console.log(\"Fetching dossiers from /api/cas...\");\n        const pageSize = 100; // Maximum allowed by the API\n        let currentPage = 1;\n        let allDossiers = [];\n        let hasMorePages = true;\n        let totalPages = 1;\n        // Fetch all pages\n        while(hasMorePages && currentPage <= 20){\n            // Add a safety limit of 20 pages\n            console.log(\"Fetching page \".concat(currentPage, \"...\"));\n            const result = await fetchPage(currentPage, pageSize);\n            if (!result.data || !Array.isArray(result.data)) {\n                if (true) {\n                    console.warn(\"Invalid data format in page\", currentPage, \":\", result);\n                }\n                throw new Error(\"Invalid data format received from server\");\n            }\n            allDossiers = [\n                ...allDossiers,\n                ...result.data\n            ];\n            totalPages = result.pagination.totalPages;\n            hasMorePages = result.pagination.hasNextPage && currentPage < totalPages;\n            currentPage++;\n            // If we've fetched all pages or reached the safety limit, stop\n            if (!hasMorePages || currentPage > totalPages) {\n                break;\n            }\n        }\n        console.log(\"Fetched \".concat(allDossiers.length, \" dossiers from \").concat(currentPage - 1, \" pages\"));\n        return allDossiers;\n    } catch (error) {\n        if (true) {\n            if (error instanceof Error) {\n                console.warn(\"Error in getCas:\", {\n                    message: error.message,\n                    name: error.name\n                });\n            } else {\n                console.warn(\"Unknown error in getCas:\", error);\n            }\n        }\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9hcGktY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBT08sZUFBZUEsU0FDbEJDLFFBQWdCO1FBQ2hCQyxVQUFBQSxpRUFBd0IsQ0FBQztJQUV6QixNQUFNLEVBQUVDLFNBQVMsS0FBSyxFQUFFQyxJQUFJLEVBQUUsR0FBR0Y7SUFFakMsTUFBTUcsV0FBVyxNQUFNQyxNQUFNTCxVQUFVO1FBQ25DRTtRQUNBSSxTQUFTO1lBQ0wsZ0JBQWdCO1lBQ2hCLEdBQUdMLFFBQVFLLE9BQU87UUFDdEI7UUFDQUMsYUFBYU4sUUFBUU0sV0FBVyxJQUFJO1FBQ3BDSixNQUFNQSxPQUFPSyxLQUFLQyxTQUFTLENBQUNOLFFBQVFPO0lBQ3hDO0lBRUEsSUFBSSxDQUFDTixTQUFTTyxFQUFFLEVBQUU7UUFDZCxJQUFJQyxlQUFlLHVCQUF1QyxPQUFoQlIsU0FBU1MsTUFBTTtRQUN6RCxJQUFJQyxZQUFpQjtRQUVyQixJQUFJO1lBQ0EsdUNBQXVDO1lBQ3ZDLE1BQU1DLFlBQVksTUFBTVgsU0FBU1ksSUFBSTtZQUVyQyxJQUFJRCxXQUFXO2dCQUNYLElBQUk7b0JBQ0EsZ0NBQWdDO29CQUNoQ0QsWUFBWU4sS0FBS1MsS0FBSyxDQUFDRjtvQkFDdkJILGVBQ0lFLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV0ksS0FBSyxNQUFJSixzQkFBQUEsZ0NBQUFBLFVBQVdLLE9BQU8sS0FBSUo7Z0JBQ2xELEVBQUUsT0FBT0ssWUFBWTtvQkFDakIsMERBQTBEO29CQUMxRFIsZUFBZUcsYUFBYUg7Z0JBQ2hDO1lBQ0o7UUFDSixFQUFFLE9BQU9TLFdBQVc7WUFDaEIscURBQXFEO1lBQ3JEQyxRQUFRQyxJQUFJLENBQUMsdUNBQXVDRjtRQUN4RDtRQUVBLGtFQUFrRTtRQUNsRSxJQUFJRyxJQUFzQyxFQUFFO1lBQ3hDRixRQUFRQyxJQUFJLENBQUMsc0JBQXNCO2dCQUMvQkUsS0FBS3JCLFNBQVNxQixHQUFHO2dCQUNqQlosUUFBUVQsU0FBU1MsTUFBTTtnQkFDdkJhLFlBQVl0QixTQUFTc0IsVUFBVTtnQkFDL0JkO2dCQUNBRTtZQUNKO1FBQ0o7UUFFQSw4QkFBOEI7UUFDOUIsSUFBSVYsU0FBU1MsTUFBTSxLQUFLLEtBQUs7WUFDekIsTUFBTSxJQUFJYyxNQUFNO1FBQ3BCLE9BQU8sSUFBSXZCLFNBQVNTLE1BQU0sS0FBSyxLQUFLO1lBQ2hDLE1BQU0sSUFBSWMsTUFDTjtRQUVSLE9BQU8sSUFBSXZCLFNBQVNTLE1BQU0sS0FBSyxLQUFLO1lBQ2hDLE1BQU0sSUFBSWMsTUFBTTtRQUNwQixPQUFPLElBQUl2QixTQUFTUyxNQUFNLElBQUksS0FBSztZQUMvQixNQUFNLElBQUljLE1BQU07UUFDcEI7UUFFQSxNQUFNLElBQUlBLE1BQU1mO0lBQ3BCO0lBRUEsSUFBSVIsU0FBU1MsTUFBTSxLQUFLLEtBQUs7UUFDekIsYUFBYTtRQUNiLE9BQU87SUFDWDtJQUVBLE9BQU9ULFNBQVN3QixJQUFJO0FBQ3hCO0FBRUEsc0NBQXNDO0FBQy9CLE1BQU1DLFlBQVk7SUFDckJDLEtBQUssQ0FDRDlCLFVBQ0FDO1FBRUEsT0FBT0YsU0FBWUMsVUFBVTtZQUFFLEdBQUdDLE9BQU87WUFBRUMsUUFBUTtRQUFNO0lBQzdEO0lBQ0E2QixNQUFNLENBQ0YvQixVQUNBRyxNQUNBRjtRQUVBLE9BQU9GLFNBQVlDLFVBQVU7WUFBRSxHQUFHQyxPQUFPO1lBQUVDLFFBQVE7WUFBUUM7UUFBSztJQUNwRTtJQUNBNkIsS0FBSyxDQUNEaEMsVUFDQUcsTUFDQUY7UUFFQSxPQUFPRixTQUFZQyxVQUFVO1lBQUUsR0FBR0MsT0FBTztZQUFFQyxRQUFRO1lBQU9DO1FBQUs7SUFDbkU7SUFDQThCLFFBQVEsQ0FDSmpDLFVBQ0FDO1FBRUEsT0FBT0YsU0FBWUMsVUFBVTtZQUFFLEdBQUdDLE9BQU87WUFBRUMsUUFBUTtRQUFTO0lBQ2hFO0FBS0osRUFBRTtBQTJCRixpQkFBaUI7QUFDVixNQUFNZ0MsVUFBVSxDQUFDQztJQUNwQixPQUFPTixVQUFVQyxHQUFHLENBQUMsY0FBb0IsT0FBTks7QUFDdkMsRUFBRTtBQUVLLE1BQU1DLGNBQWMsQ0FDdkJELE9BQ0FFO0lBRUEsT0FBT1IsVUFBVUUsSUFBSSxDQUFDLGNBQW9CLE9BQU5JLE9BQU0sY0FBWTtRQUFFRTtJQUFRO0FBQ3BFLEVBQUU7QUFjRixlQUFlQyxVQUNYQyxJQUFZLEVBQ1pDLFFBQWdCO0lBRWhCLE1BQU1wQyxXQUFXLE1BQU1DLE1BQU0saUJBQWtDbUMsT0FBakJELE1BQUssY0FBcUIsT0FBVEMsV0FBWTtRQUN2RXRDLFFBQVE7UUFDUkksU0FBUztZQUNMLGdCQUFnQjtRQUNwQjtRQUNBQyxhQUFhO0lBQ2pCO0lBRUEsSUFBSSxDQUFDSCxTQUFTTyxFQUFFLEVBQUU7UUFDZCxJQUFJQyxlQUFlLGtDQUFrRCxPQUFoQlIsU0FBU1MsTUFBTTtRQUVwRSxJQUFJO1lBQ0EsTUFBTUUsWUFBWSxNQUFNWCxTQUFTWSxJQUFJO1lBQ3JDLElBQUlELFdBQVc7Z0JBQ1gsSUFBSTtvQkFDQSxNQUFNRCxZQUFZTixLQUFLUyxLQUFLLENBQUNGO29CQUM3QkgsZUFDSUUsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXSSxLQUFLLE1BQUlKLHNCQUFBQSxnQ0FBQUEsVUFBV0ssT0FBTyxLQUFJSjtnQkFDbEQsRUFBRSxVQUFNO29CQUNKSCxlQUFlRztnQkFDbkI7WUFDSjtRQUNKLEVBQUUsVUFBTTtRQUNKLHlEQUF5RDtRQUM3RDtRQUVBLElBQUlTLElBQXNDLEVBQUU7WUFDeENGLFFBQVFDLElBQUksQ0FBQyxxQkFBcUI7Z0JBQzlCVixRQUFRVCxTQUFTUyxNQUFNO2dCQUN2QmEsWUFBWXRCLFNBQVNzQixVQUFVO2dCQUMvQkQsS0FBS3JCLFNBQVNxQixHQUFHO2dCQUNqQmI7WUFDSjtRQUNKO1FBRUEsTUFBTSxJQUFJZSxNQUFNZjtJQUNwQjtJQUVBLE9BQU9SLFNBQVN3QixJQUFJO0FBQ3hCO0FBRU8sZUFBZWE7SUFDbEIsSUFBSTtRQUNBbkIsUUFBUW9CLEdBQUcsQ0FBQztRQUNaLE1BQU1GLFdBQVcsS0FBSyw2QkFBNkI7UUFDbkQsSUFBSUcsY0FBYztRQUNsQixJQUFJQyxjQUFxQixFQUFFO1FBQzNCLElBQUlDLGVBQWU7UUFDbkIsSUFBSUMsYUFBYTtRQUVqQixrQkFBa0I7UUFDbEIsTUFBT0QsZ0JBQWdCRixlQUFlLEdBQUk7WUFDdEMsaUNBQWlDO1lBQ2pDckIsUUFBUW9CLEdBQUcsQ0FBQyxpQkFBNkIsT0FBWkMsYUFBWTtZQUN6QyxNQUFNSSxTQUFTLE1BQU1ULFVBQVVLLGFBQWFIO1lBRTVDLElBQUksQ0FBQ08sT0FBT0MsSUFBSSxJQUFJLENBQUNDLE1BQU1DLE9BQU8sQ0FBQ0gsT0FBT0MsSUFBSSxHQUFHO2dCQUM3QyxJQUFJeEIsSUFBc0MsRUFBRTtvQkFDeENGLFFBQVFDLElBQUksQ0FDUiwrQkFDQW9CLGFBQ0EsS0FDQUk7Z0JBRVI7Z0JBQ0EsTUFBTSxJQUFJcEIsTUFBTTtZQUNwQjtZQUVBaUIsY0FBYzttQkFBSUE7bUJBQWdCRyxPQUFPQyxJQUFJO2FBQUM7WUFDOUNGLGFBQWFDLE9BQU9JLFVBQVUsQ0FBQ0wsVUFBVTtZQUN6Q0QsZUFDSUUsT0FBT0ksVUFBVSxDQUFDQyxXQUFXLElBQUlULGNBQWNHO1lBQ25ESDtZQUVBLCtEQUErRDtZQUMvRCxJQUFJLENBQUNFLGdCQUFnQkYsY0FBY0csWUFBWTtnQkFDM0M7WUFDSjtRQUNKO1FBRUF4QixRQUFRb0IsR0FBRyxDQUNQLFdBQ0lDLE9BRE9DLFlBQVlTLE1BQU0sRUFBQyxtQkFFN0IsT0FER1YsY0FBYyxHQUNqQjtRQUVMLE9BQU9DO0lBQ1gsRUFBRSxPQUFPMUIsT0FBZ0I7UUFDckIsSUFBSU0sSUFBc0MsRUFBRTtZQUN4QyxJQUFJTixpQkFBaUJTLE9BQU87Z0JBQ3hCTCxRQUFRQyxJQUFJLENBQUMsb0JBQW9CO29CQUM3QkosU0FBU0QsTUFBTUMsT0FBTztvQkFDdEJtQyxNQUFNcEMsTUFBTW9DLElBQUk7Z0JBQ3BCO1lBQ0osT0FBTztnQkFDSGhDLFFBQVFDLElBQUksQ0FBQyw0QkFBNEJMO1lBQzdDO1FBQ0o7UUFDQSxNQUFNQTtJQUNWO0FBQ0oiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxhcGktY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbInR5cGUgRmV0Y2hPcHRpb25zID0ge1xuICAgIG1ldGhvZD86IFwiR0VUXCIgfCBcIlBPU1RcIiB8IFwiUFVUXCIgfCBcIkRFTEVURVwiIHwgXCJQQVRDSFwiOyAvLyBBZGRlZCBQQVRDSCBhcyBhbiBleGFtcGxlXG4gICAgYm9keT86IGFueTtcbiAgICBoZWFkZXJzPzogUmVjb3JkPHN0cmluZywgc3RyaW5nPjsgLy8gQWRkZWRcbiAgICBjcmVkZW50aWFscz86IFwiaW5jbHVkZVwiIHwgXCJzYW1lLW9yaWdpblwiIHwgXCJvbWl0XCI7IC8vIEFkZGVkXG59O1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZmV0Y2hBcGk8VD4oXG4gICAgZW5kcG9pbnQ6IHN0cmluZyxcbiAgICBvcHRpb25zOiBGZXRjaE9wdGlvbnMgPSB7fVxuKTogUHJvbWlzZTxUPiB7XG4gICAgY29uc3QgeyBtZXRob2QgPSBcIkdFVFwiLCBib2R5IH0gPSBvcHRpb25zO1xuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChlbmRwb2ludCwge1xuICAgICAgICBtZXRob2QsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICAgICAgLi4ub3B0aW9ucy5oZWFkZXJzLCAvLyBBbGxvdyBwYXNzaW5nIGN1c3RvbSBoZWFkZXJzXG4gICAgICAgIH0sXG4gICAgICAgIGNyZWRlbnRpYWxzOiBvcHRpb25zLmNyZWRlbnRpYWxzIHx8IFwiaW5jbHVkZVwiLCAvLyBJbmNsdWRlIGNyZWRlbnRpYWxzIChjb29raWVzKSB3aXRoIGV2ZXJ5IHJlcXVlc3RcbiAgICAgICAgYm9keTogYm9keSA/IEpTT04uc3RyaW5naWZ5KGJvZHkpIDogdW5kZWZpbmVkLFxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBsZXQgZXJyb3JNZXNzYWdlID0gYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YDtcbiAgICAgICAgbGV0IGVycm9yRGF0YTogYW55ID0gbnVsbDtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gUmVhZCB0aGUgcmVzcG9uc2UgYm9keSBhcyB0ZXh0IGZpcnN0XG4gICAgICAgICAgICBjb25zdCBlcnJvclRleHQgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XG5cbiAgICAgICAgICAgIGlmIChlcnJvclRleHQpIHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAvLyBUcnkgdG8gcGFyc2UgdGhlIHRleHQgYXMgSlNPTlxuICAgICAgICAgICAgICAgICAgICBlcnJvckRhdGEgPSBKU09OLnBhcnNlKGVycm9yVGV4dCk7XG4gICAgICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvckRhdGE/LmVycm9yIHx8IGVycm9yRGF0YT8ubWVzc2FnZSB8fCBlcnJvclRleHQ7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiBwYXJzaW5nIGZhaWxzLCB1c2UgdGhlIHJhdyB0ZXh0IGFzIHRoZSBlcnJvciBtZXNzYWdlXG4gICAgICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yVGV4dCB8fCBlcnJvck1lc3NhZ2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChyZWFkRXJyb3IpIHtcbiAgICAgICAgICAgIC8vIElmIHdlIGNhbid0IHJlYWQgdGhlIHJlc3BvbnNlIGJvZHksIHVzZSB0aGUgc3RhdHVzXG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXCJDb3VsZCBub3QgcmVhZCBlcnJvciByZXNwb25zZSBib2R5OlwiLCByZWFkRXJyb3IpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gTG9nIGVycm9yIGRldGFpbHMgZm9yIGRlYnVnZ2luZyAoYnV0IGRvbid0IHRocm93IGNvbnNvbGUuZXJyb3IpXG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZlbG9wbWVudFwiKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXCJBUEkgRXJyb3IgRGV0YWlsczpcIiwge1xuICAgICAgICAgICAgICAgIHVybDogcmVzcG9uc2UudXJsLFxuICAgICAgICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgICAgICAgIHN0YXR1c1RleHQ6IHJlc3BvbnNlLnN0YXR1c1RleHQsXG4gICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlLFxuICAgICAgICAgICAgICAgIGVycm9yRGF0YSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gSGFuZGxlIHNwZWNpZmljIGVycm9yIGNhc2VzXG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQXV0aGVudGljYXRpb24gcmVxdWlyZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uXCIpO1xuICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAzKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICAgICAgXCJBY2Nlc3MgZGVuaWVkLiBZb3UgZG9uJ3QgaGF2ZSBwZXJtaXNzaW9uIHRvIHBlcmZvcm0gdGhpcyBhY3Rpb24uXCJcbiAgICAgICAgICAgICk7XG4gICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDQpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIlJlc291cmNlIG5vdCBmb3VuZC5cIik7XG4gICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2Uuc3RhdHVzID49IDUwMCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiU2VydmVyIGVycm9yLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLlwiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgIH1cblxuICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDIwNCkge1xuICAgICAgICAvLyBObyBDb250ZW50XG4gICAgICAgIHJldHVybiBudWxsIGFzIFQ7XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlc3BvbnNlLmpzb24oKTtcbn1cblxuLy8gQWRkIHRoZSBmb2xsb3dpbmcgYXBpQ2xpZW50IGV4cG9ydDpcbmV4cG9ydCBjb25zdCBhcGlDbGllbnQgPSB7XG4gICAgZ2V0OiA8VD4oXG4gICAgICAgIGVuZHBvaW50OiBzdHJpbmcsXG4gICAgICAgIG9wdGlvbnM/OiBPbWl0PEZldGNoT3B0aW9ucywgXCJtZXRob2RcIiB8IFwiYm9keVwiPlxuICAgICk6IFByb21pc2U8VD4gPT4ge1xuICAgICAgICByZXR1cm4gZmV0Y2hBcGk8VD4oZW5kcG9pbnQsIHsgLi4ub3B0aW9ucywgbWV0aG9kOiBcIkdFVFwiIH0pO1xuICAgIH0sXG4gICAgcG9zdDogPFQ+KFxuICAgICAgICBlbmRwb2ludDogc3RyaW5nLFxuICAgICAgICBib2R5OiBhbnksXG4gICAgICAgIG9wdGlvbnM/OiBPbWl0PEZldGNoT3B0aW9ucywgXCJtZXRob2RcIiB8IFwiYm9keVwiPlxuICAgICk6IFByb21pc2U8VD4gPT4ge1xuICAgICAgICByZXR1cm4gZmV0Y2hBcGk8VD4oZW5kcG9pbnQsIHsgLi4ub3B0aW9ucywgbWV0aG9kOiBcIlBPU1RcIiwgYm9keSB9KTtcbiAgICB9LFxuICAgIHB1dDogPFQ+KFxuICAgICAgICBlbmRwb2ludDogc3RyaW5nLFxuICAgICAgICBib2R5OiBhbnksXG4gICAgICAgIG9wdGlvbnM/OiBPbWl0PEZldGNoT3B0aW9ucywgXCJtZXRob2RcIiB8IFwiYm9keVwiPlxuICAgICk6IFByb21pc2U8VD4gPT4ge1xuICAgICAgICByZXR1cm4gZmV0Y2hBcGk8VD4oZW5kcG9pbnQsIHsgLi4ub3B0aW9ucywgbWV0aG9kOiBcIlBVVFwiLCBib2R5IH0pO1xuICAgIH0sXG4gICAgZGVsZXRlOiA8VD4oXG4gICAgICAgIGVuZHBvaW50OiBzdHJpbmcsXG4gICAgICAgIG9wdGlvbnM/OiBPbWl0PEZldGNoT3B0aW9ucywgXCJtZXRob2RcIiB8IFwiYm9keVwiPlxuICAgICk6IFByb21pc2U8VD4gPT4ge1xuICAgICAgICByZXR1cm4gZmV0Y2hBcGk8VD4oZW5kcG9pbnQsIHsgLi4ub3B0aW9ucywgbWV0aG9kOiBcIkRFTEVURVwiIH0pO1xuICAgIH0sXG4gICAgLy8gWW91IGNhbiBhZGQgb3RoZXIgSFRUUCBtZXRob2RzIGxpa2UgUEFUQ0ggaWYgbmVlZGVkXG4gICAgLy8gcGF0Y2g6IDxUPihlbmRwb2ludDogc3RyaW5nLCBib2R5OiBhbnksIG9wdGlvbnM/OiBPbWl0PEZldGNoT3B0aW9ucywgJ21ldGhvZCcgfCAnYm9keSc+KTogUHJvbWlzZTxUPiA9PiB7XG4gICAgLy8gICByZXR1cm4gZmV0Y2hBcGk8VD4oZW5kcG9pbnQsIHsgLi4ub3B0aW9ucywgbWV0aG9kOiAnUEFUQ0gnLCBib2R5IH0pO1xuICAgIC8vIH0sXG59O1xuXG4vLyBUeXBlc1xuZXhwb3J0IGludGVyZmFjZSBDaGF0IHtcbiAgICBpZDogc3RyaW5nO1xuICAgIG1lc3NhZ2VzOiBNZXNzYWdlW107XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTWVzc2FnZSB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBjb250ZW50OiBzdHJpbmc7XG4gICAgdXNlcklkOiBzdHJpbmc7XG4gICAgdXNlcjoge1xuICAgICAgICB1c2VybmFtZTogc3RyaW5nO1xuICAgIH07XG4gICAgY3JlYXRlZEF0OiBEYXRlO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENhcyB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBub206IHN0cmluZztcbiAgICBjb21tdW5lPzoge1xuICAgICAgICBpZDogc3RyaW5nO1xuICAgICAgICBub206IHN0cmluZztcbiAgICB9O1xufVxuXG4vLyBDaGF0IEZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IGdldENoYXQgPSAoY2FzSWQ6IHN0cmluZyk6IFByb21pc2U8Q2hhdD4gPT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQuZ2V0KGAvYXBpL2NoYXRzLyR7Y2FzSWR9YCk7XG59O1xuXG5leHBvcnQgY29uc3Qgc2VuZE1lc3NhZ2UgPSAoXG4gICAgY2FzSWQ6IHN0cmluZyxcbiAgICBjb250ZW50OiBzdHJpbmdcbik6IFByb21pc2U8TWVzc2FnZT4gPT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucG9zdChgL2FwaS9jaGF0cy8ke2Nhc0lkfS9tZXNzYWdlc2AsIHsgY29udGVudCB9KTtcbn07XG5cbmludGVyZmFjZSBQYWdpbmF0ZWRSZXNwb25zZTxUPiB7XG4gICAgZGF0YTogVFtdO1xuICAgIHBhZ2luYXRpb246IHtcbiAgICAgICAgcGFnZTogbnVtYmVyO1xuICAgICAgICBwYWdlU2l6ZTogbnVtYmVyO1xuICAgICAgICB0b3RhbENvdW50OiBudW1iZXI7XG4gICAgICAgIHRvdGFsUGFnZXM6IG51bWJlcjtcbiAgICAgICAgaGFzTmV4dFBhZ2U6IGJvb2xlYW47XG4gICAgICAgIGhhc1ByZXZQYWdlOiBib29sZWFuO1xuICAgIH07XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGZldGNoUGFnZShcbiAgICBwYWdlOiBudW1iZXIsXG4gICAgcGFnZVNpemU6IG51bWJlclxuKTogUHJvbWlzZTxQYWdpbmF0ZWRSZXNwb25zZTxDYXM+PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9jYXM/cGFnZT0ke3BhZ2V9JnBhZ2VTaXplPSR7cGFnZVNpemV9YCwge1xuICAgICAgICBtZXRob2Q6IFwiR0VUXCIsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICB9LFxuICAgICAgICBjcmVkZW50aWFsczogXCJpbmNsdWRlXCIsXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSBgQVBJIHJlcXVlc3QgZmFpbGVkIHdpdGggc3RhdHVzICR7cmVzcG9uc2Uuc3RhdHVzfWA7XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcbiAgICAgICAgICAgIGlmIChlcnJvclRleHQpIHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBKU09OLnBhcnNlKGVycm9yVGV4dCk7XG4gICAgICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvckRhdGE/LmVycm9yIHx8IGVycm9yRGF0YT8ubWVzc2FnZSB8fCBlcnJvclRleHQ7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yVGV4dDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2gge1xuICAgICAgICAgICAgLy8gSWYgd2UgY2FuJ3QgcmVhZCB0aGUgcmVzcG9uc2UsIHVzZSB0aGUgZGVmYXVsdCBtZXNzYWdlXG4gICAgICAgIH1cblxuICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwiZGV2ZWxvcG1lbnRcIikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKFwiZ2V0Q2FzIEFQSSBFcnJvcjpcIiwge1xuICAgICAgICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgICAgICAgIHN0YXR1c1RleHQ6IHJlc3BvbnNlLnN0YXR1c1RleHQsXG4gICAgICAgICAgICAgICAgdXJsOiByZXNwb25zZS51cmwsXG4gICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cblxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICB9XG5cbiAgICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0Q2FzKCk6IFByb21pc2U8Q2FzW10+IHtcbiAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZyhcIkZldGNoaW5nIGRvc3NpZXJzIGZyb20gL2FwaS9jYXMuLi5cIik7XG4gICAgICAgIGNvbnN0IHBhZ2VTaXplID0gMTAwOyAvLyBNYXhpbXVtIGFsbG93ZWQgYnkgdGhlIEFQSVxuICAgICAgICBsZXQgY3VycmVudFBhZ2UgPSAxO1xuICAgICAgICBsZXQgYWxsRG9zc2llcnM6IENhc1tdID0gW107XG4gICAgICAgIGxldCBoYXNNb3JlUGFnZXMgPSB0cnVlO1xuICAgICAgICBsZXQgdG90YWxQYWdlcyA9IDE7XG5cbiAgICAgICAgLy8gRmV0Y2ggYWxsIHBhZ2VzXG4gICAgICAgIHdoaWxlIChoYXNNb3JlUGFnZXMgJiYgY3VycmVudFBhZ2UgPD0gMjApIHtcbiAgICAgICAgICAgIC8vIEFkZCBhIHNhZmV0eSBsaW1pdCBvZiAyMCBwYWdlc1xuICAgICAgICAgICAgY29uc29sZS5sb2coYEZldGNoaW5nIHBhZ2UgJHtjdXJyZW50UGFnZX0uLi5gKTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGZldGNoUGFnZShjdXJyZW50UGFnZSwgcGFnZVNpemUpO1xuXG4gICAgICAgICAgICBpZiAoIXJlc3VsdC5kYXRhIHx8ICFBcnJheS5pc0FycmF5KHJlc3VsdC5kYXRhKSkge1xuICAgICAgICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZlbG9wbWVudFwiKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgICAgICAgICAgICAgICAgIFwiSW52YWxpZCBkYXRhIGZvcm1hdCBpbiBwYWdlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50UGFnZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIFwiOlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0XG4gICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgZGF0YSBmb3JtYXQgcmVjZWl2ZWQgZnJvbSBzZXJ2ZXJcIik7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGFsbERvc3NpZXJzID0gWy4uLmFsbERvc3NpZXJzLCAuLi5yZXN1bHQuZGF0YV07XG4gICAgICAgICAgICB0b3RhbFBhZ2VzID0gcmVzdWx0LnBhZ2luYXRpb24udG90YWxQYWdlcztcbiAgICAgICAgICAgIGhhc01vcmVQYWdlcyA9XG4gICAgICAgICAgICAgICAgcmVzdWx0LnBhZ2luYXRpb24uaGFzTmV4dFBhZ2UgJiYgY3VycmVudFBhZ2UgPCB0b3RhbFBhZ2VzO1xuICAgICAgICAgICAgY3VycmVudFBhZ2UrKztcblxuICAgICAgICAgICAgLy8gSWYgd2UndmUgZmV0Y2hlZCBhbGwgcGFnZXMgb3IgcmVhY2hlZCB0aGUgc2FmZXR5IGxpbWl0LCBzdG9wXG4gICAgICAgICAgICBpZiAoIWhhc01vcmVQYWdlcyB8fCBjdXJyZW50UGFnZSA+IHRvdGFsUGFnZXMpIHtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgYEZldGNoZWQgJHthbGxEb3NzaWVycy5sZW5ndGh9IGRvc3NpZXJzIGZyb20gJHtcbiAgICAgICAgICAgICAgICBjdXJyZW50UGFnZSAtIDFcbiAgICAgICAgICAgIH0gcGFnZXNgXG4gICAgICAgICk7XG4gICAgICAgIHJldHVybiBhbGxEb3NzaWVycztcbiAgICB9IGNhdGNoIChlcnJvcjogdW5rbm93bikge1xuICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwiZGV2ZWxvcG1lbnRcIikge1xuICAgICAgICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXCJFcnJvciBpbiBnZXRDYXM6XCIsIHtcbiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogZXJyb3IubmFtZSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwiVW5rbm93biBlcnJvciBpbiBnZXRDYXM6XCIsIGVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG59XG4iXSwibmFtZXMiOlsiZmV0Y2hBcGkiLCJlbmRwb2ludCIsIm9wdGlvbnMiLCJtZXRob2QiLCJib2R5IiwicmVzcG9uc2UiLCJmZXRjaCIsImhlYWRlcnMiLCJjcmVkZW50aWFscyIsIkpTT04iLCJzdHJpbmdpZnkiLCJ1bmRlZmluZWQiLCJvayIsImVycm9yTWVzc2FnZSIsInN0YXR1cyIsImVycm9yRGF0YSIsImVycm9yVGV4dCIsInRleHQiLCJwYXJzZSIsImVycm9yIiwibWVzc2FnZSIsInBhcnNlRXJyb3IiLCJyZWFkRXJyb3IiLCJjb25zb2xlIiwid2FybiIsInByb2Nlc3MiLCJ1cmwiLCJzdGF0dXNUZXh0IiwiRXJyb3IiLCJqc29uIiwiYXBpQ2xpZW50IiwiZ2V0IiwicG9zdCIsInB1dCIsImRlbGV0ZSIsImdldENoYXQiLCJjYXNJZCIsInNlbmRNZXNzYWdlIiwiY29udGVudCIsImZldGNoUGFnZSIsInBhZ2UiLCJwYWdlU2l6ZSIsImdldENhcyIsImxvZyIsImN1cnJlbnRQYWdlIiwiYWxsRG9zc2llcnMiLCJoYXNNb3JlUGFnZXMiLCJ0b3RhbFBhZ2VzIiwicmVzdWx0IiwiZGF0YSIsIkFycmF5IiwiaXNBcnJheSIsInBhZ2luYXRpb24iLCJoYXNOZXh0UGFnZSIsImxlbmd0aCIsIm5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-client.ts\n"));

/***/ })

});