/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cas/route";
exports.ids = ["app/api/cas/route"];
exports.modules = {

/***/ "(rsc)/./app/api/cas/route.ts":
/*!******************************!*\
  !*** ./app/api/cas/route.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/permissions */ \"(rsc)/./lib/permissions.ts\");\n\n\n\n\n\n // Assuming this is your auth library\n\n\n// Get all cas - filtered by user role\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const encrageId = searchParams.get(\"encrageId\");\n        const problematiqueId = searchParams.get(\"problematiqueId\"); // Lire le problematiqueId\n        const withGeojson = searchParams.get(\"withGeojson\") === \"true\";\n        const regularisation = searchParams.get(\"regularisation\");\n        const casStatus = searchParams.get(\"casStatus\");\n        const wilayaId = searchParams.get(\"wilayaId\");\n        // Pagination parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const pageSize = parseInt(searchParams.get(\"pageSize\") || \"20\");\n        const search = searchParams.get(\"search\") || \"\";\n        // Validate pagination parameters\n        if (page < 1) return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Page must be greater than 0\"\n        }, {\n            status: 400\n        });\n        if (pageSize < 1 || pageSize > 1000) return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Page size must be between 1 and 1000\"\n        }, {\n            status: 400\n        });\n        // Get the current user from the token\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)(); // Await the cookies() call\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token); // Assuming verifyToken is async\n        if (!userPayload) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        // Build the where clause based on filters and user role\n        let where = {};\n        // Si problematiqueId est fourni, il a la priorité pour le filtrage direct des Cas\n        if (problematiqueId) {\n            where.problematiqueId = problematiqueId;\n        } else if (encrageId) {\n            // Sinon, si encrageId est fourni, filtre les Cas via l'encrage de leur problématique\n            where.problematique = {\n                encrageId: encrageId\n            };\n        }\n        // Filtrage par statut de régularisation\n        if (regularisation === \"true\") {\n            where.regularisation = true;\n        } else if (regularisation === \"false\") {\n            where.regularisation = false;\n        }\n        // Si withGeojson est true, ne retourner que les cas avec des coordonnées\n        if (withGeojson) {\n            where.geojson = {\n                not: null\n            };\n        }\n        // Search functionality\n        if (search) {\n            where.OR = [\n                {\n                    nom: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    nif: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    nin: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    communes: {\n                        some: {\n                            nom: {\n                                contains: search,\n                                mode: \"insensitive\"\n                            }\n                        }\n                    }\n                }\n            ];\n        }\n        // Filtrage par wilayaId\n        if (userPayload.role === \"BASIC\" || userPayload.role === \"EDITOR\") {\n            // Pour BASIC et EDITOR, filtrer par leur wilayaId uniquement\n            if (userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {\n                where.wilayaId = Number(userPayload.wilayaId);\n            }\n        } else if (userPayload.role === \"ADMIN\" || userPayload.role === \"VIEWER\") {\n            // Pour ADMIN et VIEWER, permettre le filtrage par wilayaId via paramètre\n            if (wilayaId && !isNaN(Number(wilayaId))) {\n                where.wilayaId = Number(wilayaId);\n            } else if (userPayload.role === \"VIEWER\" && userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {\n                where.wilayaId = Number(userPayload.wilayaId);\n            }\n        }\n        // Pour le filtre par statut, nous devons récupérer tous les cas d'abord\n        // car le statut dépend des résolutions de blocage\n        let needsStatusFiltering = casStatus && [\n            \"REGULARISE\",\n            \"AJOURNE\",\n            \"NON_EXAMINE\",\n            \"REJETE\"\n        ].includes(casStatus);\n        // Si pas de filtre par statut, on peut utiliser le count normal\n        let totalCount;\n        let totalPages;\n        let skip;\n        if (!needsStatusFiltering) {\n            totalCount = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                where\n            });\n            totalPages = Math.ceil(totalCount / pageSize);\n            skip = (page - 1) * pageSize;\n        } else {\n            // Si filtre par statut, on doit récupérer tous les cas pour les filtrer\n            skip = 0; // On récupère tout d'abord\n        }\n        let cas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.findMany({\n            where,\n            include: {\n                problematique: {\n                    include: {\n                        encrage: true\n                    }\n                },\n                user: {\n                    select: {\n                        id: true,\n                        username: true,\n                        role: true\n                    }\n                },\n                communes: true,\n                blocage: {\n                    select: {\n                        resolution: true\n                    }\n                }\n            },\n            skip: needsStatusFiltering ? 0 : skip,\n            take: needsStatusFiltering ? undefined : pageSize,\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        // Filtrage par statut de cas (après récupération car dépend des résolutions)\n        if (needsStatusFiltering) {\n            cas = cas.filter((c)=>{\n                const resolutions = c.blocage.map((b)=>b.resolution);\n                // Déterminer le statut réel du cas avec logique de priorité\n                let actualStatus;\n                if (resolutions.length === 0) {\n                    actualStatus = \"NON_EXAMINE\"; // Cas sans blocage\n                } else if (resolutions.every((r)=>r === \"ATTENTE\")) {\n                    actualStatus = \"NON_EXAMINE\"; // Tous en attente\n                } else if (resolutions.some((r)=>r === \"REJETE\")) {\n                    actualStatus = \"REJETE\"; // Au moins un rejeté (priorité la plus haute)\n                } else if (resolutions.some((r)=>r === \"AJOURNE\")) {\n                    actualStatus = \"AJOURNE\"; // Au moins un ajourné\n                } else if (resolutions.every((r)=>r === \"ACCEPTE\")) {\n                    actualStatus = \"REGULARISE\"; // Tous acceptés\n                } else {\n                    actualStatus = \"NON_EXAMINE\"; // Cas par défaut\n                }\n                // Filtrer selon le statut demandé\n                return actualStatus === casStatus;\n            });\n            // Maintenant calculer la pagination sur les données filtrées\n            totalCount = cas.length;\n            totalPages = Math.ceil(totalCount / pageSize);\n            // Appliquer la pagination sur les données filtrées\n            const startIndex = (page - 1) * pageSize;\n            cas = cas.slice(startIndex, startIndex + pageSize);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            data: cas,\n            pagination: {\n                page,\n                pageSize,\n                totalCount,\n                totalPages,\n                hasNextPage: page < totalPages,\n                hasPrevPage: page > 1\n            }\n        });\n    } catch (error) {\n        console.error(\"ERREUR API_CAS_GET:\", error); // Log détaillé de l'erreur côté serveur\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientKnownRequestError) {\n            // Erreurs connues de Prisma (ex: contrainte violée, enregistrement non trouvé)\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: `Erreur de base de données (Prisma): ${error.code}`,\n                message: error.message,\n                details: error.meta\n            }, {\n                status: 500\n            });\n        } else if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientValidationError) {\n            // Erreurs de validation de Prisma (ex: type de champ incorrect)\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Erreur de validation des données (Prisma).\",\n                message: error.message\n            }, {\n                status: 400\n            });\n        }\n        // Pour les autres types d'erreurs, utilisez le gestionnaire générique ou un message par défaut\n        // return handleError(error); // Si handleError est suffisant\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Erreur interne du serveur lors de la récupération des cas.\",\n            message: error instanceof Error ? error.message : \"Une erreur inconnue est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Create new cas\n// Mettre à jour le schéma Zod\nconst casSchema = zod__WEBPACK_IMPORTED_MODULE_7__.z.object({\n    nom: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().min(1, \"Le nom est requis.\"),\n    genre: zod__WEBPACK_IMPORTED_MODULE_7__.z.nativeEnum(_prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne, {\n        errorMap: (issue, ctx)=>{\n            if (issue.code === zod__WEBPACK_IMPORTED_MODULE_7__.z.ZodIssueCode.invalid_enum_value) {\n                return {\n                    message: \"La valeur du genre est invalide.\"\n                };\n            }\n            return {\n                message: ctx.defaultError\n            };\n        }\n    }),\n    nif: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional().nullable(),\n    nin: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional().nullable(),\n    superficie: zod__WEBPACK_IMPORTED_MODULE_7__.z.number().positive(\"La superficie doit être un nombre positif.\"),\n    // MODIFIÉ: regularisation est maintenant optionnel et défaut à false\n    regularisation: zod__WEBPACK_IMPORTED_MODULE_7__.z.boolean().optional().default(false),\n    observation: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional().nullable(),\n    problematiqueId: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().cuid(\"L'ID de la problématique est invalide.\"),\n    communeIds: zod__WEBPACK_IMPORTED_MODULE_7__.z.array(zod__WEBPACK_IMPORTED_MODULE_7__.z.string().regex(/^\\d+$/, \"Chaque ID de commune doit être une chaîne de chiffres.\")).min(1, \"Au moins une commune doit être sélectionnée.\"),\n    date_depot: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().datetime({\n        offset: true\n    }).optional().nullable()\n}).refine((data)=>{\n    if (data.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_MORALE) {\n        return !!data.nif; // NIF requis pour PERSONNE_MORALE\n    }\n    return true;\n}, {\n    message: \"Le NIF est requis et doit être valide pour une personne morale.\",\n    path: [\n        \"nif\"\n    ]\n}).refine((data)=>{\n    if (data.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_PHYSIQUE) {\n        return !!data.nin; // NIN requis pour PERSONNE_PHYSIQUE\n    }\n    return true;\n}, {\n    message: \"Le NIN est requis et doit être valide pour une personne physique.\",\n    path: [\n        \"nin\"\n    ]\n});\n// La transformation pour genre n'est plus nécessaire ici si le frontend envoie déjà les bonnes valeurs\n// et que z.nativeEnum(TypePersonne) est utilisé.\nasync function POST(request) {\n    try {\n        // Check write permissions using the new permission system\n        const { hasPermission, user, error } = await (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_6__.requireWritePermission)();\n        if (!hasPermission || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: error || \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const validation = casSchema.safeParse(body);\n        if (!validation.success) {\n            console.error(\"Validation errors:\", validation.error.flatten());\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(validation.error.flatten(), {\n                status: 400\n            });\n        }\n        // 'userId' est maintenant pris de userPayload.id\n        // 'regularisation' est maintenant inclus dans validation.data\n        const { nom, nif, nin, genre: genreString, date_depot, superficie, regularisation, observation, problematiqueId, communeIds } = validation.data;\n        // Utiliser user pour wilayaId - ADMIN peut créer des cas pour toutes les wilayas\n        // Note: wilayaId is required in the database, so we need to provide a valid value\n        if (!user.wilayaId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"WilayaId is required for all users\"\n            }, {\n                status: 400\n            });\n        }\n        const wilayaId = user.wilayaId;\n        const newCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.create({\n            data: {\n                nom,\n                nif: genreString === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_MORALE ? nif : null,\n                nin: genreString === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_PHYSIQUE ? nin : null,\n                genre: _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne[genreString],\n                date_depot: date_depot ? new Date(date_depot).toISOString() : null,\n                superficie,\n                regularisation,\n                observation,\n                problematiqueId,\n                userId: user.id,\n                wilayaId,\n                communes: {\n                    connect: communeIds.map((id)=>({\n                            id: parseInt(id)\n                        }))\n                }\n            },\n            include: {\n                problematique: true,\n                user: true,\n                communes: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(newCas, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"POST /api/cas error:\", error);\n        // Amélioration de la gestion des erreurs Prisma\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientKnownRequestError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                message: error.message,\n                code: error.code,\n                meta: error.meta\n            }, {\n                status: 400\n            });\n        }\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientValidationError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                message: error.message\n            }, {\n                status: 400\n            });\n        }\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/cas/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   forbidden: () => (/* binding */ forbidden),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   notFound: () => (/* binding */ notFound),\n/* harmony export */   unauthorized: () => (/* binding */ unauthorized),\n/* harmony export */   updateCasRegularisationStatus: () => (/* binding */ updateCasRegularisationStatus)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n // Importez Prisma pour typer les erreurs spécifiques\n\nasync function updateCasRegularisationStatus(casId) {\n    const relatedCasBlocages = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.blocage.findMany({\n        where: {\n            casId: casId\n        },\n        select: {\n            regularise: true\n        }\n    });\n    const allBlocagesRegularised = relatedCasBlocages.length > 0 && relatedCasBlocages.every((b)=>b.regularise);\n    await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.cas.update({\n        where: {\n            id: casId\n        },\n        data: {\n            regularisation: allBlocagesRegularised\n        }\n    });\n}\nfunction handleError(error) {\n    console.error(error); // Bon pour le débogage côté serveur\n    if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_1__.Prisma.PrismaClientKnownRequestError) {\n        // Erreurs connues de Prisma (contraintes uniques, etc.)\n        // Vous pouvez ajouter des codes d'erreur spécifiques ici si nécessaire\n        // Par exemple, P2002 pour violation de contrainte unique\n        if (error.code === \"P2002\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Une ressource avec ces identifiants existe déjà.\",\n                details: error.meta\n            }, {\n                status: 409\n            }); // Conflict\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur de base de données.\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n    if (error instanceof Error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Une erreur interne est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Une erreur inconnue est survenue.\"\n    }, {\n        status: 500\n    });\n}\nfunction forbidden(message = \"Accès interdit.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 403\n    });\n}\nfunction notFound(message = \"Ressource non trouvée.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 404\n    });\n}\nfunction unauthorized() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Non autorisé\"\n    }, {\n        status: 401\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/permissions.ts":
/*!****************************!*\
  !*** ./lib/permissions.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCurrentUserPermissions: () => (/* binding */ getCurrentUserPermissions),\n/* harmony export */   getPermissionsByRole: () => (/* binding */ getPermissionsByRole),\n/* harmony export */   requireDeletePermission: () => (/* binding */ requireDeletePermission),\n/* harmony export */   requireFileUploadPermission: () => (/* binding */ requireFileUploadPermission),\n/* harmony export */   requireMessagingPermission: () => (/* binding */ requireMessagingPermission),\n/* harmony export */   requireUserManagementPermission: () => (/* binding */ requireUserManagementPermission),\n/* harmony export */   requireWritePermission: () => (/* binding */ requireWritePermission)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./lib/auth.ts\");\n// Server-side permissions - only use in Server Components\n\n\n/**\n * Get user permissions based on their role\n */ function getPermissionsByRole(role) {\n    switch(role){\n        case \"ADMIN\":\n            return {\n                canRead: true,\n                canWrite: true,\n                canDelete: true,\n                canManageUsers: true,\n                canUploadFiles: true,\n                canSendMessages: true,\n                isReadOnly: false\n            };\n        case \"EDITOR\":\n            return {\n                canRead: true,\n                canWrite: true,\n                canDelete: true,\n                canManageUsers: false,\n                canUploadFiles: true,\n                canSendMessages: true,\n                isReadOnly: false\n            };\n        case \"BASIC\":\n            return {\n                canRead: true,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: true,\n                isReadOnly: true\n            };\n        case \"VIEWER\":\n            return {\n                canRead: true,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: false,\n                isReadOnly: true\n            };\n        default:\n            return {\n                canRead: false,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: false,\n                isReadOnly: true\n            };\n    }\n}\n/**\n * Get current user permissions from JWT token\n */ async function getCurrentUserPermissions() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return {\n                permissions: null,\n                user: null\n            };\n        }\n        const userPayload = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.verifyToken)(token);\n        if (!userPayload) {\n            return {\n                permissions: null,\n                user: null\n            };\n        }\n        const permissions = getPermissionsByRole(userPayload.role);\n        const user = {\n            id: userPayload.id,\n            role: userPayload.role,\n            username: userPayload.username,\n            email: userPayload.email,\n            wilayaId: userPayload.wilayaId\n        };\n        return {\n            permissions,\n            user\n        };\n    } catch (error) {\n        console.error(\"Error getting user permissions:\", error);\n        return {\n            permissions: null,\n            user: null\n        };\n    }\n}\n/**\n * Check if user has permission for write operations\n */ async function requireWritePermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canWrite) {\n        return {\n            hasPermission: false,\n            user,\n            error: user.role === \"VIEWER\" ? \"Read-only access: Write operations not permitted for VIEWER role\" : \"Insufficient permissions for write operations\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for delete operations\n */ async function requireDeletePermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canDelete) {\n        return {\n            hasPermission: false,\n            user,\n            error: user.role === \"VIEWER\" ? \"Read-only access: Delete operations not permitted for VIEWER role\" : \"Insufficient permissions for delete operations\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for user management\n */ async function requireUserManagementPermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canManageUsers) {\n        return {\n            hasPermission: false,\n            user,\n            error: \"Only ADMIN users can manage other users\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for file uploads\n */ async function requireFileUploadPermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canUploadFiles) {\n        return {\n            hasPermission: false,\n            user,\n            error: user.role === \"VIEWER\" ? \"Read-only access: File uploads not permitted for VIEWER role\" : \"Insufficient permissions for file uploads\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for sending messages\n */ async function requireMessagingPermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canSendMessages) {\n        return {\n            hasPermission: false,\n            user,\n            error: \"Read-only access: Messaging not permitted for VIEWER role\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcGVybWlzc2lvbnMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsMERBQTBEO0FBQ25CO0FBQ0Y7QUFjckM7O0NBRUMsR0FDTSxTQUFTRSxxQkFBcUJDLElBQWM7SUFDL0MsT0FBUUE7UUFDSixLQUFLO1lBQ0QsT0FBTztnQkFDSEMsU0FBUztnQkFDVEMsVUFBVTtnQkFDVkMsV0FBVztnQkFDWEMsZ0JBQWdCO2dCQUNoQkMsZ0JBQWdCO2dCQUNoQkMsaUJBQWlCO2dCQUNqQkMsWUFBWTtZQUNoQjtRQUNKLEtBQUs7WUFDRCxPQUFPO2dCQUNITixTQUFTO2dCQUNUQyxVQUFVO2dCQUNWQyxXQUFXO2dCQUNYQyxnQkFBZ0I7Z0JBQ2hCQyxnQkFBZ0I7Z0JBQ2hCQyxpQkFBaUI7Z0JBQ2pCQyxZQUFZO1lBQ2hCO1FBQ0osS0FBSztZQUNELE9BQU87Z0JBQ0hOLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1ZDLFdBQVc7Z0JBQ1hDLGdCQUFnQjtnQkFDaEJDLGdCQUFnQjtnQkFDaEJDLGlCQUFpQjtnQkFDakJDLFlBQVk7WUFDaEI7UUFDSixLQUFLO1lBQ0QsT0FBTztnQkFDSE4sU0FBUztnQkFDVEMsVUFBVTtnQkFDVkMsV0FBVztnQkFDWEMsZ0JBQWdCO2dCQUNoQkMsZ0JBQWdCO2dCQUNoQkMsaUJBQWlCO2dCQUNqQkMsWUFBWTtZQUNoQjtRQUNKO1lBQ0ksT0FBTztnQkFDSE4sU0FBUztnQkFDVEMsVUFBVTtnQkFDVkMsV0FBVztnQkFDWEMsZ0JBQWdCO2dCQUNoQkMsZ0JBQWdCO2dCQUNoQkMsaUJBQWlCO2dCQUNqQkMsWUFBWTtZQUNoQjtJQUNSO0FBQ0o7QUFFQTs7Q0FFQyxHQUNNLGVBQWVDO0lBVWxCLElBQUk7UUFDQSxNQUFNQyxjQUFjLE1BQU1aLHFEQUFPQTtRQUNqQyxNQUFNYSxRQUFRRCxZQUFZRSxHQUFHLENBQUMsVUFBVUM7UUFFeEMsSUFBSSxDQUFDRixPQUFPO1lBQ1IsT0FBTztnQkFBRUcsYUFBYTtnQkFBTUMsTUFBTTtZQUFLO1FBQzNDO1FBRUEsTUFBTUMsY0FBYyxNQUFNakIsa0RBQVdBLENBQUNZO1FBQ3RDLElBQUksQ0FBQ0ssYUFBYTtZQUNkLE9BQU87Z0JBQUVGLGFBQWE7Z0JBQU1DLE1BQU07WUFBSztRQUMzQztRQUVBLE1BQU1ELGNBQWNkLHFCQUFxQmdCLFlBQVlmLElBQUk7UUFDekQsTUFBTWMsT0FBTztZQUNURSxJQUFJRCxZQUFZQyxFQUFFO1lBQ2xCaEIsTUFBTWUsWUFBWWYsSUFBSTtZQUN0QmlCLFVBQVVGLFlBQVlFLFFBQVE7WUFDOUJDLE9BQU9ILFlBQVlHLEtBQUs7WUFDeEJDLFVBQVVKLFlBQVlJLFFBQVE7UUFDbEM7UUFFQSxPQUFPO1lBQUVOO1lBQWFDO1FBQUs7SUFDL0IsRUFBRSxPQUFPTSxPQUFPO1FBQ1pDLFFBQVFELEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ2pELE9BQU87WUFBRVAsYUFBYTtZQUFNQyxNQUFNO1FBQUs7SUFDM0M7QUFDSjtBQUVBOztDQUVDLEdBQ00sZUFBZVE7SUFXbEIsTUFBTSxFQUFFVCxXQUFXLEVBQUVDLElBQUksRUFBRSxHQUFHLE1BQU1OO0lBRXBDLElBQUksQ0FBQ0ssZUFBZSxDQUFDQyxNQUFNO1FBQ3ZCLE9BQU87WUFDSFMsZUFBZTtZQUNmVCxNQUFNO1lBQ05NLE9BQU87UUFDWDtJQUNKO0lBRUEsSUFBSSxDQUFDUCxZQUFZWCxRQUFRLEVBQUU7UUFDdkIsT0FBTztZQUNIcUIsZUFBZTtZQUNmVDtZQUNBTSxPQUNJTixLQUFLZCxJQUFJLEtBQUssV0FDUixxRUFDQTtRQUNkO0lBQ0o7SUFFQSxPQUFPO1FBQUV1QixlQUFlO1FBQU1UO0lBQUs7QUFDdkM7QUFFQTs7Q0FFQyxHQUNNLGVBQWVVO0lBVWxCLE1BQU0sRUFBRVgsV0FBVyxFQUFFQyxJQUFJLEVBQUUsR0FBRyxNQUFNTjtJQUVwQyxJQUFJLENBQUNLLGVBQWUsQ0FBQ0MsTUFBTTtRQUN2QixPQUFPO1lBQ0hTLGVBQWU7WUFDZlQsTUFBTTtZQUNOTSxPQUFPO1FBQ1g7SUFDSjtJQUVBLElBQUksQ0FBQ1AsWUFBWVYsU0FBUyxFQUFFO1FBQ3hCLE9BQU87WUFDSG9CLGVBQWU7WUFDZlQ7WUFDQU0sT0FDSU4sS0FBS2QsSUFBSSxLQUFLLFdBQ1Isc0VBQ0E7UUFDZDtJQUNKO0lBRUEsT0FBTztRQUFFdUIsZUFBZTtRQUFNVDtJQUFLO0FBQ3ZDO0FBRUE7O0NBRUMsR0FDTSxlQUFlVztJQVVsQixNQUFNLEVBQUVaLFdBQVcsRUFBRUMsSUFBSSxFQUFFLEdBQUcsTUFBTU47SUFFcEMsSUFBSSxDQUFDSyxlQUFlLENBQUNDLE1BQU07UUFDdkIsT0FBTztZQUNIUyxlQUFlO1lBQ2ZULE1BQU07WUFDTk0sT0FBTztRQUNYO0lBQ0o7SUFFQSxJQUFJLENBQUNQLFlBQVlULGNBQWMsRUFBRTtRQUM3QixPQUFPO1lBQ0htQixlQUFlO1lBQ2ZUO1lBQ0FNLE9BQU87UUFDWDtJQUNKO0lBRUEsT0FBTztRQUFFRyxlQUFlO1FBQU1UO0lBQUs7QUFDdkM7QUFFQTs7Q0FFQyxHQUNNLGVBQWVZO0lBVWxCLE1BQU0sRUFBRWIsV0FBVyxFQUFFQyxJQUFJLEVBQUUsR0FBRyxNQUFNTjtJQUVwQyxJQUFJLENBQUNLLGVBQWUsQ0FBQ0MsTUFBTTtRQUN2QixPQUFPO1lBQ0hTLGVBQWU7WUFDZlQsTUFBTTtZQUNOTSxPQUFPO1FBQ1g7SUFDSjtJQUVBLElBQUksQ0FBQ1AsWUFBWVIsY0FBYyxFQUFFO1FBQzdCLE9BQU87WUFDSGtCLGVBQWU7WUFDZlQ7WUFDQU0sT0FDSU4sS0FBS2QsSUFBSSxLQUFLLFdBQ1IsaUVBQ0E7UUFDZDtJQUNKO0lBRUEsT0FBTztRQUFFdUIsZUFBZTtRQUFNVDtJQUFLO0FBQ3ZDO0FBRUE7O0NBRUMsR0FDTSxlQUFlYTtJQVVsQixNQUFNLEVBQUVkLFdBQVcsRUFBRUMsSUFBSSxFQUFFLEdBQUcsTUFBTU47SUFFcEMsSUFBSSxDQUFDSyxlQUFlLENBQUNDLE1BQU07UUFDdkIsT0FBTztZQUNIUyxlQUFlO1lBQ2ZULE1BQU07WUFDTk0sT0FBTztRQUNYO0lBQ0o7SUFFQSxJQUFJLENBQUNQLFlBQVlQLGVBQWUsRUFBRTtRQUM5QixPQUFPO1lBQ0hpQixlQUFlO1lBQ2ZUO1lBQ0FNLE9BQU87UUFDWDtJQUNKO0lBRUEsT0FBTztRQUFFRyxlQUFlO1FBQU1UO0lBQUs7QUFDdkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwZXJtaXNzaW9ucy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTZXJ2ZXItc2lkZSBwZXJtaXNzaW9ucyAtIG9ubHkgdXNlIGluIFNlcnZlciBDb21wb25lbnRzXG5pbXBvcnQgeyBjb29raWVzIH0gZnJvbSBcIm5leHQvaGVhZGVyc1wiO1xuaW1wb3J0IHsgdmVyaWZ5VG9rZW4gfSBmcm9tIFwiLi9hdXRoXCI7XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlclBlcm1pc3Npb25zIHtcbiAgICBjYW5SZWFkOiBib29sZWFuO1xuICAgIGNhbldyaXRlOiBib29sZWFuO1xuICAgIGNhbkRlbGV0ZTogYm9vbGVhbjtcbiAgICBjYW5NYW5hZ2VVc2VyczogYm9vbGVhbjtcbiAgICBjYW5VcGxvYWRGaWxlczogYm9vbGVhbjtcbiAgICBjYW5TZW5kTWVzc2FnZXM6IGJvb2xlYW47XG4gICAgaXNSZWFkT25seTogYm9vbGVhbjtcbn1cblxuZXhwb3J0IHR5cGUgVXNlclJvbGUgPSBcIkFETUlOXCIgfCBcIkVESVRPUlwiIHwgXCJCQVNJQ1wiIHwgXCJWSUVXRVJcIjtcblxuLyoqXG4gKiBHZXQgdXNlciBwZXJtaXNzaW9ucyBiYXNlZCBvbiB0aGVpciByb2xlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRQZXJtaXNzaW9uc0J5Um9sZShyb2xlOiBVc2VyUm9sZSk6IFVzZXJQZXJtaXNzaW9ucyB7XG4gICAgc3dpdGNoIChyb2xlKSB7XG4gICAgICAgIGNhc2UgXCJBRE1JTlwiOlxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBjYW5SZWFkOiB0cnVlLFxuICAgICAgICAgICAgICAgIGNhbldyaXRlOiB0cnVlLFxuICAgICAgICAgICAgICAgIGNhbkRlbGV0ZTogdHJ1ZSxcbiAgICAgICAgICAgICAgICBjYW5NYW5hZ2VVc2VyczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBjYW5VcGxvYWRGaWxlczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBjYW5TZW5kTWVzc2FnZXM6IHRydWUsXG4gICAgICAgICAgICAgICAgaXNSZWFkT25seTogZmFsc2UsXG4gICAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwiRURJVE9SXCI6XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGNhblJlYWQ6IHRydWUsXG4gICAgICAgICAgICAgICAgY2FuV3JpdGU6IHRydWUsXG4gICAgICAgICAgICAgICAgY2FuRGVsZXRlOiB0cnVlLFxuICAgICAgICAgICAgICAgIGNhbk1hbmFnZVVzZXJzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBjYW5VcGxvYWRGaWxlczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBjYW5TZW5kTWVzc2FnZXM6IHRydWUsXG4gICAgICAgICAgICAgICAgaXNSZWFkT25seTogZmFsc2UsXG4gICAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwiQkFTSUNcIjpcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgY2FuUmVhZDogdHJ1ZSxcbiAgICAgICAgICAgICAgICBjYW5Xcml0ZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgY2FuRGVsZXRlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBjYW5NYW5hZ2VVc2VyczogZmFsc2UsXG4gICAgICAgICAgICAgICAgY2FuVXBsb2FkRmlsZXM6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGNhblNlbmRNZXNzYWdlczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBpc1JlYWRPbmx5OiB0cnVlLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcIlZJRVdFUlwiOlxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBjYW5SZWFkOiB0cnVlLFxuICAgICAgICAgICAgICAgIGNhbldyaXRlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBjYW5EZWxldGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGNhbk1hbmFnZVVzZXJzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBjYW5VcGxvYWRGaWxlczogZmFsc2UsXG4gICAgICAgICAgICAgICAgY2FuU2VuZE1lc3NhZ2VzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBpc1JlYWRPbmx5OiB0cnVlLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgY2FuUmVhZDogZmFsc2UsXG4gICAgICAgICAgICAgICAgY2FuV3JpdGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGNhbkRlbGV0ZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgY2FuTWFuYWdlVXNlcnM6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGNhblVwbG9hZEZpbGVzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBjYW5TZW5kTWVzc2FnZXM6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGlzUmVhZE9ubHk6IHRydWUsXG4gICAgICAgICAgICB9O1xuICAgIH1cbn1cblxuLyoqXG4gKiBHZXQgY3VycmVudCB1c2VyIHBlcm1pc3Npb25zIGZyb20gSldUIHRva2VuXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRDdXJyZW50VXNlclBlcm1pc3Npb25zKCk6IFByb21pc2U8e1xuICAgIHBlcm1pc3Npb25zOiBVc2VyUGVybWlzc2lvbnMgfCBudWxsO1xuICAgIHVzZXI6IHtcbiAgICAgICAgaWQ6IHN0cmluZztcbiAgICAgICAgcm9sZTogVXNlclJvbGU7XG4gICAgICAgIHVzZXJuYW1lOiBzdHJpbmc7XG4gICAgICAgIGVtYWlsOiBzdHJpbmc7XG4gICAgICAgIHdpbGF5YUlkPzogbnVtYmVyO1xuICAgIH0gfCBudWxsO1xufT4ge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGNvb2tpZVN0b3JlID0gYXdhaXQgY29va2llcygpO1xuICAgICAgICBjb25zdCB0b2tlbiA9IGNvb2tpZVN0b3JlLmdldChcInRva2VuXCIpPy52YWx1ZTtcblxuICAgICAgICBpZiAoIXRva2VuKSB7XG4gICAgICAgICAgICByZXR1cm4geyBwZXJtaXNzaW9uczogbnVsbCwgdXNlcjogbnVsbCB9O1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgdXNlclBheWxvYWQgPSBhd2FpdCB2ZXJpZnlUb2tlbih0b2tlbik7XG4gICAgICAgIGlmICghdXNlclBheWxvYWQpIHtcbiAgICAgICAgICAgIHJldHVybiB7IHBlcm1pc3Npb25zOiBudWxsLCB1c2VyOiBudWxsIH07XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBwZXJtaXNzaW9ucyA9IGdldFBlcm1pc3Npb25zQnlSb2xlKHVzZXJQYXlsb2FkLnJvbGUgYXMgVXNlclJvbGUpO1xuICAgICAgICBjb25zdCB1c2VyID0ge1xuICAgICAgICAgICAgaWQ6IHVzZXJQYXlsb2FkLmlkLFxuICAgICAgICAgICAgcm9sZTogdXNlclBheWxvYWQucm9sZSBhcyBVc2VyUm9sZSxcbiAgICAgICAgICAgIHVzZXJuYW1lOiB1c2VyUGF5bG9hZC51c2VybmFtZSxcbiAgICAgICAgICAgIGVtYWlsOiB1c2VyUGF5bG9hZC5lbWFpbCxcbiAgICAgICAgICAgIHdpbGF5YUlkOiB1c2VyUGF5bG9hZC53aWxheWFJZCxcbiAgICAgICAgfTtcblxuICAgICAgICByZXR1cm4geyBwZXJtaXNzaW9ucywgdXNlciB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZXR0aW5nIHVzZXIgcGVybWlzc2lvbnM6XCIsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIHsgcGVybWlzc2lvbnM6IG51bGwsIHVzZXI6IG51bGwgfTtcbiAgICB9XG59XG5cbi8qKlxuICogQ2hlY2sgaWYgdXNlciBoYXMgcGVybWlzc2lvbiBmb3Igd3JpdGUgb3BlcmF0aW9uc1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVxdWlyZVdyaXRlUGVybWlzc2lvbigpOiBQcm9taXNlPHtcbiAgICBoYXNQZXJtaXNzaW9uOiBib29sZWFuO1xuICAgIHVzZXI6IHtcbiAgICAgICAgaWQ6IHN0cmluZztcbiAgICAgICAgcm9sZTogVXNlclJvbGU7XG4gICAgICAgIHVzZXJuYW1lOiBzdHJpbmc7XG4gICAgICAgIGVtYWlsOiBzdHJpbmc7XG4gICAgICAgIHdpbGF5YUlkPzogbnVtYmVyO1xuICAgIH0gfCBudWxsO1xuICAgIGVycm9yPzogc3RyaW5nO1xufT4ge1xuICAgIGNvbnN0IHsgcGVybWlzc2lvbnMsIHVzZXIgfSA9IGF3YWl0IGdldEN1cnJlbnRVc2VyUGVybWlzc2lvbnMoKTtcblxuICAgIGlmICghcGVybWlzc2lvbnMgfHwgIXVzZXIpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGhhc1Blcm1pc3Npb246IGZhbHNlLFxuICAgICAgICAgICAgdXNlcjogbnVsbCxcbiAgICAgICAgICAgIGVycm9yOiBcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIsXG4gICAgICAgIH07XG4gICAgfVxuXG4gICAgaWYgKCFwZXJtaXNzaW9ucy5jYW5Xcml0ZSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgaGFzUGVybWlzc2lvbjogZmFsc2UsXG4gICAgICAgICAgICB1c2VyLFxuICAgICAgICAgICAgZXJyb3I6XG4gICAgICAgICAgICAgICAgdXNlci5yb2xlID09PSBcIlZJRVdFUlwiXG4gICAgICAgICAgICAgICAgICAgID8gXCJSZWFkLW9ubHkgYWNjZXNzOiBXcml0ZSBvcGVyYXRpb25zIG5vdCBwZXJtaXR0ZWQgZm9yIFZJRVdFUiByb2xlXCJcbiAgICAgICAgICAgICAgICAgICAgOiBcIkluc3VmZmljaWVudCBwZXJtaXNzaW9ucyBmb3Igd3JpdGUgb3BlcmF0aW9uc1wiLFxuICAgICAgICB9O1xuICAgIH1cblxuICAgIHJldHVybiB7IGhhc1Blcm1pc3Npb246IHRydWUsIHVzZXIgfTtcbn1cblxuLyoqXG4gKiBDaGVjayBpZiB1c2VyIGhhcyBwZXJtaXNzaW9uIGZvciBkZWxldGUgb3BlcmF0aW9uc1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVxdWlyZURlbGV0ZVBlcm1pc3Npb24oKTogUHJvbWlzZTx7XG4gICAgaGFzUGVybWlzc2lvbjogYm9vbGVhbjtcbiAgICB1c2VyOiB7XG4gICAgICAgIGlkOiBzdHJpbmc7XG4gICAgICAgIHJvbGU6IFVzZXJSb2xlO1xuICAgICAgICB1c2VybmFtZTogc3RyaW5nO1xuICAgICAgICBlbWFpbDogc3RyaW5nO1xuICAgIH0gfCBudWxsO1xuICAgIGVycm9yPzogc3RyaW5nO1xufT4ge1xuICAgIGNvbnN0IHsgcGVybWlzc2lvbnMsIHVzZXIgfSA9IGF3YWl0IGdldEN1cnJlbnRVc2VyUGVybWlzc2lvbnMoKTtcblxuICAgIGlmICghcGVybWlzc2lvbnMgfHwgIXVzZXIpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGhhc1Blcm1pc3Npb246IGZhbHNlLFxuICAgICAgICAgICAgdXNlcjogbnVsbCxcbiAgICAgICAgICAgIGVycm9yOiBcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIsXG4gICAgICAgIH07XG4gICAgfVxuXG4gICAgaWYgKCFwZXJtaXNzaW9ucy5jYW5EZWxldGUpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGhhc1Blcm1pc3Npb246IGZhbHNlLFxuICAgICAgICAgICAgdXNlcixcbiAgICAgICAgICAgIGVycm9yOlxuICAgICAgICAgICAgICAgIHVzZXIucm9sZSA9PT0gXCJWSUVXRVJcIlxuICAgICAgICAgICAgICAgICAgICA/IFwiUmVhZC1vbmx5IGFjY2VzczogRGVsZXRlIG9wZXJhdGlvbnMgbm90IHBlcm1pdHRlZCBmb3IgVklFV0VSIHJvbGVcIlxuICAgICAgICAgICAgICAgICAgICA6IFwiSW5zdWZmaWNpZW50IHBlcm1pc3Npb25zIGZvciBkZWxldGUgb3BlcmF0aW9uc1wiLFxuICAgICAgICB9O1xuICAgIH1cblxuICAgIHJldHVybiB7IGhhc1Blcm1pc3Npb246IHRydWUsIHVzZXIgfTtcbn1cblxuLyoqXG4gKiBDaGVjayBpZiB1c2VyIGhhcyBwZXJtaXNzaW9uIGZvciB1c2VyIG1hbmFnZW1lbnRcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJlcXVpcmVVc2VyTWFuYWdlbWVudFBlcm1pc3Npb24oKTogUHJvbWlzZTx7XG4gICAgaGFzUGVybWlzc2lvbjogYm9vbGVhbjtcbiAgICB1c2VyOiB7XG4gICAgICAgIGlkOiBzdHJpbmc7XG4gICAgICAgIHJvbGU6IFVzZXJSb2xlO1xuICAgICAgICB1c2VybmFtZTogc3RyaW5nO1xuICAgICAgICBlbWFpbDogc3RyaW5nO1xuICAgIH0gfCBudWxsO1xuICAgIGVycm9yPzogc3RyaW5nO1xufT4ge1xuICAgIGNvbnN0IHsgcGVybWlzc2lvbnMsIHVzZXIgfSA9IGF3YWl0IGdldEN1cnJlbnRVc2VyUGVybWlzc2lvbnMoKTtcblxuICAgIGlmICghcGVybWlzc2lvbnMgfHwgIXVzZXIpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGhhc1Blcm1pc3Npb246IGZhbHNlLFxuICAgICAgICAgICAgdXNlcjogbnVsbCxcbiAgICAgICAgICAgIGVycm9yOiBcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIsXG4gICAgICAgIH07XG4gICAgfVxuXG4gICAgaWYgKCFwZXJtaXNzaW9ucy5jYW5NYW5hZ2VVc2Vycykge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgaGFzUGVybWlzc2lvbjogZmFsc2UsXG4gICAgICAgICAgICB1c2VyLFxuICAgICAgICAgICAgZXJyb3I6IFwiT25seSBBRE1JTiB1c2VycyBjYW4gbWFuYWdlIG90aGVyIHVzZXJzXCIsXG4gICAgICAgIH07XG4gICAgfVxuXG4gICAgcmV0dXJuIHsgaGFzUGVybWlzc2lvbjogdHJ1ZSwgdXNlciB9O1xufVxuXG4vKipcbiAqIENoZWNrIGlmIHVzZXIgaGFzIHBlcm1pc3Npb24gZm9yIGZpbGUgdXBsb2Fkc1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVxdWlyZUZpbGVVcGxvYWRQZXJtaXNzaW9uKCk6IFByb21pc2U8e1xuICAgIGhhc1Blcm1pc3Npb246IGJvb2xlYW47XG4gICAgdXNlcjoge1xuICAgICAgICBpZDogc3RyaW5nO1xuICAgICAgICByb2xlOiBVc2VyUm9sZTtcbiAgICAgICAgdXNlcm5hbWU6IHN0cmluZztcbiAgICAgICAgZW1haWw6IHN0cmluZztcbiAgICB9IHwgbnVsbDtcbiAgICBlcnJvcj86IHN0cmluZztcbn0+IHtcbiAgICBjb25zdCB7IHBlcm1pc3Npb25zLCB1c2VyIH0gPSBhd2FpdCBnZXRDdXJyZW50VXNlclBlcm1pc3Npb25zKCk7XG5cbiAgICBpZiAoIXBlcm1pc3Npb25zIHx8ICF1c2VyKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBoYXNQZXJtaXNzaW9uOiBmYWxzZSxcbiAgICAgICAgICAgIHVzZXI6IG51bGwsXG4gICAgICAgICAgICBlcnJvcjogXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZFwiLFxuICAgICAgICB9O1xuICAgIH1cblxuICAgIGlmICghcGVybWlzc2lvbnMuY2FuVXBsb2FkRmlsZXMpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGhhc1Blcm1pc3Npb246IGZhbHNlLFxuICAgICAgICAgICAgdXNlcixcbiAgICAgICAgICAgIGVycm9yOlxuICAgICAgICAgICAgICAgIHVzZXIucm9sZSA9PT0gXCJWSUVXRVJcIlxuICAgICAgICAgICAgICAgICAgICA/IFwiUmVhZC1vbmx5IGFjY2VzczogRmlsZSB1cGxvYWRzIG5vdCBwZXJtaXR0ZWQgZm9yIFZJRVdFUiByb2xlXCJcbiAgICAgICAgICAgICAgICAgICAgOiBcIkluc3VmZmljaWVudCBwZXJtaXNzaW9ucyBmb3IgZmlsZSB1cGxvYWRzXCIsXG4gICAgICAgIH07XG4gICAgfVxuXG4gICAgcmV0dXJuIHsgaGFzUGVybWlzc2lvbjogdHJ1ZSwgdXNlciB9O1xufVxuXG4vKipcbiAqIENoZWNrIGlmIHVzZXIgaGFzIHBlcm1pc3Npb24gZm9yIHNlbmRpbmcgbWVzc2FnZXNcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJlcXVpcmVNZXNzYWdpbmdQZXJtaXNzaW9uKCk6IFByb21pc2U8e1xuICAgIGhhc1Blcm1pc3Npb246IGJvb2xlYW47XG4gICAgdXNlcjoge1xuICAgICAgICBpZDogc3RyaW5nO1xuICAgICAgICByb2xlOiBVc2VyUm9sZTtcbiAgICAgICAgdXNlcm5hbWU6IHN0cmluZztcbiAgICAgICAgZW1haWw6IHN0cmluZztcbiAgICB9IHwgbnVsbDtcbiAgICBlcnJvcj86IHN0cmluZztcbn0+IHtcbiAgICBjb25zdCB7IHBlcm1pc3Npb25zLCB1c2VyIH0gPSBhd2FpdCBnZXRDdXJyZW50VXNlclBlcm1pc3Npb25zKCk7XG5cbiAgICBpZiAoIXBlcm1pc3Npb25zIHx8ICF1c2VyKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBoYXNQZXJtaXNzaW9uOiBmYWxzZSxcbiAgICAgICAgICAgIHVzZXI6IG51bGwsXG4gICAgICAgICAgICBlcnJvcjogXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZFwiLFxuICAgICAgICB9O1xuICAgIH1cblxuICAgIGlmICghcGVybWlzc2lvbnMuY2FuU2VuZE1lc3NhZ2VzKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBoYXNQZXJtaXNzaW9uOiBmYWxzZSxcbiAgICAgICAgICAgIHVzZXIsXG4gICAgICAgICAgICBlcnJvcjogXCJSZWFkLW9ubHkgYWNjZXNzOiBNZXNzYWdpbmcgbm90IHBlcm1pdHRlZCBmb3IgVklFV0VSIHJvbGVcIixcbiAgICAgICAgfTtcbiAgICB9XG5cbiAgICByZXR1cm4geyBoYXNQZXJtaXNzaW9uOiB0cnVlLCB1c2VyIH07XG59XG4iXSwibmFtZXMiOlsiY29va2llcyIsInZlcmlmeVRva2VuIiwiZ2V0UGVybWlzc2lvbnNCeVJvbGUiLCJyb2xlIiwiY2FuUmVhZCIsImNhbldyaXRlIiwiY2FuRGVsZXRlIiwiY2FuTWFuYWdlVXNlcnMiLCJjYW5VcGxvYWRGaWxlcyIsImNhblNlbmRNZXNzYWdlcyIsImlzUmVhZE9ubHkiLCJnZXRDdXJyZW50VXNlclBlcm1pc3Npb25zIiwiY29va2llU3RvcmUiLCJ0b2tlbiIsImdldCIsInZhbHVlIiwicGVybWlzc2lvbnMiLCJ1c2VyIiwidXNlclBheWxvYWQiLCJpZCIsInVzZXJuYW1lIiwiZW1haWwiLCJ3aWxheWFJZCIsImVycm9yIiwiY29uc29sZSIsInJlcXVpcmVXcml0ZVBlcm1pc3Npb24iLCJoYXNQZXJtaXNzaW9uIiwicmVxdWlyZURlbGV0ZVBlcm1pc3Npb24iLCJyZXF1aXJlVXNlck1hbmFnZW1lbnRQZXJtaXNzaW9uIiwicmVxdWlyZUZpbGVVcGxvYWRQZXJtaXNzaW9uIiwicmVxdWlyZU1lc3NhZ2luZ1Blcm1pc3Npb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/permissions.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/cas/route.ts */ \"(rsc)/./app/api/cas/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cas/route\",\n        pathname: \"/api/cas\",\n        filename: \"route\",\n        bundlePath: \"app/api/cas/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\cas\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();