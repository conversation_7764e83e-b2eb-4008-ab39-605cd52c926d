{"/api/auth/me/route": "app/api/auth/me/route.js", "/api/encrages/route": "app/api/encrages/route.js", "/api/stats/resolution/route": "app/api/stats/resolution/route.js", "/api/stats/encrages/route": "app/api/stats/encrages/route.js", "/api/stats/cas/route": "app/api/stats/cas/route.js", "/api/communes/route": "app/api/communes/route.js", "/api/secteurs/route": "app/api/secteurs/route.js", "/api/problematiques/route": "app/api/problematiques/route.js", "/api/cas/[id]/route": "app/api/cas/[id]/route.js", "/api/cas/[id]/blocages/route": "app/api/cas/[id]/blocages/route.js", "/page": "app/page.js", "/dashboard/page": "app/dashboard/page.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/dashboard/cas/page": "app/dashboard/cas/page.js", "/dashboard/cas/[id]/page": "app/dashboard/cas/[id]/page.js"}