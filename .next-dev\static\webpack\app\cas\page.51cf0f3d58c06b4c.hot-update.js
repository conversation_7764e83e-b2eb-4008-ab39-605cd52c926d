"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/cas/page",{

/***/ "(app-pages-browser)/./app/cas/page.tsx":
/*!**************************!*\
  !*** ./app/cas/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CasPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Button */ \"(app-pages-browser)/./app/components/Button.tsx\");\n/* harmony import */ var _components_Input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Input */ \"(app-pages-browser)/./app/components/Input.tsx\");\n/* harmony import */ var _components_TextArea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/TextArea */ \"(app-pages-browser)/./app/components/TextArea.tsx\");\n/* harmony import */ var _components_Table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Table */ \"(app-pages-browser)/./app/components/Table.tsx\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/Modal */ \"(app-pages-browser)/./app/components/Modal.tsx\");\n/* harmony import */ var _components_FormError__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/FormError */ \"(app-pages-browser)/./app/components/FormError.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/CasStatusBadge */ \"(app-pages-browser)/./app/components/CasStatusBadge.tsx\");\n/* harmony import */ var _components_ExportExcelButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/ExportExcelButton */ \"(app-pages-browser)/./app/components/ExportExcelButton.tsx\");\n/* harmony import */ var _components_ExportBatchButton__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/ExportBatchButton */ \"(app-pages-browser)/./app/components/ExportBatchButton.tsx\");\n/* harmony import */ var _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/contexts/DataRefreshContext */ \"(app-pages-browser)/./app/contexts/DataRefreshContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,DocumentTextIcon,EyeIcon,PlusCircleIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,DocumentTextIcon,EyeIcon,PlusCircleIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,DocumentTextIcon,EyeIcon,PlusCircleIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,DocumentTextIcon,EyeIcon,PlusCircleIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,DocumentTextIcon,EyeIcon,PlusCircleIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/RoleBasedAccess */ \"(app-pages-browser)/./app/components/RoleBasedAccess.tsx\");\n/* harmony import */ var _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/hooks/usePermissions */ \"(app-pages-browser)/./lib/hooks/usePermissions.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @prisma/client */ \"(app-pages-browser)/./node_modules/@prisma/client/index-browser.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var _components_Select__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../components/Select */ \"(app-pages-browser)/./app/components/Select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n // Ajout de useSearchParams\n\n\n\n\n\n\n\n// Simple component for viewer read-only message\nfunction ViewerReadOnlyMessage() {\n    _s();\n    const { isViewer } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_15__.usePermissions)();\n    if (!isViewer) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.ReadOnlyMessage, {\n        message: \"Vous \\xeates en mode lecture seule. Vous pouvez consulter les dossiers mais ne pouvez pas les modifier.\",\n        className: \"mb-4\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 9\n    }, this);\n}\n_s(ViewerReadOnlyMessage, \"xvSwneBM+L5zDDX0qQs9MsT5OW0=\", false, function() {\n    return [\n        _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_15__.usePermissions\n    ];\n});\n_c = ViewerReadOnlyMessage;\n // Assurez-vous que l'import est correct\n //\n// Correction suggérée pour formatNinInput (et similaire pour formatNifInput)\n// pour gérer le problème de la variable 'name' non définie.\n// Vous devrez décider comment identifier si c'est un NIF ou NIN.\n// Une solution est de créer deux fonctions distinctes ou de passer un type.\nfunction formatGenericNumericInput(value, type) {\n    const maxLength = type === \"nin\" ? 20 : 15;\n    const rawDigits = value.replace(/\\D/g, \"\").substring(0, maxLength);\n    if (!rawDigits) return \"\";\n    let resultFormatted = \"\";\n    if (type === \"nin\") {\n        for(let j = 0; j < rawDigits.length; j++){\n            resultFormatted += rawDigits[j];\n            if ((j + 1) % 3 === 0 && j < 17 && j + 1 < rawDigits.length) {\n                resultFormatted += \".\";\n            } else if (j + 1 === 18 && j + 1 < rawDigits.length) {\n                resultFormatted += \".\";\n            }\n        }\n    } else {\n        // nif\n        for(let i = 0; i < rawDigits.length; i++){\n            resultFormatted += rawDigits[i];\n            if ((i + 1) % 3 === 0 && i + 1 < rawDigits.length) {\n                // Éviter le point final pour NIF\n                resultFormatted += \".\";\n            }\n        }\n    }\n    return resultFormatted;\n}\nfunction CasPage() {\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useSearchParams)(); // Récupérer les searchParams\n    const problematiqueIdFromUrl = searchParams.get(\"problematiqueId\"); // Extraire problematiqueId\n    const encrageIdFromUrl = searchParams.get(\"encrageId\"); // Extraire encrageId\n    // Hooks pour la gestion des rafraîchissements\n    const { afterCreate, afterUpdate, afterDelete } = (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useOperationRefresh)();\n    // Hook pour les permissions\n    const { isAdmin, isViewer } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_15__.usePermissions)();\n    const [cas, setCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [problematiques, setProblematiques] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [encrages, setEncrages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allCommunesData, setAllCommunesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Pour stocker les communes de communes.json\n    const [currentEncrageName, setCurrentEncrageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // NOUVELLE LIGNE: État pour le nom de l'encrage actuel\n    const [availableCommunesForForm, setAvailableCommunesForForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Communes filtrées pour le formulaire\n    const [selectedEncrageId, setSelectedEncrageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredProblematiques, setFilteredProblematiques] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentCas, setCurrentCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nom: \"\",\n        genre: \"\",\n        nif: \"\",\n        nin: \"\",\n        superficie: \"\",\n        observation: \"\",\n        problematiqueId: \"\",\n        communeIds: [],\n        date_depot: \"\",\n        regularisation: false,\n        userId: \"\"\n    });\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [casToDelete, setCasToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    function openModal() {\n        setIsModalOpen(true);\n    }\n    function closeModal() {\n        setIsModalOpen(false);\n        setIsEditing(false);\n        setCurrentCas(null);\n        // Réinitialiser formData aux valeurs par défaut\n        setFormData({\n            nom: \"\",\n            genre: \"\",\n            nif: \"\",\n            nin: \"\",\n            superficie: \"\",\n            observation: \"\",\n            problematiqueId: selectedEncrageId && filteredProblematiques.length > 0 ? formData.problematiqueId : \"\",\n            communeIds: [],\n            date_depot: \"\",\n            regularisation: false,\n            userId: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id) || \"\"\n        });\n        setError(\"\"); // Effacer les erreurs précédentes lors de la fermeture de la modale\n    }\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            // Charger les données des communes depuis l'API\n            async function loadCommunesData() {\n                try {\n                    // const response = await fetch(\"/data/communes.json\"); // ANCIENNE MÉTHODE\n                    // if (!response.ok) {\n                    //     throw new Error(`HTTP error! status: ${response.status}`);\n                    // }\n                    // const data: CommuneData[] = await response.json();\n                    const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/communes\"); // NOUVELLE MÉTHODE\n                    setAllCommunesData(data || []); // fetchApi peut retourner null\n                } catch (error) {\n                    console.error(\"Error fetching communes from API:\", error); // Message de log mis à jour\n                    setError(\"Erreur lors du chargement des communes.\");\n                }\n            }\n            loadCommunesData();\n        }\n    }[\"CasPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n            loadProblematiques();\n            loadEncrages();\n        }\n    }[\"CasPage.useEffect\"], [\n        problematiqueIdFromUrl,\n        encrageIdFromUrl\n    ]);\n    // Enregistrer les callbacks de rafraîchissement\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh)(\"cas-list\", {\n        \"CasPage.useRegisterDataRefresh\": ()=>loadCas(problematiqueIdFromUrl, encrageIdFromUrl)\n    }[\"CasPage.useRegisterDataRefresh\"], [\n        problematiqueIdFromUrl,\n        encrageIdFromUrl\n    ]);\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh)(\"encrages-list\", loadEncrages, []);\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh)(\"problematiques-list\", loadProblematiques, []);\n    // NOUVEAU BLOC: useEffect pour définir le nom de l'encrage actuel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            if (encrageIdFromUrl && encrages.length > 0) {\n                const foundEncrage = encrages.find({\n                    \"CasPage.useEffect.foundEncrage\": (enc)=>enc.id === encrageIdFromUrl\n                }[\"CasPage.useEffect.foundEncrage\"]);\n                if (foundEncrage) {\n                    setCurrentEncrageName(foundEncrage.nom);\n                } else {\n                    setCurrentEncrageName(\"\"); // Réinitialiser si non trouvé\n                }\n            } else {\n                setCurrentEncrageName(\"\"); // Réinitialiser si pas d'ID ou pas d'encrages chargés\n            }\n        }\n    }[\"CasPage.useEffect\"], [\n        encrageIdFromUrl,\n        encrages\n    ]);\n    // Filtrer les communes disponibles pour le formulaire en fonction du rôle et wilayaId de l'utilisateur\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            if (currentUser && allCommunesData.length > 0) {\n                if (currentUser.role === \"ADMIN\" || !currentUser.wilayaId) {\n                    setAvailableCommunesForForm(allCommunesData);\n                } else {\n                    setAvailableCommunesForForm(allCommunesData.filter({\n                        \"CasPage.useEffect\": (c)=>c.wilayaId === currentUser.wilayaId\n                    }[\"CasPage.useEffect\"]));\n                }\n            } else {\n                setAvailableCommunesForForm([]);\n            }\n        }\n    }[\"CasPage.useEffect\"], [\n        currentUser,\n        allCommunesData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            // AJOUTER LE CONSOLE.LOG POUR selectedEncrageId ICI\n            console.log(\"ID de l'encrage sélectionné:\", selectedEncrageId);\n            let newFiltered;\n            if (selectedEncrageId) {\n                newFiltered = problematiques.filter({\n                    \"CasPage.useEffect\": // Ajout d'une vérification pour p.encrage avant d'accéder à p.encrage.id\n                    (p)=>p.encrage && p.encrage.id === selectedEncrageId\n                }[\"CasPage.useEffect\"]);\n            } else {\n                newFiltered = [];\n            }\n            // CONSOLE.LOG EXISTANT POUR LES PROBLÉMATIQUES FILTRÉES\n            console.log(\"Problématiques filtrées pour l'encrage sélectionné:\", newFiltered);\n            setFilteredProblematiques(newFiltered);\n            // Réinitialiser problematiqueId dans formData uniquement si la sélection actuelle\n            // n'est plus valide pour le nouvel encrage sélectionné, ou si aucun encrage n'est sélectionné.\n            const currentProblematiqueStillValid = newFiltered.some({\n                \"CasPage.useEffect.currentProblematiqueStillValid\": (p)=>p.id === formData.problematiqueId\n            }[\"CasPage.useEffect.currentProblematiqueStillValid\"]);\n            if (!currentProblematiqueStillValid) {\n                setFormData({\n                    \"CasPage.useEffect\": (prev)=>({\n                            ...prev,\n                            problematiqueId: \"\"\n                        })\n                }[\"CasPage.useEffect\"]);\n            }\n        }\n    }[\"CasPage.useEffect\"], [\n        selectedEncrageId,\n        problematiques,\n        formData.problematiqueId\n    ]); // Ajout de formData.problematiqueId aux dépendances\n    async function loadCas(problematiqueId, encrageId) {\n        // Accepter problematiqueId et encrageId comme paramètres optionnels\n        try {\n            setIsLoading(true);\n            let url = \"/api/cas\";\n            const params = new URLSearchParams();\n            if (problematiqueId) {\n                params.append(\"problematiqueId\", problematiqueId);\n            }\n            if (encrageId) {\n                params.append(\"encrageId\", encrageId);\n            }\n            // Add pagination parameters\n            params.append(\"page\", page.toString());\n            params.append(\"pageSize\", pageSize.toString());\n            // Add search parameters\n            const searchTerms = [];\n            if (searchNom) searchTerms.push(searchNom);\n            if (searchCommune) searchTerms.push(searchCommune);\n            if (searchNifNin) searchTerms.push(searchNifNin);\n            if (searchTerms.length > 0) {\n                params.append(\"search\", searchTerms.join(\" \"));\n            }\n            // Add wilaya filter\n            if (searchWilaya) {\n                params.append(\"wilayaId\", searchWilaya);\n            }\n            // Add cas status filter\n            if (searchStatut) {\n                params.append(\"casStatus\", searchStatut);\n            }\n            if (params.toString()) {\n                url += \"?\".concat(params.toString());\n            }\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(url);\n            if (response) {\n                setCas(response.data || []);\n                // Update pagination state\n                setTotalPages(response.pagination.totalPages);\n                setTotalCount(response.pagination.totalCount);\n            } else {\n                setCas([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching cas:\", error);\n            // Handle authentication errors\n            if (error instanceof Error && error.message.includes(\"Authentication\")) {\n                window.location.href = \"/login\";\n            } else {\n                setError(\"Erreur lors du chargement des cas\");\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    async function loadEncrages() {\n        try {\n            setIsLoading(true);\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/encrages\");\n            setEncrages(data || []);\n        } catch (error) {\n            console.error(\"Error fetching encrages:\", error);\n            if (error instanceof Error && error.message.includes(\"Authentication\")) {\n                window.location.href = \"/login\";\n            } else {\n                setError(\"Erreur lors du chargement des encrages\");\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    async function loadProblematiques() {\n        try {\n            setIsLoading(true);\n            // Ajouter ?context=formCreation à l'URL\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/problematiques?context=formCreation\");\n            setProblematiques(data || []);\n        } catch (error) {\n            console.error(\"Error fetching problematiques:\", error);\n            // Handle authentication errors\n            if (error instanceof Error && error.message.includes(\"Authentication\")) {\n                window.location.href = \"/login\";\n            } else {\n                setError(\"Erreur lors du chargement des problématiques\");\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    async function handleDelete(cas) {\n        try {\n            setIsLoading(true);\n            setError(\"\"); // Clear any previous errors\n            console.log(\"Suppression du cas:\", cas.id);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/cas/\".concat(cas.id), {\n                method: \"DELETE\"\n            });\n            console.log(\"Réponse de suppression:\", response);\n            // Recharger immédiatement la liste des cas\n            await loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n            // Déclencher le rafraîchissement via le système centralisé\n            await afterDelete(\"cas\");\n            console.log(\"Suppression réussie et liste rechargée\");\n        } catch (error) {\n            console.error(\"Erreur lors de la suppression:\", error);\n            setError(error.message);\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    // Load current user from JWT token on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            async function loadCurrentUser() {\n                try {\n                    const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/auth/me\");\n                    if (response) {\n                        setCurrentUser(response);\n                    } else {\n                        // This case might occur if fetchApi returns null/undefined without throwing an error\n                        // for certain non-error responses that still indicate no user.\n                        // Depending on fetchApi's behavior, this might not be strictly necessary\n                        // if it always throws for auth issues.\n                        console.warn(\"No current user data received from /api/auth/me\");\n                    // Optionally, redirect or set error if 'null' response means unauthenticated\n                    // window.location.href = \"/login\";\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching current user:\", error);\n                    if (error instanceof Error && error.message.includes(\"Authentication\")) {\n                        window.location.href = \"/login\"; // Redirect on authentication failure\n                    } else {\n                        setError(\"Erreur lors de la récupération des informations utilisateur.\"); // Set a general error for other issues\n                    }\n                }\n            }\n            loadCurrentUser();\n        }\n    }[\"CasPage.useEffect\"], []); // Dependencies: router, setError (if used for consistency, but typically stable)\n    function handleEdit(casToEdit) {\n        var _casToEdit_superficie;\n        setCurrentCas(casToEdit);\n        setIsEditing(true);\n        let formattedDateDepot = \"\";\n        if (casToEdit.date_depot) {\n            try {\n                const dateObj = new Date(casToEdit.date_depot);\n                if (!isNaN(dateObj.getTime())) {\n                    formattedDateDepot = dateObj.toISOString().split(\"T\")[0];\n                }\n            } catch (error) {\n                console.warn(\"Date de dépôt invalide lors de l'édition:\", casToEdit.date_depot);\n            }\n        }\n        // CORRECTION : forcer les IDs en string\n        const existingCommuneIds = Array.isArray(casToEdit.communes) ? casToEdit.communes.map((c)=>String(c.id)) : [];\n        setFormData({\n            nom: casToEdit.nom || \"\",\n            genre: casToEdit.genre || \"\",\n            nif: casToEdit.nif || \"\",\n            nin: casToEdit.nin || \"\",\n            superficie: ((_casToEdit_superficie = casToEdit.superficie) === null || _casToEdit_superficie === void 0 ? void 0 : _casToEdit_superficie.toString()) || \"\",\n            observation: casToEdit.observation || \"\",\n            problematiqueId: casToEdit.problematiqueId || \"\",\n            communeIds: existingCommuneIds,\n            date_depot: formattedDateDepot,\n            regularisation: casToEdit.regularisation || false,\n            userId: casToEdit.userId || \"\"\n        });\n        setIsModalOpen(true);\n    }\n    function handleAdd() {\n        setCurrentCas(null);\n        setFormData({\n            nom: \"\",\n            genre: \"\",\n            nif: \"\",\n            nin: \"\",\n            superficie: \"\",\n            regularisation: false,\n            observation: \"\",\n            problematiqueId: \"\",\n            userId: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id) || \"\",\n            communeIds: [],\n            date_depot: \"\"\n        });\n        setSelectedEncrageId(\"\");\n        setIsEditing(false);\n        openModal();\n    }\n    // MODIFICATION 1: Définition de handleCommuneChange\n    // Cette fonction remplace celle qui se trouvait aux lignes 551-583\n    function handleCommuneChange(communeId, checked) {\n        setFormData((prev)=>{\n            const currentCommuneIds = prev.communeIds || [];\n            if (checked) {\n                if (!currentCommuneIds.includes(communeId)) {\n                    return {\n                        ...prev,\n                        communeIds: [\n                            ...currentCommuneIds,\n                            String(communeId)\n                        ]\n                    };\n                }\n            } else {\n                return {\n                    ...prev,\n                    communeIds: currentCommuneIds.filter((id)=>String(id) !== String(communeId))\n                };\n            }\n            return prev;\n        });\n    }\n    const columns = [\n        {\n            header: \"Nom\",\n            accessorKey: (row)=>row.nom,\n            className: \"max-w-[100px] w-28 truncate whitespace-nowrap overflow-hidden text-ellipsis\",\n            cell: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"truncate max-w-[100px] whitespace-nowrap overflow-hidden text-ellipsis cursor-help\",\n                    title: row.nom,\n                    children: row.nom\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 600,\n                    columnNumber: 17\n                }, this)\n        },\n        {\n            header: \"NIF/NIN\",\n            accessorKey: (row)=>row.nif || row.nin || \"N/A\",\n            className: \"max-w-[110px] w-32 truncate whitespace-nowrap overflow-hidden text-ellipsis\"\n        },\n        {\n            header: \"Commune(s)\",\n            accessorKey: (row)=>row.communes && row.communes.length > 0 ? row.communes.map((c)=>c.nom).join(\", \") : \"N/A\",\n            className: \"max-w-[110px] w-32 truncate whitespace-nowrap overflow-hidden text-ellipsis\",\n            cell: (row)=>{\n                const communes = row.communes && row.communes.length > 0 ? row.communes.map((c)=>c.nom).join(\", \") : \"N/A\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"truncate max-w-[110px] whitespace-nowrap overflow-hidden text-ellipsis cursor-help\",\n                    title: communes,\n                    children: communes\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 628,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            header: \"Superficie\",\n            accessorKey: (row)=>\"\".concat(row.superficie, \" Ha\"),\n            className: \"max-w-[80px] w-20 text-right truncate whitespace-nowrap overflow-hidden text-ellipsis\"\n        },\n        {\n            header: \"Statut\",\n            accessorKey: (row)=>{\n                const status = (0,_components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__.determineCasStatus)(row.blocage || []);\n                return status === \"REGULARISE\" ? \"Régularisé\" : status === \"AJOURNE\" ? \"Ajourné\" : \"Non examiné\";\n            },\n            className: \"max-w-[120px] w-28 truncate whitespace-nowrap overflow-hidden text-ellipsis text-center\",\n            cell: (row)=>{\n                const status = (0,_components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__.determineCasStatus)(row.blocage || []);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center px-2 py-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__.CasStatusBadge, {\n                        status: status\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 658,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            header: \"Problématique\",\n            accessorKey: (row)=>{\n                var _row_problematique;\n                return ((_row_problematique = row.problematique) === null || _row_problematique === void 0 ? void 0 : _row_problematique.problematique) || \"N/A\";\n            },\n            className: \"max-w-[110px] w-32 truncate whitespace-nowrap overflow-hidden text-ellipsis\",\n            cell: (row)=>{\n                var _row_problematique, _row_problematique1;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"truncate max-w-[110px] whitespace-nowrap overflow-hidden text-ellipsis cursor-help\",\n                    title: ((_row_problematique = row.problematique) === null || _row_problematique === void 0 ? void 0 : _row_problematique.problematique) || \"\",\n                    children: ((_row_problematique1 = row.problematique) === null || _row_problematique1 === void 0 ? void 0 : _row_problematique1.problematique) || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 671,\n                    columnNumber: 17\n                }, this);\n            }\n        }\n    ];\n    function handleChange(e) {\n        const { name, value, type } = e.target;\n        if (type === \"checkbox\") {\n            const { checked } = e.target;\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: checked\n                }));\n        } else if (name === \"communeIds\") {\n            // Gestion spécifique pour la sélection multiple des communes\n            const selectedOptions = e.target.selectedOptions;\n            const ids = Array.from(selectedOptions).map((option)=>option.value);\n            setFormData((prev)=>({\n                    ...prev,\n                    communeIds: ids\n                }));\n        } else if (name === \"nif\") {\n            setFormData((prev)=>({\n                    ...prev,\n                    nif: formatGenericNumericInput(value, \"nif\")\n                }));\n        } else if (name === \"nin\") {\n            setFormData((prev)=>({\n                    ...prev,\n                    nin: formatGenericNumericInput(value, \"nin\")\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: value\n                }));\n        }\n    }\n    // function handleEdit(casToEdit: Cas) {\n    //     setCurrentCas(casToEdit);\n    //     setIsEditing(true); // Mettre à jour l'état d'édition\n    //     setFormData({\n    //         nom: casToEdit.nom,\n    //         genre: (casToEdit as any).genre || \"\",\n    //         nif: casToEdit.nif || \"\",\n    //         nin: (casToEdit as any).nin || \"\",\n    //         superficie: String(casToEdit.superficie),\n    //         observation: casToEdit.observation || \"\",\n    //         problematiqueId: casToEdit.problematiqueId,\n    //         communeIds: casToEdit.communes\n    //             ? casToEdit.communes.map((c) => String(c.id))\n    //             : [],\n    //         date_depot: (casToEdit as any).date_depot || \"\",\n    //         communes: casToEdit.communes || [],\n    //         regularisation: casToEdit.regularisation,\n    //         userId: casToEdit.userId,\n    //     });\n    //     // Si vous filtrez les problématiques par encrage dans le modal:\n    //     if (casToEdit.problematique && casToEdit.problematique.encrage) {\n    //         setSelectedEncrageId(casToEdit.problematique.encrage.id);\n    //     } else {\n    //         setSelectedEncrageId(\"\");\n    //     }\n    //     setIsModalOpen(true);\n    // }\n    async function handleSubmit(e) {\n        e.preventDefault();\n        setError(\"\");\n        setIsLoading(true);\n        if (!formData.communeIds || formData.communeIds.length === 0) {\n            setError(\"Au moins une commune doit être sélectionnée.\");\n            setIsLoading(false);\n            return;\n        }\n        const superficieValue = parseFloat(formData.superficie);\n        if (isNaN(superficieValue) || superficieValue <= 0) {\n            setError(\"La superficie doit être un nombre positif valide.\");\n            setIsLoading(false);\n            return;\n        }\n        const dataToSend = {\n            nom: formData.nom,\n            genre: formData.genre,\n            nif: formData.nif || null,\n            nin: formData.nin || null,\n            superficie: superficieValue,\n            observation: formData.observation || null,\n            problematiqueId: formData.problematiqueId,\n            date_depot: formData.date_depot ? new Date(formData.date_depot).toISOString() : null,\n            communes: formData.communeIds.map((id)=>{\n                const commune = allCommunesData.find((c)=>c.id === id);\n                return commune ? {\n                    nom: commune.nom,\n                    wilayaId: commune.wilayaId\n                } : null;\n            }).filter(Boolean)\n        };\n        console.log(\"Données envoyées à /api/cas:\", dataToSend);\n        try {\n            if (isEditing && currentCas) {\n                await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/cas/\".concat(currentCas.id), {\n                    method: \"PUT\",\n                    body: dataToSend\n                });\n            } else {\n                await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/cas\", {\n                    method: \"POST\",\n                    body: {\n                        ...dataToSend,\n                        communeIds: formData.communeIds.map(String)\n                    }\n                });\n            }\n            closeModal();\n            // Déclencher le rafraîchissement via le système centralisé\n            if (isEditing) {\n                await afterUpdate(\"cas\");\n            } else {\n                await afterCreate(\"cas\");\n            }\n        } catch (err) {\n            console.error(\"Erreur lors de la soumission du formulaire:\", err);\n            setError(err.message || \"Une erreur est survenue.\");\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    const [searchNom, setSearchNom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchCommune, setSearchCommune] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchNifNin, setSearchNifNin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchWilaya, setSearchWilaya] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchStatut, setSearchStatut] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Liste des 58 wilayas d'Algérie (DSA)\n    const wilayasList = [\n        {\n            id: 1,\n            nom: \"DSA Adrar\"\n        },\n        {\n            id: 2,\n            nom: \"DSA Chlef\"\n        },\n        {\n            id: 3,\n            nom: \"DSA Laghouat\"\n        },\n        {\n            id: 4,\n            nom: \"DSA Oum El Bouaghi\"\n        },\n        {\n            id: 5,\n            nom: \"DSA Batna\"\n        },\n        {\n            id: 6,\n            nom: \"DSA Béjaïa\"\n        },\n        {\n            id: 7,\n            nom: \"DSA Biskra\"\n        },\n        {\n            id: 8,\n            nom: \"DSA Béchar\"\n        },\n        {\n            id: 9,\n            nom: \"DSA Blida\"\n        },\n        {\n            id: 10,\n            nom: \"DSA Bouira\"\n        },\n        {\n            id: 11,\n            nom: \"DSA Tamanrasset\"\n        },\n        {\n            id: 12,\n            nom: \"DSA Tébessa\"\n        },\n        {\n            id: 13,\n            nom: \"DSA Tlemcen\"\n        },\n        {\n            id: 14,\n            nom: \"DSA Tiaret\"\n        },\n        {\n            id: 15,\n            nom: \"DSA Tizi Ouzou\"\n        },\n        {\n            id: 16,\n            nom: \"DSA Alger\"\n        },\n        {\n            id: 17,\n            nom: \"DSA Djelfa\"\n        },\n        {\n            id: 18,\n            nom: \"DSA Jijel\"\n        },\n        {\n            id: 19,\n            nom: \"DSA Sétif\"\n        },\n        {\n            id: 20,\n            nom: \"DSA Saïda\"\n        },\n        {\n            id: 21,\n            nom: \"DSA Skikda\"\n        },\n        {\n            id: 22,\n            nom: \"DSA Sidi Bel Abbès\"\n        },\n        {\n            id: 23,\n            nom: \"DSA Annaba\"\n        },\n        {\n            id: 24,\n            nom: \"DSA Guelma\"\n        },\n        {\n            id: 25,\n            nom: \"DSA Constantine\"\n        },\n        {\n            id: 26,\n            nom: \"DSA Médéa\"\n        },\n        {\n            id: 27,\n            nom: \"DSA Mostaganem\"\n        },\n        {\n            id: 28,\n            nom: \"DSA M'Sila\"\n        },\n        {\n            id: 29,\n            nom: \"DSA Mascara\"\n        },\n        {\n            id: 30,\n            nom: \"DSA Ouargla\"\n        },\n        {\n            id: 31,\n            nom: \"DSA Oran\"\n        },\n        {\n            id: 32,\n            nom: \"DSA El Bayadh\"\n        },\n        {\n            id: 33,\n            nom: \"DSA Illizi\"\n        },\n        {\n            id: 34,\n            nom: \"DSA Bordj Bou Arréridj\"\n        },\n        {\n            id: 35,\n            nom: \"DSA Boumerdès\"\n        },\n        {\n            id: 36,\n            nom: \"DSA El Tarf\"\n        },\n        {\n            id: 37,\n            nom: \"DSA Tindouf\"\n        },\n        {\n            id: 38,\n            nom: \"DSA Tissemsilt\"\n        },\n        {\n            id: 39,\n            nom: \"DSA El Oued\"\n        },\n        {\n            id: 40,\n            nom: \"DSA Khenchela\"\n        },\n        {\n            id: 41,\n            nom: \"DSA Souk Ahras\"\n        },\n        {\n            id: 42,\n            nom: \"DSA Tipaza\"\n        },\n        {\n            id: 43,\n            nom: \"DSA Mila\"\n        },\n        {\n            id: 44,\n            nom: \"DSA Aïn Defla\"\n        },\n        {\n            id: 45,\n            nom: \"DSA Naâma\"\n        },\n        {\n            id: 46,\n            nom: \"DSA Aïn Témouchent\"\n        },\n        {\n            id: 47,\n            nom: \"DSA Ghardaïa\"\n        },\n        {\n            id: 48,\n            nom: \"DSA Relizane\"\n        },\n        {\n            id: 49,\n            nom: \"DSA Timimoun\"\n        },\n        {\n            id: 50,\n            nom: \"DSA Bordj Badji Mokhtar\"\n        },\n        {\n            id: 51,\n            nom: \"DSA Ouled Djellal\"\n        },\n        {\n            id: 52,\n            nom: \"DSA Béni Abbès\"\n        },\n        {\n            id: 53,\n            nom: \"DSA In Salah\"\n        },\n        {\n            id: 54,\n            nom: \"DSA In Guezzam\"\n        },\n        {\n            id: 55,\n            nom: \"DSA Touggourt\"\n        },\n        {\n            id: 56,\n            nom: \"DSA Djanet\"\n        },\n        {\n            id: 57,\n            nom: \"DSA El M'Ghair\"\n        },\n        {\n            id: 58,\n            nom: \"DSA El Meniaa\"\n        }\n    ];\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100); // Augmenté à 100 pour afficher plus de dossiers\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Remove client-side filtering since we're using server-side pagination\n    // const filteredCas = cas.filter((row) => {\n    //     const matchesNom = row.nom\n    //         .toLowerCase()\n    //         .includes(searchNom.toLowerCase());\n    //     const matchesCommune = row.communes.some((commune) =>\n    //         commune.nom.toLowerCase().includes(searchCommune.toLowerCase())\n    //     );\n    //     const matchesNifNin =\n    //         (row.nif &&\n    //             row.nif.toLowerCase().includes(searchNifNin.toLowerCase())) ||\n    //         (row.nin &&\n    //             row.nin.toLowerCase().includes(searchNifNin.toLowerCase()));\n    //     // Tous les champs de recherche doivent matcher (AND logique)\n    //     return (\n    //         (!searchNom || matchesNom) &&\n    //         (!searchCommune || matchesCommune) &&\n    //         (!searchNifNin || matchesNifNin)\n    //     );\n    // });\n    // Pagination - now using server-side data\n    // const totalPages = Math.max(1, Math.ceil(filteredCas.length / pageSize));\n    // const paginatedCas = filteredCas.slice(\n    //     (page - 1) * pageSize,\n    //     page * pageSize\n    // );\n    // Réinitialiser la page si le filtre réduit le nombre de pages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            if (page > totalPages) setPage(1);\n        }\n    }[\"CasPage.useEffect\"], [\n        totalPages,\n        page\n    ]);\n    // Trigger data loading when page or search parameters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n        }\n    }[\"CasPage.useEffect\"], [\n        page,\n        pageSize,\n        searchNom,\n        searchCommune,\n        searchNifNin,\n        searchWilaya,\n        searchStatut\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 transition-all duration-300\",\n        style: {\n            paddingLeft: \"var(--sidebar-width, 0.5rem)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-9xl mx-auto px-1 sm:px-1 md:px-1 py-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"sticky top-0 z-10 bg-white shadow-md py-4 px-1 mb-3 rounded-xl flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-8 w-8 text-indigo-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 939,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl md:text-3xl font-bold text-gray-800 tracking-tight\",\n                                        children: \"Gestion des Dossiers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.UserRoleBadge, {\n                                        className: \"mt-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 flex items-center gap-4 text-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                    children: [\n                                                        \"\\uD83D\\uDCCA \",\n                                                        totalCount.toLocaleString(),\n                                                        \" dossier\",\n                                                        totalCount > 1 ? \"s\" : \"\",\n                                                        \" trouv\\xe9\",\n                                                        totalCount > 1 ? \"s\" : \"\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                    lineNumber: 948,\n                                                    columnNumber: 33\n                                                }, this),\n                                                (searchNom || searchCommune || searchNifNin || searchWilaya || searchStatut || problematiqueIdFromUrl || encrageIdFromUrl) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                    children: \"\\uD83D\\uDD0D Filtres actifs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 940,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ExportExcelButton__WEBPACK_IMPORTED_MODULE_11__.ExportExcelButton, {\n                                        filters: {\n                                            search: searchNom,\n                                            casStatus: searchStatut,\n                                            wilayaId: searchWilaya,\n                                            problematiqueId: problematiqueIdFromUrl || undefined,\n                                            encrageId: encrageIdFromUrl || undefined\n                                        },\n                                        totalCasCount: totalCount,\n                                        className: \"bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold px-2 py-2 rounded-lg shadow transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 981,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden md:inline text-xs\",\n                                                children: totalCount > 50000 ? \"Export désactivé\" : \"Export (≤50k)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 982,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 969,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ExportBatchButton__WEBPACK_IMPORTED_MODULE_12__.ExportBatchButton, {\n                                        filters: {\n                                            search: searchNom,\n                                            casStatus: searchStatut,\n                                            wilayaId: searchWilaya,\n                                            problematiqueId: problematiqueIdFromUrl || undefined,\n                                            encrageId: encrageIdFromUrl || undefined\n                                        },\n                                        className: \"bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold px-2 py-2 rounded-lg shadow transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 1001,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden md:inline text-xs\",\n                                                children: \"Batch (>50k)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 990,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.WriteAccess, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.PermissionButton, {\n                                            onClick: openModal,\n                                            requirePermission: \"canWrite\",\n                                            disabledMessage: \"Vous n'avez pas les permissions pour ajouter des dossiers\",\n                                            className: \"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold px-2 py-2 rounded-lg shadow transition-all duration-200 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                    lineNumber: 1014,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Ajouter un dossier\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                    lineNumber: 1015,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1008,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1007,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 967,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 938,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewerReadOnlyMessage, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1024,\n                        columnNumber: 17\n                    }, this),\n                    currentEncrageName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block bg-indigo-100 text-indigo-700 px-1 py-1 rounded-full text-sm font-medium shadow\",\n                            children: [\n                                \"Encrage : \",\n                                currentEncrageName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1028,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1027,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row items-center gap-2 mb-2 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                type: \"text\",\n                                placeholder: \"Recherche par nom...\",\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow\",\n                                value: searchNom,\n                                onChange: (e)=>{\n                                    setSearchNom(e.target.value);\n                                    setPage(1);\n                                },\n                                onKeyDown: (e)=>{\n                                    if (e.key === \"Enter\") {\n                                        loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1036,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                type: \"text\",\n                                placeholder: \"Recherche par commune...\",\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow\",\n                                value: searchCommune,\n                                onChange: (e)=>{\n                                    setSearchCommune(e.target.value);\n                                    setPage(1);\n                                },\n                                onKeyDown: (e)=>{\n                                    if (e.key === \"Enter\") {\n                                        loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1054,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                type: \"text\",\n                                placeholder: \"Recherche par NIF/NIN...\",\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow\",\n                                value: searchNifNin,\n                                onChange: (e)=>{\n                                    setSearchNifNin(e.target.value);\n                                    setPage(1);\n                                },\n                                onKeyDown: (e)=>{\n                                    if (e.key === \"Enter\") {\n                                        loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1072,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1035,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row items-center gap-2 mb-4 w-full\",\n                        children: [\n                            (isAdmin || isViewer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow\",\n                                value: searchWilaya,\n                                onChange: (e)=>{\n                                    setSearchWilaya(e.target.value);\n                                    setPage(1);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Toutes les wilayas (DSA)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1104,\n                                        columnNumber: 29\n                                    }, this),\n                                    wilayasList.map((wilaya)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: wilaya.id.toString(),\n                                            children: wilaya.nom\n                                        }, wilaya.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1106,\n                                            columnNumber: 33\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1096,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow\",\n                                value: searchStatut,\n                                onChange: (e)=>{\n                                    setSearchStatut(e.target.value);\n                                    setPage(1);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Tous les statuts\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1124,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"REGULARISE\",\n                                        children: \"R\\xe9gularis\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1125,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"AJOURNE\",\n                                        children: \"Ajourn\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1126,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"NON_EXAMINE\",\n                                        children: \"Non examin\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1127,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"REJETE\",\n                                        children: \"Rejet\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1128,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1116,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>loadCas(problematiqueIdFromUrl, encrageIdFromUrl),\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm\",\n                                disabled: isLoading,\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1139,\n                                    columnNumber: 29\n                                }, this) : \"Rechercher\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1131,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>{\n                                    setSearchNom(\"\");\n                                    setSearchCommune(\"\");\n                                    setSearchNifNin(\"\");\n                                    // Réinitialiser le filtre wilaya seulement pour ADMIN et VIEWER\n                                    if (isAdmin || isViewer) {\n                                        setSearchWilaya(\"\");\n                                    }\n                                    setSearchStatut(\"\");\n                                    setPage(1);\n                                    // Recharger les données après réinitialisation\n                                    setTimeout(()=>{\n                                        loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                    }, 100);\n                                },\n                                className: \"bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm\",\n                                disabled: isLoading,\n                                children: \"R\\xe9initialiser\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1145,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1093,\n                        columnNumber: 17\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1174,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Chargement des donn\\xe9es...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1175,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1173,\n                        columnNumber: 21\n                    }, this),\n                    !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                        data: cas,\n                        columns: columns,\n                        actions: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>router.push(\"/dashboard/cas/\".concat(row.id)),\n                                        className: \"bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-bold py-1 px-2 rounded-lg text-xs flex items-center shadow transition-all duration-200\",\n                                        title: \"Voir les d\\xe9tails du dossier\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1195,\n                                            columnNumber: 37\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1188,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.DeleteAccess, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.PermissionButton, {\n                                            onClick: ()=>{\n                                                setCasToDelete(row);\n                                                setIsDeleteModalOpen(true);\n                                            },\n                                            requirePermission: \"canDelete\",\n                                            disabledMessage: \"Vous n'avez pas les permissions pour supprimer des dossiers\",\n                                            className: \"bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white font-bold py-1 px-2 rounded-lg text-xs flex items-center shadow transition-all duration-200\",\n                                            title: \"Supprimer ce dossier\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 1208,\n                                                columnNumber: 41\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 37\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1197,\n                                        columnNumber: 33\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1187,\n                                columnNumber: 29\n                            }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1183,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-end items-center gap-2 px-2 sm:px-6 py-3 border-t border-gray-200 bg-gray-50 flex-wrap rounded-b-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm mr-2\",\n                                children: \"R\\xe9sultats par page :\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1217,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all\",\n                                value: pageSize,\n                                onChange: (e)=>{\n                                    setPageSize(Number(e.target.value));\n                                    setPage(1);\n                                    loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                },\n                                disabled: isLoading,\n                                children: [\n                                    10,\n                                    20,\n                                    50,\n                                    100,\n                                    200,\n                                    500,\n                                    1000\n                                ].map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: size,\n                                        children: size\n                                    }, size, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1229,\n                                        columnNumber: 29\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1218,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>{\n                                    setPage(page - 1);\n                                    loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                },\n                                disabled: page === 1 || isLoading,\n                                className: \"px-3 py-1 text-sm rounded-md border border-gray-200 bg-white hover:bg-gray-100 text-gray-700 shadow-sm\",\n                                children: \"Pr\\xe9c\\xe9dent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1234,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: [\n                                    \"Page \",\n                                    page,\n                                    \" / \",\n                                    totalPages,\n                                    \" (\",\n                                    totalCount,\n                                    \" r\\xe9sultats)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1244,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>{\n                                    setPage(page + 1);\n                                    loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                },\n                                disabled: page === totalPages || isLoading,\n                                className: \"px-3 py-1 text-sm rounded-md border border-gray-200 bg-white hover:bg-gray-100 text-gray-700 shadow-sm\",\n                                children: \"Suivant\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1247,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1216,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                lineNumber: 937,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                isOpen: isDeleteModalOpen,\n                onClose: ()=>setIsDeleteModalOpen(false),\n                title: \"Confirmer la suppression\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xcates-vous s\\xfbr de vouloir supprimer ce dossier ? Cette action est irr\\xe9versible.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1266,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>setIsDeleteModalOpen(false),\n                                    className: \"bg-gray-200 text-gray-800\",\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1271,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: async ()=>{\n                                        if (casToDelete) {\n                                            try {\n                                                await handleDelete(casToDelete);\n                                                // Fermer la modal seulement si la suppression réussit\n                                                setIsDeleteModalOpen(false);\n                                                setCasToDelete(null);\n                                            } catch (error) {\n                                                // En cas d'erreur, la modal reste ouverte\n                                                console.error(\"Erreur lors de la suppression:\", error);\n                                            // L'erreur est déjà gérée dans handleDelete\n                                            }\n                                        }\n                                    },\n                                    className: \"bg-red-600 text-white\",\n                                    isLoading: isLoading,\n                                    children: \"Supprimer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1277,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1270,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 1265,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                lineNumber: 1260,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                isOpen: isModalOpen,\n                onClose: closeModal,\n                title: isEditing ? \"Modifier le Dossier\" : \"Ajouter un Dossier\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6 p-1\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormError__WEBPACK_IMPORTED_MODULE_7__.FormError, {\n                            message: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1310,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-1 gap-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                id: \"nom\",\n                                label: \"Nom du dossier\",\n                                value: formData.nom,\n                                onChange: (e)=>setFormData({\n                                        ...formData,\n                                        nom: e.target.value\n                                    }),\n                                required: true,\n                                placeholder: \"Nom du cas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1313,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1312,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"modal_genre\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1342,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Select__WEBPACK_IMPORTED_MODULE_16__.Select // Assurez-vous que le composant Select est importé et utilisé correctement\n                                , {\n                                    name: \"genre\",\n                                    id: \"modal_genre\",\n                                    value: formData.genre,\n                                    onChange: handleChange,\n                                    required: true,\n                                    className: \"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"S\\xe9lectionner un type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1356,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: _prisma_client__WEBPACK_IMPORTED_MODULE_22__.TypePersonne.PERSONNE_PHYSIQUE,\n                                            children: \"Personne Physique\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1359,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: _prisma_client__WEBPACK_IMPORTED_MODULE_22__.TypePersonne.PERSONNE_MORALE,\n                                            children: \"Personne Morale\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1362,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1348,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1341,\n                            columnNumber: 21\n                        }, this),\n                        formData.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_22__.TypePersonne.PERSONNE_MORALE && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"modal_nif\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"NIF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1371,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    id: \"modal_nif\",\n                                    name: \"nif\" // Important: name attribute for handleChange\n                                    ,\n                                    value: formData.nif || \"\",\n                                    onChange: handleChange,\n                                    required: true,\n                                    placeholder: \"Num\\xe9ro d'Identification Fiscale\",\n                                    className: \"mt-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1377,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1370,\n                            columnNumber: 25\n                        }, this),\n                        formData.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_22__.TypePersonne.PERSONNE_PHYSIQUE && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"modal_nin\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"NIN\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1392,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    id: \"modal_nin\",\n                                    name: \"nin\" // Important: name attribute for handleChange\n                                    ,\n                                    value: formData.nin || \"\",\n                                    onChange: handleChange,\n                                    required: true,\n                                    placeholder: \"Num\\xe9ro d'Identification National\",\n                                    className: \"mt-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1398,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1391,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Commune(s)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1411,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto border rounded p-2\",\n                                    children: availableCommunesForForm.map((commune)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: formData.communeIds.includes(String(commune.id)),\n                                                    onChange: (e)=>handleCommuneChange(String(commune.id), e.target.checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                    lineNumber: 1420,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: commune.nom\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                    lineNumber: 1432,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, commune.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1416,\n                                            columnNumber: 33\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1414,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1410,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"encrageId\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Encrage Juridique\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1440,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"encrageId\",\n                                    value: selectedEncrageId,\n                                    onChange: (e)=>setSelectedEncrageId(e.target.value),\n                                    className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm p-2\",\n                                    required: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"S\\xe9lectionner un encrage\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1455,\n                                            columnNumber: 29\n                                        }, this),\n                                        encrages.map((encrage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: encrage.id,\n                                                children: encrage.nom\n                                            }, encrage.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 1457,\n                                                columnNumber: 33\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1446,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1439,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"problematiqueId\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Probl\\xe9matique Sp\\xe9cifique\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1466,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"problematiqueId\",\n                                    value: formData.problematiqueId,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            problematiqueId: e.target.value\n                                        }),\n                                    className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm p-2\",\n                                    required: true,\n                                    disabled: !selectedEncrageId || filteredProblematiques.length === 0,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: selectedEncrageId ? filteredProblematiques.length > 0 ? \"Sélectionner une problématique\" : \"Aucune problématique pour cet encrage\" : \"Sélectionner d'abord un encrage\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1488,\n                                            columnNumber: 29\n                                        }, this),\n                                        filteredProblematiques.map((problematique)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: problematique.id,\n                                                children: problematique.problematique\n                                            }, problematique.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 1496,\n                                                columnNumber: 33\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1472,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1465,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            id: \"superficie\",\n                            label: \"Superficie (Ha)\",\n                            type: \"number\",\n                            value: formData.superficie,\n                            onChange: (e)=>setFormData({\n                                    ...formData,\n                                    superficie: e.target.value\n                                }),\n                            required: true,\n                            placeholder: \"Ex: 120.5\",\n                            min: \"0\",\n                            step: \"any\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1506,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Date de d\\xe9p\\xf4t du dossier\",\n                                type: \"date\" // Important: type=\"date\"\n                                ,\n                                name: \"date_depot\",\n                                value: formData.date_depot || \"\",\n                                onChange: handleChange,\n                                placeholder: \"JJ/MM/AAAA\" // Le placeholder n'est généralement pas affiché pour type=\"date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1525,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1524,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TextArea__WEBPACK_IMPORTED_MODULE_4__.TextArea, {\n                            id: \"observation\",\n                            label: \"Observation\",\n                            value: formData.observation || \"\",\n                            onChange: (e)=>setFormData({\n                                    ...formData,\n                                    observation: e.target.value\n                                }),\n                            placeholder: \"Ajoutez des observations ici...\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1535,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsModalOpen(false),\n                                    type: \"button\",\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1550,\n                                    columnNumber: 25\n                                }, this),\n                                (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) !== \"BASIC\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    isLoading: isLoading,\n                                    children: isEditing ? \"Enregistré\" : \"Créer le Dossier\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1558,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1549,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 1309,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                lineNumber: 1304,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n        lineNumber: 931,\n        columnNumber: 9\n    }, this);\n}\n_s1(CasPage, \"rHTj0Eqqv0mjBOtpBnK5lm5Ng04=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useSearchParams,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useOperationRefresh,\n        _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_15__.usePermissions,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh\n    ];\n});\n_c1 = CasPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ViewerReadOnlyMessage\");\n$RefreshReg$(_c1, \"CasPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/cas/page.tsx\n"));

/***/ })

});