"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/problematiques/page",{

/***/ "(app-pages-browser)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   fetchApi: () => (/* binding */ fetchApi),\n/* harmony export */   getCas: () => (/* binding */ getCas),\n/* harmony export */   getChat: () => (/* binding */ getChat),\n/* harmony export */   sendMessage: () => (/* binding */ sendMessage)\n/* harmony export */ });\nasync function fetchApi(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { method = \"GET\", body } = options;\n    const response = await fetch(endpoint, {\n        method,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...options.headers\n        },\n        credentials: options.credentials || \"include\",\n        body: body ? JSON.stringify(body) : undefined\n    });\n    if (!response.ok) {\n        let errorMessage = \"HTTP error! status: \".concat(response.status);\n        let errorData = null;\n        try {\n            // Read the response body as text first\n            const errorText = await response.text();\n            if (errorText) {\n                try {\n                    // Try to parse the text as JSON\n                    errorData = JSON.parse(errorText);\n                    errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.error) || (errorData === null || errorData === void 0 ? void 0 : errorData.message) || errorText;\n                } catch (parseError) {\n                    // If parsing fails, use the raw text as the error message\n                    errorMessage = errorText || errorMessage;\n                }\n            }\n        } catch (readError) {\n            // If we can't read the response body, use the status\n            console.warn(\"Could not read error response body:\", readError);\n        }\n        // Log error details for debugging (but don't throw console.error)\n        if (true) {\n            console.warn(\"API Error Details:\", {\n                url: response.url,\n                status: response.status,\n                statusText: response.statusText,\n                errorMessage,\n                errorData\n            });\n        }\n        // Handle specific error cases\n        if (response.status === 401) {\n            throw new Error(\"Authentication required. Please log in again.\");\n        } else if (response.status === 403) {\n            throw new Error(\"Access denied. You don't have permission to perform this action.\");\n        } else if (response.status === 404) {\n            throw new Error(\"Resource not found.\");\n        } else if (response.status >= 500) {\n            throw new Error(\"Server error. Please try again later.\");\n        }\n        throw new Error(errorMessage);\n    }\n    if (response.status === 204) {\n        // No Content\n        return null;\n    }\n    return response.json();\n}\n// Add the following apiClient export:\nconst apiClient = {\n    get: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"GET\"\n        });\n    },\n    post: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"POST\",\n            body\n        });\n    },\n    put: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"PUT\",\n            body\n        });\n    },\n    delete: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"DELETE\"\n        });\n    }\n};\n// Chat Functions\nconst getChat = (casId)=>{\n    return apiClient.get(\"/api/chats/\".concat(casId));\n};\nconst sendMessage = (casId, content)=>{\n    return apiClient.post(\"/api/chats/\".concat(casId, \"/messages\"), {\n        content\n    });\n};\nasync function fetchPage(page, pageSize) {\n    const response = await fetch(\"/api/cas?page=\".concat(page, \"&pageSize=\").concat(pageSize), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        credentials: \"include\"\n    });\n    if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"API Error response:\", errorText);\n        throw new Error(\"API request failed with status \".concat(response.status));\n    }\n    return response.json();\n}\nasync function getCas() {\n    try {\n        console.log(\"Fetching dossiers from /api/cas...\");\n        const pageSize = 100; // Maximum allowed by the API\n        let currentPage = 1;\n        let allDossiers = [];\n        let hasMorePages = true;\n        let totalPages = 1;\n        // Fetch all pages\n        while(hasMorePages && currentPage <= 20){\n            // Add a safety limit of 20 pages\n            console.log(\"Fetching page \".concat(currentPage, \"...\"));\n            const result = await fetchPage(currentPage, pageSize);\n            if (!result.data || !Array.isArray(result.data)) {\n                console.error(\"Invalid data format in page\", currentPage, \":\", result);\n                throw new Error(\"Invalid data format received from server\");\n            }\n            allDossiers = [\n                ...allDossiers,\n                ...result.data\n            ];\n            totalPages = result.pagination.totalPages;\n            hasMorePages = result.pagination.hasNextPage && currentPage < totalPages;\n            currentPage++;\n            // If we've fetched all pages or reached the safety limit, stop\n            if (!hasMorePages || currentPage > totalPages) {\n                break;\n            }\n        }\n        console.log(\"Fetched \".concat(allDossiers.length, \" dossiers from \").concat(currentPage - 1, \" pages\"));\n        return allDossiers;\n    } catch (error) {\n        if (error instanceof Error) {\n            console.error(\"Error in getCas:\", {\n                message: error.message,\n                name: error.name,\n                stack: error.stack\n            });\n        } else {\n            console.error(\"Unknown error in getCas:\", error);\n        }\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-client.ts\n"));

/***/ })

});