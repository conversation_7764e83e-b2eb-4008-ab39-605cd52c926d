/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/users/[id]/route";
exports.ids = ["app/api/users/[id]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/users/[id]/route.ts":
/*!*************************************!*\
  !*** ./app/api/users/[id]/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n\n\n\n\n\n\nconst userUpdateSchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().email().optional(),\n    username: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(3).max(100).optional(),\n    password: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(6).optional(),\n    role: zod__WEBPACK_IMPORTED_MODULE_5__.z.enum([\n        \"ADMIN\",\n        \"EDITOR\",\n        \"BASIC\",\n        \"VIEWER\"\n    ]).optional(),\n    wilayaId: zod__WEBPACK_IMPORTED_MODULE_5__.z.number().optional().nullable()\n});\n// Get a specific user by ID - only accessible by ADMIN or the user themselves\nasync function GET(req, { params }) {\n    try {\n        const { id: userId } = await params;\n        // Get the current user from the token\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token);\n        if (!userPayload) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        // Only allow ADMIN or the user themselves to access this endpoint\n        if (userPayload.role !== \"ADMIN\" && userPayload.id !== userId) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        }\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                id: true,\n                email: true,\n                username: true,\n                role: true,\n                wilayaId: true,\n                createdAt: true,\n                updatedAt: true,\n                // Exclude password for security\n                _count: {\n                    select: {\n                        cas: true\n                    }\n                }\n            }\n        });\n        if (!user) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.notFound)(\"Utilisateur non trouvé\");\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(user);\n    } catch (error) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n// Update a specific user - only accessible by ADMIN or the user themselves\nasync function PUT(request, { params }) {\n    try {\n        const { id: userId } = await params;\n        // Get the current user from the token\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token);\n        if (!userPayload) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        // Only allow ADMIN or the user themselves to update\n        // Only ADMIN can change roles\n        const isAdmin = userPayload.role === \"ADMIN\";\n        const isSelf = userPayload.id === userId;\n        if (!isAdmin && !isSelf) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        }\n        // Check if user exists\n        const existingUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            }\n        });\n        if (!existingUser) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.notFound)(\"Utilisateur non trouvé\");\n        }\n        const body = await request.json();\n        const parsedData = userUpdateSchema.parse(body);\n        // Non-admin users cannot change their role\n        if (!isAdmin && parsedData.role) {\n            delete parsedData.role;\n        }\n        // Hash password if provided\n        let updateData = {\n            ...parsedData\n        };\n        if (parsedData.password) {\n            updateData.password = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.hashPassword)(parsedData.password);\n        }\n        const updatedUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: updateData,\n            select: {\n                id: true,\n                email: true,\n                username: true,\n                role: true,\n                wilayaId: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(updatedUser);\n    } catch (error) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n// Delete a specific user - only accessible by ADMIN\nasync function DELETE(req, { params }) {\n    try {\n        const { id: userId } = await params;\n        // Get the current user from the token\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token);\n        if (!userPayload || userPayload.role !== \"ADMIN\") {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        }\n        // Check if user exists\n        const existingUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            }\n        });\n        if (!existingUser) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.notFound)(\"Utilisateur non trouvé\");\n        }\n        // Delete the user\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.delete({\n            where: {\n                id: userId\n            }\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse(null, {\n            status: 204\n        }); // No content\n    } catch (error) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/users/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   forbidden: () => (/* binding */ forbidden),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   notFound: () => (/* binding */ notFound),\n/* harmony export */   unauthorized: () => (/* binding */ unauthorized),\n/* harmony export */   updateCasRegularisationStatus: () => (/* binding */ updateCasRegularisationStatus)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n // Importez Prisma pour typer les erreurs spécifiques\n\nasync function updateCasRegularisationStatus(casId) {\n    const relatedCasBlocages = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.blocage.findMany({\n        where: {\n            casId: casId\n        },\n        select: {\n            regularise: true\n        }\n    });\n    const allBlocagesRegularised = relatedCasBlocages.length > 0 && relatedCasBlocages.every((b)=>b.regularise);\n    await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.cas.update({\n        where: {\n            id: casId\n        },\n        data: {\n            regularisation: allBlocagesRegularised\n        }\n    });\n}\nfunction handleError(error) {\n    console.error(error); // Bon pour le débogage côté serveur\n    if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_1__.Prisma.PrismaClientKnownRequestError) {\n        // Erreurs connues de Prisma (contraintes uniques, etc.)\n        // Vous pouvez ajouter des codes d'erreur spécifiques ici si nécessaire\n        // Par exemple, P2002 pour violation de contrainte unique\n        if (error.code === \"P2002\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Une ressource avec ces identifiants existe déjà.\",\n                details: error.meta\n            }, {\n                status: 409\n            }); // Conflict\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur de base de données.\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n    if (error instanceof Error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Une erreur interne est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Une erreur inconnue est survenue.\"\n    }, {\n        status: 500\n    });\n}\nfunction forbidden(message = \"Accès interdit.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 403\n    });\n}\nfunction notFound(message = \"Ressource non trouvée.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 404\n    });\n}\nfunction unauthorized() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Non autorisé\"\n    }, {\n        status: 401\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_users_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/users/[id]/route.ts */ \"(rsc)/./app/api/users/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/users/[id]/route\",\n        pathname: \"/api/users/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\users\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_users_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();