/**
 * Script pour réinitialiser les données de régularisation et résolution
 * Usage: node scripts/reset-data.js
 */

const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

async function resetData() {
    console.log('🔄 Script de Réinitialisation des Données');
    console.log('=====================================');
    console.log('');
    console.log('Cette opération va :');
    console.log('• Réinitialiser toutes les résolutions de blocage à "ATTENTE"');
    console.log('• Réinitialiser toutes les régularisations de cas à "false"');
    console.log('');
    console.log('⚠️  ATTENTION: Cette action est IRRÉVERSIBLE !');
    console.log('');

    // Demander confirmation
    const confirmation = await new Promise((resolve) => {
        rl.question('Êtes-vous sûr de vouloir continuer ? (tapez "OUI" pour confirmer): ', resolve);
    });

    if (confirmation !== 'OUI') {
        console.log('❌ Opération annulée.');
        rl.close();
        return;
    }

    console.log('');
    console.log('🔍 Vérification de l\'aperçu des données...');

    try {
        // Appel à l'API d'aperçu
        const previewResponse = await fetch('http://localhost:3000/api/admin/reset-data-preview', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // Note: En production, vous devrez fournir un token d'authentification
            }
        });

        if (!previewResponse.ok) {
            throw new Error(`Erreur lors de l'aperçu: ${previewResponse.status}`);
        }

        const previewData = await previewResponse.json();
        
        console.log('📊 Aperçu des données actuelles:');
        console.log(`• Cas régularisés: ${previewData.currentStats.cas.regularises}`);
        console.log(`• Cas non régularisés: ${previewData.currentStats.cas.nonRegularises}`);
        console.log(`• Blocages acceptés: ${previewData.currentStats.blocages.acceptes}`);
        console.log(`• Blocages ajournés: ${previewData.currentStats.blocages.ajournes}`);
        console.log(`• Blocages rejetés: ${previewData.currentStats.blocages.rejetes}`);
        console.log(`• Blocages en attente: ${previewData.currentStats.blocages.attente}`);
        console.log('');
        console.log(`📈 Impact de la réinitialisation:`);
        console.log(`• ${previewData.impact.casAffectes} cas seront affectés`);
        console.log(`• ${previewData.impact.blocagesAffectes} blocages seront affectés`);
        console.log('');

        // Demander une confirmation finale
        const finalConfirmation = await new Promise((resolve) => {
            rl.question('Procéder à la réinitialisation ? (tapez "CONFIRMER" pour exécuter): ', resolve);
        });

        if (finalConfirmation !== 'CONFIRMER') {
            console.log('❌ Opération annulée.');
            rl.close();
            return;
        }

        console.log('');
        console.log('🚀 Exécution de la réinitialisation...');

        // Appel à l'API de réinitialisation
        const resetResponse = await fetch('http://localhost:3000/api/admin/reset-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // Note: En production, vous devrez fournir un token d'authentification
            }
        });

        if (!resetResponse.ok) {
            throw new Error(`Erreur lors de la réinitialisation: ${resetResponse.status}`);
        }

        const resetData = await resetResponse.json();

        if (resetData.success) {
            console.log('');
            console.log('✅ Réinitialisation terminée avec succès !');
            console.log('');
            console.log('📊 Résultats:');
            console.log(`• ${resetData.operations.casUpdated} cas mis à jour`);
            console.log(`• ${resetData.operations.blocagesUpdated} blocages mis à jour`);
            console.log(`• Temps d'exécution: ${resetData.performance.duration}ms`);
            console.log('');
            console.log('🔍 Vérifications:');
            Object.entries(resetData.verifications).forEach(([key, value]) => {
                console.log(`• ${value}`);
            });
            console.log('');
            console.log(resetData.summary.message);
        } else {
            console.log('❌ Erreur lors de la réinitialisation:', resetData.error);
        }

    } catch (error) {
        console.error('❌ Erreur:', error.message);
        console.log('');
        console.log('💡 Assurez-vous que:');
        console.log('• Le serveur Next.js est démarré (npm run dev)');
        console.log('• Vous avez les permissions administrateur');
        console.log('• La base de données est accessible');
    }

    rl.close();
}

// Exécuter le script
resetData().catch(console.error);
