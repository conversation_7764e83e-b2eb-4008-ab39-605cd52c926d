/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/reset-data-preview/route";
exports.ids = ["app/api/admin/reset-data-preview/route"];
exports.modules = {

/***/ "(rsc)/./app/api/admin/reset-data-preview/route.ts":
/*!***************************************************!*\
  !*** ./app/api/admin/reset-data-preview/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n\nasync function GET(request) {\n    try {\n        // Vérification de l'authentification\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Token manquant\"\n            }, {\n                status: 401\n            });\n        }\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)(token);\n        if (!userPayload || userPayload.role !== \"ADMIN\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Accès non autorisé - Admin requis\"\n            }, {\n                status: 403\n            });\n        }\n        console.log(\"👀 Aperçu des données avant réinitialisation...\");\n        // Statistiques actuelles\n        const currentStats = {\n            cas: {\n                total: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count(),\n                regularises: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                    where: {\n                        regularisation: true\n                    }\n                }),\n                nonRegularises: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                    where: {\n                        regularisation: false\n                    }\n                })\n            },\n            blocages: {\n                total: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count(),\n                acceptes: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                    where: {\n                        resolution: \"ACCEPTE\"\n                    }\n                }),\n                ajournes: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                    where: {\n                        resolution: \"AJOURNE\"\n                    }\n                }),\n                rejetes: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                    where: {\n                        resolution: \"REJETE\"\n                    }\n                }),\n                attente: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                    where: {\n                        resolution: \"ATTENTE\"\n                    }\n                })\n            }\n        };\n        // Calcul de l'impact\n        const impact = {\n            casAResetterRegularisation: currentStats.cas.regularises,\n            blocagesAResetterResolution: currentStats.blocages.total - currentStats.blocages.attente,\n            pourcentageCasRegularises: currentStats.cas.total > 0 ? Math.round(currentStats.cas.regularises / currentStats.cas.total * 100) : 0,\n            pourcentageBlocagesNonAttente: currentStats.blocages.total > 0 ? Math.round((currentStats.blocages.total - currentStats.blocages.attente) / currentStats.blocages.total * 100) : 0\n        };\n        // Aperçu des changements\n        const preview = {\n            avant: {\n                cas: {\n                    regularises: currentStats.cas.regularises,\n                    nonRegularises: currentStats.cas.nonRegularises\n                },\n                blocages: {\n                    acceptes: currentStats.blocages.acceptes,\n                    ajournes: currentStats.blocages.ajournes,\n                    rejetes: currentStats.blocages.rejetes,\n                    attente: currentStats.blocages.attente\n                }\n            },\n            apres: {\n                cas: {\n                    regularises: 0,\n                    nonRegularises: currentStats.cas.total // Tous seront à false\n                },\n                blocages: {\n                    acceptes: 0,\n                    ajournes: 0,\n                    rejetes: 0,\n                    attente: currentStats.blocages.total // Tous seront en ATTENTE\n                }\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Aperçu de la réinitialisation des données\",\n            currentStats,\n            impact: {\n                casAffectes: impact.casAResetterRegularisation,\n                blocagesAffectes: impact.blocagesAResetterResolution,\n                pourcentageCasRegularises: impact.pourcentageCasRegularises,\n                pourcentageBlocagesNonAttente: impact.pourcentageBlocagesNonAttente\n            },\n            preview,\n            warnings: [\n                \"⚠️ Cette action est IRRÉVERSIBLE\",\n                \"⚠️ Tous les cas régularisés seront marqués comme non régularisés\",\n                \"⚠️ Toutes les résolutions de blocage seront remises en attente\",\n                \"⚠️ Cette action affectera les statistiques et rapports\",\n                \"⚠️ Assurez-vous d'avoir une sauvegarde de la base de données\"\n            ],\n            recommendations: [\n                \"💾 Effectuez une sauvegarde complète avant de procéder\",\n                \"📊 Exportez les statistiques actuelles si nécessaire\",\n                \"👥 Informez les utilisateurs de cette maintenance\",\n                \"🕐 Effectuez cette opération pendant une période de faible activité\"\n            ],\n            actionRequired: {\n                endpoint: \"/api/admin/reset-data\",\n                method: \"POST\",\n                confirmation: \"Pour procéder, envoyez une requête POST à l'endpoint ci-dessus\"\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('❌ Erreur lors de l\\'aperçu:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Erreur lors de la génération de l'aperçu\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/reset-data-preview/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freset-data-preview%2Froute&page=%2Fapi%2Fadmin%2Freset-data-preview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freset-data-preview%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freset-data-preview%2Froute&page=%2Fapi%2Fadmin%2Freset-data-preview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freset-data-preview%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_admin_reset_data_preview_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/reset-data-preview/route.ts */ \"(rsc)/./app/api/admin/reset-data-preview/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/reset-data-preview/route\",\n        pathname: \"/api/admin/reset-data-preview\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/reset-data-preview/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\admin\\\\reset-data-preview\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_admin_reset_data_preview_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freset-data-preview%2Froute&page=%2Fapi%2Fadmin%2Freset-data-preview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freset-data-preview%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freset-data-preview%2Froute&page=%2Fapi%2Fadmin%2Freset-data-preview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freset-data-preview%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();