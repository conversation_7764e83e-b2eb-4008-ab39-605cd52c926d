import { prisma } from "@/lib/prisma";

export type Resolution = "ATTENTE" | "ACCEPTE" | "AJOURNE" | "REJETE";

/**
 * Met à jour le statut de régularisation d'un cas basé sur les résolutions de ses blocages
 * Règles:
 * - Si tous les blocages sont ACCEPTE -> regularisation = true
 * - Si au moins un blocage est AJOURNE -> regularisation = false
 * - Si tous les blocages sont ATTENTE -> regularisation = false
 */
export async function updateCasRegularisationFromResolutions(
    casId: string
): Promise<void> {
    try {
        // Récupérer tous les blocages du cas avec leurs résolutions
        const blocages = await prisma.blocage.findMany({
            where: { casId },
            select: { resolution: true },
        });

        if (blocages.length === 0) {
            // Aucun blocage, le cas reste non régularisé
            await prisma.cas.update({
                where: { id: casId },
                data: { regularisation: false },
            });
            return;
        }

        const resolutions = blocages.map((b) => b.resolution as Resolution);

        // Déterminer le nouveau statut de régularisation
        let newRegularisationStatus = false;

        if (resolutions.every((resolution) => resolution === "ACCEPTE")) {
            // Tous acceptés -> régularisé
            newRegularisationStatus = true;
        } else {
            // Au moins un ajourné ou en attente -> non régularisé
            newRegularisationStatus = false;
        }

        // Mettre à jour le cas
        await prisma.cas.update({
            where: { id: casId },
            data: { regularisation: newRegularisationStatus },
        });

        console.log(
            `Cas ${casId} mis à jour: regularisation = ${newRegularisationStatus}`
        );
    } catch (error) {
        console.error(`Erreur lors de la mise à jour du cas ${casId}:`, error);
        throw error;
    }
}

/**
 * Calcule les statistiques des cas basées sur les résolutions des blocages
 * Filtre par wilaya selon le rôle de l'utilisateur
 */
export async function getCasStatisticsByResolution(userPayload?: any) {
    try {
        // Construire les conditions de filtrage
        const where: any = {};

        // Filtrage par wilayaId selon le rôle
        if (userPayload) {
            if (userPayload.role === "BASIC" || userPayload.role === "EDITOR") {
                // Pour BASIC et EDITOR, filtrer par leur wilayaId uniquement
                if (
                    userPayload.wilayaId &&
                    !isNaN(Number(userPayload.wilayaId))
                ) {
                    where.wilayaId = Number(userPayload.wilayaId);
                }
            } else if (
                userPayload.role === "ADMIN" ||
                userPayload.role === "VIEWER"
            ) {
                // Pour ADMIN et VIEWER, pas de filtrage par défaut (accès à toutes les wilayas)
                // Le filtrage peut être ajouté via des paramètres si nécessaire
            }
        }

        console.log(
            "📊 getCasStatisticsByResolution - Utilisation de requête SQL optimisée..."
        );
        console.time("resolution-stats-optimized");

        // Version optimisée avec requête SQL brute pour éviter les limites
        let whereClauseSQL = "";
        let params: any[] = [];

        if (where.wilayaId) {
            whereClauseSQL = 'WHERE c."wilayaId" = $1';
            params = [where.wilayaId];
        }

        let casAjournes = 0;
        let casNonExamines = 0;
        let casRegularises = 0;
        let casNonRegularises = 0;
        let casRejetes = 0;
        let totalCas = 0;

        try {
            // Utiliser une approche plus simple et fiable basée sur la logique getCasStatus
            const cas = await prisma.cas.findMany({
                where: whereClause,
                include: {
                    blocage: {
                        select: {
                            resolution: true
                        }
                    }
                }
            });

            totalCas = cas.length;

            // Calculer les statistiques en utilisant la logique officielle
            cas.forEach(c => {
                const resolutions = c.blocage.map(b => b.resolution as Resolution);
                const status = getCasStatus(resolutions);

                switch (status) {
                    case "REGULARISE":
                        casRegularises++;
                        break;
                    case "AJOURNE":
                        casAjournes++;
                        break;
                    case "REJETE":
                        casRejetes++;
                        break;
                    case "NON_EXAMINE":
                        casNonExamines++;
                        break;
                }
            });

            // Les cas non régularisés sont tous sauf les régularisés
            casNonRegularises = totalCas - casRegularises;
                casNonExamines = Math.max(0, casNonExamines);
                casNonRegularises = Math.max(0, casNonRegularises);
            }

            console.log(`📊 ${totalCas} cas traités avec requête optimisée`);
        } catch (sqlError) {
            console.error(
                "Erreur SQL, fallback vers requête simple:",
                sqlError
            );

            // Fallback vers requête simple avec limite
            totalCas = await prisma.cas.count({ where });

            // Estimations simples
            casRegularises = await prisma.cas.count({
                where: { ...where, regularisation: true },
            });

            casNonExamines = totalCas - casRegularises;
        }

        console.timeEnd("resolution-stats-optimized");

        return {
            total: totalCas,
            regularises: casRegularises,
            nonRegularises: casNonRegularises,
            ajournes: casAjournes,
            nonExamines: casNonExamines,
            rejetes: casRejetes,
        };
    } catch (error) {
        console.error("Erreur lors du calcul des statistiques:", error);
        throw error;
    }
}

/**
 * Détermine si un cas est ajourné (au moins un blocage ajourné)
 */
export function isCasAjourne(resolutions: Resolution[]): boolean {
    return resolutions.some((resolution) => resolution === "AJOURNE");
}

/**
 * Détermine si un cas est non examiné (tous les blocages en attente ou aucun blocage)
 */
export function isCasNonExamine(resolutions: Resolution[]): boolean {
    return (
        resolutions.length === 0 ||
        resolutions.every((resolution) => resolution === "ATTENTE")
    );
}

/**
 * Détermine si un cas est régularisé (tous les blocages acceptés)
 */
export function isCasRegularise(resolutions: Resolution[]): boolean {
    return (
        resolutions.length > 0 &&
        resolutions.every((resolution) => resolution === "ACCEPTE")
    );
}

/**
 * Détermine le statut d'un cas basé sur ses résolutions
 * IMPORTANT: Cette fonction définit la logique de priorité officielle
 * Utilisez cette fonction dans tous les filtres pour maintenir la cohérence
 */
export function getCasStatus(
    resolutions: Resolution[]
): "REGULARISE" | "AJOURNE" | "NON_EXAMINE" | "REJETE" {
    if (resolutions.length === 0) {
        return "NON_EXAMINE"; // Cas sans blocage
    } else if (resolutions.every((r) => r === "ATTENTE")) {
        return "NON_EXAMINE"; // Tous en attente
    } else if (resolutions.some((r) => r === "REJETE")) {
        return "REJETE"; // Au moins un rejeté (priorité la plus haute)
    } else if (resolutions.some((r) => r === "AJOURNE")) {
        return "AJOURNE"; // Au moins un ajourné
    } else if (resolutions.every((r) => r === "ACCEPTE")) {
        return "REGULARISE"; // Tous acceptés
    } else {
        return "NON_EXAMINE"; // Cas par défaut
    }
}

/**
 * Filtre les cas par statut en utilisant la logique de priorité officielle
 * Utilisez cette fonction dans toutes les APIs pour maintenir la cohérence
 */
export function filterCasByStatus(
    cas: Array<{ blocage: Array<{ resolution: string }> }>,
    targetStatus: string
): Array<{ blocage: Array<{ resolution: string }> }> {
    if (!["REGULARISE", "AJOURNE", "NON_EXAMINE", "REJETE"].includes(targetStatus)) {
        return cas; // Pas de filtrage si statut invalide
    }

    return cas.filter((c) => {
        const resolutions = c.blocage.map((b) => b.resolution as Resolution);
        const actualStatus = getCasStatus(resolutions);
        return actualStatus === targetStatus;
    });
}
