/**
 * Script pour obtenir un token d'administrateur
 * Usage: node scripts/get-admin-token.js username password
 */

const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

async function getAdminToken() {
    console.log('🔑 Obtention d\'un Token Administrateur');
    console.log('====================================');
    console.log('');

    // Récupérer les arguments ou demander les credentials
    const args = process.argv.slice(2);
    let username = args[0];
    let password = args[1];

    if (!username) {
        username = await new Promise((resolve) => {
            rl.question('Nom d\'utilisateur admin: ', resolve);
        });
    }

    if (!password) {
        password = await new Promise((resolve) => {
            rl.question('Mot de passe: ', resolve);
        });
    }

    try {
        console.log('');
        console.log('🔄 Connexion en cours...');

        const response = await fetch('http://localhost:3000/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username,
                password
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Erreur de connexion (${response.status}): ${errorData}`);
        }

        const data = await response.json();

        if (data.success && data.user && data.user.role === 'ADMIN') {
            // Extraire le token des cookies
            const cookies = response.headers.get('set-cookie');
            let token = null;
            
            if (cookies) {
                const tokenMatch = cookies.match(/token=([^;]+)/);
                if (tokenMatch) {
                    token = tokenMatch[1];
                }
            }

            console.log('');
            console.log('✅ Connexion réussie !');
            console.log('');
            console.log('👤 Utilisateur:', data.user.username);
            console.log('🔐 Rôle:', data.user.role);
            
            if (token) {
                console.log('');
                console.log('🎫 Token d\'authentification:');
                console.log('─'.repeat(50));
                console.log(token);
                console.log('─'.repeat(50));
                console.log('');
                console.log('💡 Utilisation:');
                console.log(`curl -X GET "http://localhost:3000/api/admin/reset-data-preview" \\`);
                console.log(`  -H "Cookie: token=${token}"`);
                console.log('');
                console.log(`curl -X POST "http://localhost:3000/api/admin/reset-data" \\`);
                console.log(`  -H "Cookie: token=${token}"`);
            } else {
                console.log('⚠️ Token non trouvé dans la réponse');
            }

        } else if (data.user && data.user.role !== 'ADMIN') {
            console.log('❌ Erreur: L\'utilisateur n\'a pas les droits administrateur');
            console.log(`Rôle actuel: ${data.user.role}`);
        } else {
            console.log('❌ Erreur de connexion:', data.error || 'Réponse inattendue');
        }

    } catch (error) {
        console.error('❌ Erreur:', error.message);
        console.log('');
        console.log('💡 Assurez-vous que:');
        console.log('• Le serveur Next.js est démarré (npm run dev)');
        console.log('• Les credentials sont corrects');
        console.log('• L\'utilisateur a le rôle ADMIN');
        console.log('• La base de données est accessible');
    }

    rl.close();
}

// Exécuter le script
getAdminToken().catch(console.error);
