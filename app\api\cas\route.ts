import { prisma } from "@/lib/prisma";
import { handleError, forbidden } from "@/lib/api-utils";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth"; // Assuming this is your auth library
import { TypePersonne, Prisma } from "@prisma/client";
import { requireWritePermission } from "@/lib/permissions";

// Get all cas - filtered by user role
export async function GET(req: NextRequest) {
    try {
        const { searchParams } = new URL(req.url);
        const encrageId = searchParams.get("encrageId");
        const problematiqueId = searchParams.get("problematiqueId"); // Lire le problematiqueId
        const withGeojson = searchParams.get("withGeojson") === "true";
        const regularisation = searchParams.get("regularisation");
        const casStatus = searchParams.get("casStatus");
        const wilayaId = searchParams.get("wilayaId");

        // Pagination parameters
        const page = parseInt(searchParams.get("page") || "1");
        const pageSize = parseInt(searchParams.get("pageSize") || "20");
        const search = searchParams.get("search") || "";

        // Validate pagination parameters
        if (page < 1)
            return NextResponse.json(
                { error: "Page must be greater than 0" },
                { status: 400 }
            );
        if (pageSize < 1 || pageSize > 1000)
            return NextResponse.json(
                { error: "Page size must be between 1 and 1000" },
                { status: 400 }
            );

        // Get the current user from the token
        const cookieStore = await cookies(); // Await the cookies() call
        const token = cookieStore.get("token")?.value;
        if (!token) return forbidden();

        const userPayload = await verifyToken(token); // Assuming verifyToken is async
        if (!userPayload) return forbidden();

        // Build the where clause based on filters and user role
        let where: any = {};

        // Si problematiqueId est fourni, il a la priorité pour le filtrage direct des Cas
        if (problematiqueId) {
            where.problematiqueId = problematiqueId;
        } else if (encrageId) {
            // Sinon, si encrageId est fourni, filtre les Cas via l'encrage de leur problématique
            where.problematique = {
                encrageId: encrageId,
            };
        }

        // Filtrage par statut de régularisation
        if (regularisation === "true") {
            where.regularisation = true;
        } else if (regularisation === "false") {
            where.regularisation = false;
        }

        // Si withGeojson est true, ne retourner que les cas avec des coordonnées
        if (withGeojson) {
            where.geojson = {
                not: null,
            };
        }

        // Search functionality
        if (search) {
            where.OR = [
                { nom: { contains: search, mode: "insensitive" } },
                { nif: { contains: search, mode: "insensitive" } },
                { nin: { contains: search, mode: "insensitive" } },
                {
                    communes: {
                        some: {
                            nom: { contains: search, mode: "insensitive" },
                        },
                    },
                },
            ];
        }

        // Filtrage par wilayaId
        if (userPayload.role === "BASIC" || userPayload.role === "EDITOR") {
            // Pour BASIC et EDITOR, filtrer par leur wilayaId uniquement
            if (userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {
                where.wilayaId = Number(userPayload.wilayaId);
            }
        } else if (
            userPayload.role === "ADMIN" ||
            userPayload.role === "VIEWER"
        ) {
            // Pour ADMIN et VIEWER, permettre le filtrage par wilayaId via paramètre
            if (wilayaId && !isNaN(Number(wilayaId))) {
                where.wilayaId = Number(wilayaId);
            }
            // Si pas de filtre wilayaId spécifié et que l'utilisateur a une wilayaId, l'utiliser comme fallback pour VIEWER
            else if (
                userPayload.role === "VIEWER" &&
                userPayload.wilayaId &&
                !isNaN(Number(userPayload.wilayaId))
            ) {
                where.wilayaId = Number(userPayload.wilayaId);
            }
        }

        // Pour le filtre par statut, nous devons récupérer tous les cas d'abord
        // car le statut dépend des résolutions de blocage
        let needsStatusFiltering =
            casStatus &&
            ["REGULARISE", "AJOURNE", "NON_EXAMINE", "REJETE"].includes(
                casStatus
            );

        // Si pas de filtre par statut, on peut utiliser le count normal
        let totalCount: number;
        let totalPages: number;
        let skip: number;

        if (!needsStatusFiltering) {
            totalCount = await prisma.cas.count({ where });
            totalPages = Math.ceil(totalCount / pageSize);
            skip = (page - 1) * pageSize;
        } else {
            // Si filtre par statut, on doit récupérer tous les cas pour les filtrer
            skip = 0; // On récupère tout d'abord
        }

        let cas = await prisma.cas.findMany({
            where,
            include: {
                problematique: {
                    include: {
                        encrage: true,
                    },
                },
                user: {
                    select: {
                        id: true,
                        username: true,
                        role: true,
                    },
                },
                communes: true,
                blocage: {
                    select: {
                        resolution: true,
                    },
                },
            },
            skip: needsStatusFiltering ? 0 : skip, // Si filtre par statut, récupérer tous
            take: needsStatusFiltering ? undefined : pageSize, // Si filtre par statut, pas de limite
            orderBy: {
                createdAt: "desc",
            },
        });

        // Filtrage par statut de cas (après récupération car dépend des résolutions)
        if (needsStatusFiltering) {
            cas = cas.filter((c) => {
                const resolutions = c.blocage.map((b) => b.resolution);

                // Déterminer le statut réel du cas avec logique de priorité
                let actualStatus: string;

                if (resolutions.length === 0) {
                    actualStatus = "NON_EXAMINE"; // Cas sans blocage
                } else if (resolutions.every((r) => r === "ATTENTE")) {
                    actualStatus = "NON_EXAMINE"; // Tous en attente
                } else if (resolutions.some((r) => r === "REJETE")) {
                    actualStatus = "REJETE"; // Au moins un rejeté (priorité la plus haute)
                } else if (resolutions.some((r) => r === "AJOURNE")) {
                    actualStatus = "AJOURNE"; // Au moins un ajourné
                } else if (resolutions.every((r) => r === "ACCEPTE")) {
                    actualStatus = "REGULARISE"; // Tous acceptés
                } else {
                    actualStatus = "NON_EXAMINE"; // Cas par défaut
                }

                // Filtrer selon le statut demandé
                return actualStatus === casStatus;
            });

            // Maintenant calculer la pagination sur les données filtrées
            totalCount = cas.length;
            totalPages = Math.ceil(totalCount / pageSize);

            // Appliquer la pagination sur les données filtrées
            const startIndex = (page - 1) * pageSize;
            cas = cas.slice(startIndex, startIndex + pageSize);
        }

        return NextResponse.json({
            data: cas,
            pagination: {
                page,
                pageSize,
                totalCount,
                totalPages,
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1,
            },
        });
    } catch (error) {
        console.error("ERREUR API_CAS_GET:", error); // Log détaillé de l'erreur côté serveur

        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            // Erreurs connues de Prisma (ex: contrainte violée, enregistrement non trouvé)
            return NextResponse.json(
                {
                    error: `Erreur de base de données (Prisma): ${error.code}`,
                    message: error.message,
                    details: error.meta,
                },
                { status: 500 }
            );
        } else if (error instanceof Prisma.PrismaClientValidationError) {
            // Erreurs de validation de Prisma (ex: type de champ incorrect)
            return NextResponse.json(
                {
                    error: "Erreur de validation des données (Prisma).",
                    message: error.message,
                },
                { status: 400 }
            );
        }
        // Pour les autres types d'erreurs, utilisez le gestionnaire générique ou un message par défaut
        // return handleError(error); // Si handleError est suffisant
        return NextResponse.json(
            {
                error: "Erreur interne du serveur lors de la récupération des cas.",
                message:
                    error instanceof Error
                        ? error.message
                        : "Une erreur inconnue est survenue.",
            },
            { status: 500 }
        );
    }
}

// Create new cas
// Mettre à jour le schéma Zod
const casSchema = z
    .object({
        nom: z.string().min(1, "Le nom est requis."),
        genre: z.nativeEnum(TypePersonne, {
            errorMap: (issue, ctx) => {
                if (issue.code === z.ZodIssueCode.invalid_enum_value) {
                    return { message: "La valeur du genre est invalide." };
                }
                return { message: ctx.defaultError };
            },
        }),
        nif: z.string().optional().nullable(),
        nin: z.string().optional().nullable(),
        superficie: z
            .number()
            .positive("La superficie doit être un nombre positif."),
        // MODIFIÉ: regularisation est maintenant optionnel et défaut à false
        regularisation: z.boolean().optional().default(false),
        observation: z.string().optional().nullable(),
        problematiqueId: z
            .string()
            .cuid("L'ID de la problématique est invalide."),
        communeIds: z
            .array(
                z
                    .string()
                    .regex(
                        /^\d+$/,
                        "Chaque ID de commune doit être une chaîne de chiffres."
                    )
            )
            .min(1, "Au moins une commune doit être sélectionnée."),
        date_depot: z.string().datetime({ offset: true }).optional().nullable(),
        // userId n'est plus attendu du corps de la requête, il sera extrait du token
    })
    .refine(
        (data) => {
            if (data.genre === TypePersonne.PERSONNE_MORALE) {
                return !!data.nif; // NIF requis pour PERSONNE_MORALE
            }
            return true;
        },
        {
            message:
                "Le NIF est requis et doit être valide pour une personne morale.",
            path: ["nif"],
        }
    )
    .refine(
        (data) => {
            if (data.genre === TypePersonne.PERSONNE_PHYSIQUE) {
                return !!data.nin; // NIN requis pour PERSONNE_PHYSIQUE
            }
            return true;
        },
        {
            message:
                "Le NIN est requis et doit être valide pour une personne physique.",
            path: ["nin"],
        }
    );
// La transformation pour genre n'est plus nécessaire ici si le frontend envoie déjà les bonnes valeurs
// et que z.nativeEnum(TypePersonne) est utilisé.

export async function POST(request: NextRequest) {
    try {
        // Check write permissions using the new permission system
        const { hasPermission, user, error } = await requireWritePermission();
        if (!hasPermission || !user) {
            return NextResponse.json(
                { error: error || "Insufficient permissions" },
                { status: 403 }
            );
        }

        const body = await request.json();
        const validation = casSchema.safeParse(body);

        if (!validation.success) {
            console.error("Validation errors:", validation.error.flatten());
            return NextResponse.json(validation.error.flatten(), {
                status: 400,
            });
        }

        // 'userId' est maintenant pris de userPayload.id
        // 'regularisation' est maintenant inclus dans validation.data
        const {
            nom,
            nif,
            nin,
            genre: genreString,
            date_depot,
            superficie,
            regularisation,
            observation,
            problematiqueId,
            communeIds,
        } = validation.data;

        // Utiliser user pour wilayaId - ADMIN peut créer des cas pour toutes les wilayas
        // Note: wilayaId is required in the database, so we need to provide a valid value
        if (!user.wilayaId) {
            return NextResponse.json(
                { error: "WilayaId is required for all users" },
                { status: 400 }
            );
        }

        const wilayaId = user.wilayaId;
        const newCas = await prisma.cas.create({
            data: {
                nom,
                nif: genreString === TypePersonne.PERSONNE_MORALE ? nif : null,
                nin:
                    genreString === TypePersonne.PERSONNE_PHYSIQUE ? nin : null,
                genre: TypePersonne[genreString as keyof typeof TypePersonne],
                date_depot: date_depot
                    ? new Date(date_depot).toISOString()
                    : null,
                superficie,
                regularisation,
                observation,
                problematiqueId,
                userId: user.id,
                wilayaId, // Ajouté ici
                communes: {
                    connect: communeIds.map((id: string) => ({
                        id: parseInt(id),
                    })),
                },
            },
            include: {
                problematique: true,
                user: true,
                communes: true,
            },
        });

        return NextResponse.json(newCas, { status: 201 });
    } catch (error) {
        console.error("POST /api/cas error:", error);
        // Amélioration de la gestion des erreurs Prisma
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            return NextResponse.json(
                { message: error.message, code: error.code, meta: error.meta },
                { status: 400 }
            );
        }
        if (error instanceof Prisma.PrismaClientValidationError) {
            return NextResponse.json(
                { message: error.message },
                { status: 400 }
            );
        }
        return handleError(error);
    }
}
