import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token non fourni" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload) {
            return NextResponse.json(
                { error: "Token invalide" },
                { status: 401 }
            );
        }

        // Récupération des paramètres de requête
        const { searchParams } = new URL(request.url);
        const wilayaId = searchParams.get("wilayaId");

        // Construction de la clause WHERE pour filtrer par wilaya si nécessaire
        let whereClause: any = {};

        // Si l'utilisateur n'est pas ADMIN et a une wilayaId, filtrer par wilaya
        if (userPayload.role !== "ADMIN" && userPayload.wilayaId) {
            whereClause.wilayaId = userPayload.wilayaId;
        } else if (wilayaId) {
            whereClause.wilayaId = parseInt(wilayaId);
        }

        console.log(
            "📊 API /api/stats/cas - Utilisation de requête SQL optimisée..."
        );
        console.time("stats-cas-optimized");

        // Utiliser une approche plus simple et fiable
        let totalCas = 0;
        let casRegularises = 0;

        try {
            // Compter le total des cas
            totalCas = await prisma.cas.count({
                where: whereClause,
            });

            // Compter les cas régularisés (basé sur le champ regularisation)
            casRegularises = await prisma.cas.count({
                where: {
                    ...whereClause,
                    regularisation: true,
                },
            });

            console.log(
                `📊 ${totalCas} cas traités, ${casRegularises} régularisés`
            );
        } catch (error) {
            console.error("Erreur lors du calcul des statistiques:", error);
            throw error;
        }

        console.timeEnd("stats-cas-optimized");
        const casEnAttente = totalCas - casRegularises;

        const stats = {
            total: totalCas,
            regularises: casRegularises,
            enAttente: casEnAttente,
        };

        return NextResponse.json(stats);
    } catch (error) {
        console.error(
            "Erreur lors de la récupération des statistiques des cas:",
            error
        );
        return NextResponse.json(
            { error: "Erreur interne du serveur" },
            { status: 500 }
        );
    }
}
