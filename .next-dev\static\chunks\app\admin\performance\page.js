/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/admin/performance/page"],{

/***/ "(app-pages-browser)/./app/admin/performance/page.tsx":
/*!****************************************!*\
  !*** ./app/admin/performance/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PerformancePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction PerformancePage() {\n    _s();\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [summary, setSummary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [database, setDatabase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [analysis, setAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [targetCas, setTargetCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50000);\n    const [isTesting, setIsTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const testStatsAPIs = async ()=>{\n        setIsTesting(true);\n        setError(null);\n        try {\n            console.log(\"🧪 Test des APIs de statistiques...\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/admin/test-stats-apis\");\n            if (response.success) {\n                console.log(\"✅ Test APIs statistiques réussi:\", response.results);\n                const results = response.results;\n                const successCount = results.filter((r)=>r.success).length;\n                const failCount = results.filter((r)=>!r.success).length;\n                let message = \"✅ Test APIs statistiques termin\\xe9 !\\n\\n\";\n                message += \"\\uD83D\\uDCCA \".concat(successCount, \"/\").concat(results.length, \" APIs fonctionnelles\\n\");\n                message += \"\\uD83D\\uDCC8 Donn\\xe9es de base: \".concat(response.baseCounts.cas, \" cas, \").concat(response.baseCounts.blocages, \" blocages\\n\");\n                message += \"⏱️ Dur\\xe9e: \".concat(response.performance.duration, \"ms\\n\\n\");\n                message += \"D\\xe9tails par API:\\n\";\n                results.forEach((result)=>{\n                    if (result.success) {\n                        message += \"✅ \".concat(result.api, \": \").concat(result.dataCount, \" \\xe9l\\xe9ments\\n\");\n                    } else {\n                        message += \"❌ \".concat(result.api, \": \").concat(result.error, \"\\n\");\n                    }\n                });\n                alert(message);\n                // Mettre à jour les stats de la base\n                if (response.baseCounts) {\n                    setDatabase(response.baseCounts);\n                }\n            } else {\n                setError(\"Test \\xe9chou\\xe9: \".concat(response.error, \"\\nD\\xe9tails: \").concat(response.details || \"Aucun détail\"));\n            }\n        } catch (err) {\n            console.error(\"Erreur lors du test:\", err);\n            setError(\"Test APIs statistiques \\xe9chou\\xe9: \".concat(err.message));\n        } finally{\n            setIsTesting(false);\n        }\n    };\n    const testSimpleStats = async ()=>{\n        setIsTesting(true);\n        setError(null);\n        try {\n            console.log(\"🧪 Test des statistiques simples...\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/test-simple-stats\");\n            if (response.success) {\n                console.log(\"✅ Test statistiques simples réussi:\", response.stats);\n                const stats = response.stats;\n                alert(\"✅ Test statistiques simples r\\xe9ussi !\\n\\n\" + \"\\uD83D\\uDCCA Total cas: \".concat(stats.totalCas.toLocaleString(), \"\\n\") + \"✅ R\\xe9gularis\\xe9s: \".concat(stats.regularises.toLocaleString(), \"\\n\") + \"\\uD83D\\uDEA7 Total blocages: \".concat(stats.totalBlocages.toLocaleString(), \"\\n\") + \"⏱️ Dur\\xe9e: \".concat(response.performance.duration, \"ms\\n\") + \"⏰ \".concat(response.performance.timestamp));\n                // Mettre à jour les stats de la base si disponibles\n                if (stats) {\n                    setDatabase({\n                        cas: stats.totalCas,\n                        blocages: stats.totalBlocages,\n                        secteurs: 0,\n                        problematiques: 0,\n                        users: 0\n                    });\n                }\n            } else {\n                setError(\"Test \\xe9chou\\xe9: \".concat(response.error, \"\\nD\\xe9tails: \").concat(response.details || \"Aucun détail\"));\n            }\n        } catch (err) {\n            console.error(\"Erreur lors du test:\", err);\n            setError(\"Test statistiques \\xe9chou\\xe9: \".concat(err.message));\n        } finally{\n            setIsTesting(false);\n        }\n    };\n    const runFullDiagnostic = async ()=>{\n        setIsTesting(true);\n        setError(null);\n        try {\n            console.log(\"🔍 Lancement du diagnostic complet...\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/admin/debug-api\");\n            if (response.success) {\n                console.log(\"✅ Diagnostic complet réussi:\", response.results);\n                const results = response.results;\n                const dbCounts = results.database.counts;\n                alert(\"✅ Diagnostic complet r\\xe9ussi !\\n\\n\" + \"\\uD83D\\uDC64 Utilisateur: \".concat(results.user.username, \" (\").concat(results.user.role, \")\\n\") + \"\\uD83D\\uDDC4️ Base de donn\\xe9es: Connect\\xe9e\\n\" + \"\\uD83D\\uDCCA Donn\\xe9es: \".concat(dbCounts.cas.toLocaleString(), \" cas, \").concat(dbCounts.blocages.toLocaleString(), \" blocages\\n\") + \"\\uD83D\\uDD17 Tests: JOIN OK, SQL brut OK\\n\" + \"⏰ \".concat(response.timestamp));\n                // Mettre à jour les stats de la base\n                if (results.database.counts) {\n                    setDatabase(results.database.counts);\n                }\n            } else {\n                const errorMsg = \"❌ Diagnostic \\xe9chou\\xe9 \\xe0 l'\\xe9tape: \".concat(response.step, \"\\n\\n\") + \"Erreur: \".concat(response.error, \"\\n\") + \"D\\xe9tails: \".concat(response.details || \"Aucun détail\");\n                console.error(\"❌ Diagnostic échoué:\", response);\n                setError(errorMsg);\n                alert(errorMsg);\n            }\n        } catch (err) {\n            console.error(\"Erreur lors du diagnostic:\", err);\n            const errorMsg = \"\\uD83D\\uDCA5 Erreur fatale lors du diagnostic:\\n\\n\".concat(err.message);\n            setError(errorMsg);\n            alert(errorMsg);\n        } finally{\n            setIsTesting(false);\n        }\n    };\n    const testRealGeneration = async ()=>{\n        setIsTesting(true);\n        setError(null);\n        try {\n            console.log(\"🧪 Test de génération réelle (100 cas)...\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/admin/generate-real-data\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    testCount: 100\n                })\n            });\n            if (response.success) {\n                setDatabase(response.totals);\n                console.log(\"✅ Génération réelle réussie:\", response.message);\n                alert(\"✅ G\\xe9n\\xe9ration r\\xe9elle r\\xe9ussie !\\n\\n\\uD83D\\uDCCA \".concat(response.created.cas, \" cas cr\\xe9\\xe9s\\n\\uD83D\\uDEA7 \").concat(response.created.blocages, \" blocages cr\\xe9\\xe9s\\n\\uD83D\\uDCC8 Total: \").concat(response.totals.cas.toLocaleString(), \" cas dans la base\"));\n            } else {\n                setError(\"G\\xe9n\\xe9ration \\xe9chou\\xe9e: \".concat(response.message || \"Erreur inconnue\"));\n            }\n        } catch (err) {\n            console.error(\"Erreur lors de la génération:\", err);\n            setError(\"Test de g\\xe9n\\xe9ration \\xe9chou\\xe9: \".concat(err.message));\n        } finally{\n            setIsTesting(false);\n        }\n    };\n    const testConnection = async ()=>{\n        setIsTesting(true);\n        setError(null);\n        try {\n            console.log(\"🔍 Test de connexion...\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/admin/simple-test\");\n            if (response.success) {\n                console.log(\"✅ Connexion réussie:\", response.message);\n                alert(\"✅ Test de connexion r\\xe9ussi !\\n\\n\\uD83D\\uDC64 Utilisateur: \".concat(response.user.username, \" (\").concat(response.user.role, \")\\n⏰ Timestamp: \").concat(response.timestamp));\n            } else {\n                setError(\"Test \\xe9chou\\xe9: \".concat(response.message || \"Erreur inconnue\"));\n            }\n        } catch (err) {\n            console.error(\"Erreur lors du test:\", err);\n            setError(\"Test de connexion \\xe9chou\\xe9: \".concat(err.message));\n        } finally{\n            setIsTesting(false);\n        }\n    };\n    const testSmallGeneration = async ()=>{\n        setIsTesting(true);\n        setError(null);\n        try {\n            console.log(\"🧪 Test de génération (10 cas)...\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/admin/simple-test\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    testCount: 10,\n                    action: \"test-generation\"\n                })\n            });\n            if (response.success) {\n                console.log(\"✅ Test POST réussi:\", response.message);\n                alert(\"✅ Test POST r\\xe9ussi !\\n\\n� Utilisateur: \".concat(response.user, \"\\n\\uD83D\\uDCE6 Donn\\xe9es re\\xe7ues: \").concat(JSON.stringify(response.received), \"\\n⏰ \").concat(response.timestamp));\n            } else {\n                setError(\"Test \\xe9chou\\xe9: \".concat(response.message || \"Erreur inconnue\"));\n            }\n        } catch (err) {\n            console.error(\"Erreur lors du test:\", err);\n            setError(\"Test de g\\xe9n\\xe9ration \\xe9chou\\xe9: \".concat(err.message));\n        } finally{\n            setIsTesting(false);\n        }\n    };\n    const generateTestData = async ()=>{\n        setIsGenerating(true);\n        setError(null);\n        try {\n            console.log(\"\\uD83D\\uDE80 G\\xe9n\\xe9ration de \".concat(targetCas.toLocaleString(), \" cas de test...\"));\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/admin/generate-test-data\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    targetCas\n                })\n            });\n            if (response.success) {\n                setDatabase(response.totals);\n                console.log(\"✅ G\\xe9n\\xe9ration termin\\xe9e: \".concat(response.created.cas, \" cas, \").concat(response.created.blocages, \" blocages cr\\xe9\\xe9s\"));\n                alert(\"\\uD83C\\uDF89 G\\xe9n\\xe9ration r\\xe9ussie !\\n\\n\\uD83D\\uDCCA \".concat(response.created.cas.toLocaleString(), \" cas cr\\xe9\\xe9s\\n\\uD83D\\uDEA7 \").concat(response.created.blocages.toLocaleString(), \" blocages cr\\xe9\\xe9s\\n\\uD83D\\uDCE6 \").concat(response.batches, \" batches trait\\xe9s\"));\n            }\n        } catch (err) {\n            var _err_message, _err_message1, _err_message2;\n            console.error(\"Erreur lors de la génération:\", err);\n            console.error(\"Détails de l'erreur:\", {\n                message: err.message,\n                status: err.status,\n                response: err.response\n            });\n            let errorMessage = \"Erreur lors de la génération des données de test\";\n            if ((_err_message = err.message) === null || _err_message === void 0 ? void 0 : _err_message.includes(\"401\")) {\n                errorMessage = \"Erreur d'authentification. Veuillez vous reconnecter.\";\n            } else if ((_err_message1 = err.message) === null || _err_message1 === void 0 ? void 0 : _err_message1.includes(\"403\")) {\n                errorMessage = \"Accès refusé. Vous devez être administrateur.\";\n            } else if ((_err_message2 = err.message) === null || _err_message2 === void 0 ? void 0 : _err_message2.includes(\"400\")) {\n                errorMessage = \"Paramètres invalides. Vérifiez le nombre de cas.\";\n            } else if (err.message) {\n                errorMessage = err.message;\n            }\n            setError(errorMessage);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const runTests = async function() {\n        let testType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"all\";\n        setIsRunning(true);\n        setError(null);\n        setResults([]);\n        setSummary(null);\n        try {\n            console.log(\"\\uD83D\\uDE80 Lancement des tests de performance: \".concat(testType));\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/admin/performance-test?type=\".concat(testType));\n            setResults(response.results);\n            setSummary(response.summary);\n            setDatabase(response.database);\n            setAnalysis(response.analysis);\n            console.log(\"✅ Tests termin\\xe9s: \".concat(response.summary.successfulTests, \"/\").concat(response.summary.totalTests, \" r\\xe9ussis\"));\n        } catch (err) {\n            console.error(\"Erreur lors des tests:\", err);\n            setError(err.message || \"Erreur lors des tests de performance\");\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const getPerformanceColor = (duration)=>{\n        if (duration < 1000) return \"text-green-600\";\n        if (duration < 5000) return \"text-yellow-600\";\n        if (duration < 10000) return \"text-orange-600\";\n        return \"text-red-600\";\n    };\n    const getPerformanceIcon = (duration)=>{\n        if (duration < 1000) return \"🚀\";\n        if (duration < 5000) return \"⚡\";\n        if (duration < 10000) return \"⚠️\";\n        return \"🐌\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-gray-900 mb-8\",\n                    children: \"\\uD83D\\uDE80 Tests de Performance\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 17\n                }, this),\n                database && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-blue-900 mb-4\",\n                            children: \"\\uD83D\\uDCCA Base de donn\\xe9es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: database.cas.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: \"Cas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: database.blocages.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: \"Blocages\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: database.secteurs\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: \"Secteurs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: database.problematiques\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: \"Probl\\xe9matiques\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: database.users\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: \"Utilisateurs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 21\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-yellow-900 mb-4\",\n                            children: \"\\uD83D\\uDD0D Tests de diagnostic\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testConnection,\n                                    disabled: isTesting || isGenerating || isRunning,\n                                    className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                    children: isTesting ? \"🔄 Test...\" : \"🔍 Test Auth\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testSmallGeneration,\n                                    disabled: isTesting || isGenerating || isRunning,\n                                    className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                    children: isTesting ? \"🔄 Test...\" : \"🧪 Test API POST\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testRealGeneration,\n                                    disabled: isTesting || isGenerating || isRunning,\n                                    className: \"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                    children: isTesting ? \"🔄 Test...\" : \"🚀 Test Génération (100 cas)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testStatsAPIs,\n                                    disabled: isTesting || isGenerating || isRunning,\n                                    className: \"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                    children: isTesting ? \"🔄 Test...\" : \"📊 Test APIs Stats\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testSimpleStats,\n                                    disabled: isTesting || isGenerating || isRunning,\n                                    className: \"bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                    children: isTesting ? \"🔄 Test...\" : \"🧪 Test Stats Simples\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: runFullDiagnostic,\n                                    disabled: isTesting || isGenerating || isRunning,\n                                    className: \"bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                    children: isTesting ? \"🔄 Test...\" : \"🔍 Diagnostic Complet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-yellow-700\",\n                            children: \"Utilisez ces tests pour diagnostiquer les probl\\xe8mes avant la g\\xe9n\\xe9ration compl\\xe8te.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                    lineNumber: 501,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"\\uD83D\\uDCCA G\\xe9n\\xe9ration de donn\\xe9es de test\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Nombre de cas \\xe0 g\\xe9n\\xe9rer:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: targetCas,\n                                            onChange: (e)=>setTargetCas(parseInt(e.target.value) || 50000),\n                                            min: \"1000\",\n                                            max: \"100000\",\n                                            step: \"1000\",\n                                            className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-24\",\n                                            disabled: isGenerating\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: generateTestData,\n                                    disabled: isGenerating || isRunning,\n                                    className: \"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                    children: isGenerating ? \"🔄 Génération en cours...\" : \"📊 Générer les données\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mt-2\",\n                            children: \"G\\xe9n\\xe8re des cas fictifs avec leurs contraintes associ\\xe9es pour tester les performances. Chaque cas aura 1-3 contraintes avec des r\\xe9solutions vari\\xe9es.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                    lineNumber: 558,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"\\uD83E\\uDDEA Lancer les tests de performance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>runTests(\"all\"),\n                                    disabled: isRunning,\n                                    className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                    children: isRunning ? \"🔄 Tests en cours...\" : \"🚀 Tous les tests\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>runTests(\"dashboard\"),\n                                    disabled: isRunning,\n                                    className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                    children: \"\\uD83D\\uDCCA Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>runTests(\"listing\"),\n                                    disabled: isRunning,\n                                    className: \"bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                    children: \"\\uD83D\\uDCCB Listing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>runTests(\"stats\"),\n                                    disabled: isRunning,\n                                    className: \"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                    children: \"\\uD83D\\uDCC8 Statistiques\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                    lineNumber: 600,\n                    columnNumber: 17\n                }, this),\n                summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"\\uD83D\\uDCCB R\\xe9sum\\xe9 des tests\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-6 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-600\",\n                                            children: [\n                                                Math.round(summary.totalDuration),\n                                                \"ms\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 646,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"Dur\\xe9e totale\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: summary.totalTests\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"Tests total\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: summary.successfulTests\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"R\\xe9ussis\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: summary.failedTests\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"\\xc9chou\\xe9s\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-600\",\n                                            children: summary.slowTests\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"Lents (5-10s)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: summary.verySlowTests\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"Tr\\xe8s lents (>10s)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                    lineNumber: 640,\n                    columnNumber: 21\n                }, this),\n                analysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border rounded-lg p-6 mb-6 \".concat(analysis.performance === \"excellent\" ? \"bg-green-50 border-green-200\" : analysis.performance === \"good\" ? \"bg-yellow-50 border-yellow-200\" : \"bg-red-50 border-red-200\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: analysis.performance === \"excellent\" ? \"🚀 Performance Excellente\" : analysis.performance === \"good\" ? \"⚡ Performance Correcte\" : \"🐌 Performance à Optimiser\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 25\n                        }, this),\n                        analysis.recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-2\",\n                                    children: \"\\uD83D\\uDCA1 Recommandations:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1\",\n                                    children: analysis.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"text-sm\",\n                                            children: rec\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 45\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 716,\n                            columnNumber: 29\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                    lineNumber: 699,\n                    columnNumber: 21\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-red-900 mb-2\",\n                            children: \"❌ Erreur\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                    lineNumber: 736,\n                    columnNumber: 21\n                }, this),\n                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"\\uD83D\\uDCCA R\\xe9sultats d\\xe9taill\\xe9s\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 747,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Test\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Dur\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"R\\xe9sultats\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"M\\xe9moire\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Statut\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: results.map((result, index)=>{\n                                            var _result_resultCount;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: result.success ? \"\" : \"bg-red-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                        children: result.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium \".concat(getPerformanceColor(result.duration)),\n                                                        children: [\n                                                            getPerformanceIcon(result.duration),\n                                                            \" \",\n                                                            result.duration,\n                                                            \"ms\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: ((_result_resultCount = result.resultCount) === null || _result_resultCount === void 0 ? void 0 : _result_resultCount.toLocaleString()) || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: result.memoryUsage ? \"\".concat(Math.round(result.memoryUsage.heapUsed / 1024 / 1024), \"MB\") : \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: result.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                            children: \"✅ R\\xe9ussi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 53\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\",\n                                                            children: \"❌ \\xc9chou\\xe9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 53\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                        lineNumber: 808,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 41\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                                lineNumber: 751,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                            lineNumber: 750,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n                    lineNumber: 746,\n                    columnNumber: 21\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n            lineNumber: 446,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\",\n        lineNumber: 445,\n        columnNumber: 9\n    }, this);\n}\n_s(PerformancePage, \"hdd9mTZFh7tV/odj0rEoKF4ilLY=\");\n_c = PerformancePage;\nvar _c;\n$RefreshReg$(_c, \"PerformancePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/performance/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   fetchApi: () => (/* binding */ fetchApi),\n/* harmony export */   getCas: () => (/* binding */ getCas),\n/* harmony export */   getChat: () => (/* binding */ getChat),\n/* harmony export */   sendMessage: () => (/* binding */ sendMessage)\n/* harmony export */ });\nasync function fetchApi(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { method = \"GET\", body } = options;\n    const response = await fetch(endpoint, {\n        method,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...options.headers\n        },\n        credentials: options.credentials || \"include\",\n        body: body ? JSON.stringify(body) : undefined\n    });\n    if (!response.ok) {\n        let errorMessage = \"HTTP error! status: \".concat(response.status);\n        let errorData = null;\n        try {\n            // Read the response body as text first\n            const errorText = await response.text();\n            if (errorText) {\n                try {\n                    // Try to parse the text as JSON\n                    errorData = JSON.parse(errorText);\n                    errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.error) || (errorData === null || errorData === void 0 ? void 0 : errorData.message) || errorText;\n                } catch (parseError) {\n                    // If parsing fails, use the raw text as the error message\n                    errorMessage = errorText || errorMessage;\n                }\n            }\n        } catch (readError) {\n            // If we can't read the response body, use the status\n            console.warn(\"Could not read error response body:\", readError);\n        }\n        // Log error details for debugging (but don't throw console.error)\n        if (true) {\n            console.warn(\"API Error Details:\", {\n                url: response.url,\n                status: response.status,\n                statusText: response.statusText,\n                errorMessage,\n                errorData\n            });\n        }\n        // Handle specific error cases\n        if (response.status === 401) {\n            throw new Error(\"Authentication required. Please log in again.\");\n        } else if (response.status === 403) {\n            throw new Error(\"Access denied. You don't have permission to perform this action.\");\n        } else if (response.status === 404) {\n            throw new Error(\"Resource not found.\");\n        } else if (response.status >= 500) {\n            throw new Error(\"Server error. Please try again later.\");\n        }\n        throw new Error(errorMessage);\n    }\n    if (response.status === 204) {\n        // No Content\n        return null;\n    }\n    return response.json();\n}\n// Add the following apiClient export:\nconst apiClient = {\n    get: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"GET\"\n        });\n    },\n    post: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"POST\",\n            body\n        });\n    },\n    put: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"PUT\",\n            body\n        });\n    },\n    delete: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"DELETE\"\n        });\n    }\n};\n// Chat Functions\nconst getChat = (casId)=>{\n    return apiClient.get(\"/api/chats/\".concat(casId));\n};\nconst sendMessage = (casId, content)=>{\n    return apiClient.post(\"/api/chats/\".concat(casId, \"/messages\"), {\n        content\n    });\n};\nasync function fetchPage(page, pageSize) {\n    const response = await fetch(\"/api/cas?page=\".concat(page, \"&pageSize=\").concat(pageSize), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        credentials: \"include\"\n    });\n    if (!response.ok) {\n        let errorMessage = \"API request failed with status \".concat(response.status);\n        try {\n            const errorText = await response.text();\n            if (errorText) {\n                try {\n                    const errorData = JSON.parse(errorText);\n                    errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.error) || (errorData === null || errorData === void 0 ? void 0 : errorData.message) || errorText;\n                } catch (e) {\n                    errorMessage = errorText;\n                }\n            }\n        } catch (e) {\n        // If we can't read the response, use the default message\n        }\n        if (true) {\n            console.warn(\"getCas API Error:\", {\n                status: response.status,\n                statusText: response.statusText,\n                url: response.url,\n                errorMessage\n            });\n        }\n        throw new Error(errorMessage);\n    }\n    return response.json();\n}\nasync function getCas() {\n    try {\n        console.log(\"Fetching dossiers from /api/cas...\");\n        const pageSize = 100; // Maximum allowed by the API\n        let currentPage = 1;\n        let allDossiers = [];\n        let hasMorePages = true;\n        let totalPages = 1;\n        // Fetch all pages\n        while(hasMorePages && currentPage <= 20){\n            // Add a safety limit of 20 pages\n            console.log(\"Fetching page \".concat(currentPage, \"...\"));\n            const result = await fetchPage(currentPage, pageSize);\n            if (!result.data || !Array.isArray(result.data)) {\n                if (true) {\n                    console.warn(\"Invalid data format in page\", currentPage, \":\", result);\n                }\n                throw new Error(\"Invalid data format received from server\");\n            }\n            allDossiers = [\n                ...allDossiers,\n                ...result.data\n            ];\n            totalPages = result.pagination.totalPages;\n            hasMorePages = result.pagination.hasNextPage && currentPage < totalPages;\n            currentPage++;\n            // If we've fetched all pages or reached the safety limit, stop\n            if (!hasMorePages || currentPage > totalPages) {\n                break;\n            }\n        }\n        console.log(\"Fetched \".concat(allDossiers.length, \" dossiers from \").concat(currentPage - 1, \" pages\"));\n        return allDossiers;\n    } catch (error) {\n        if (true) {\n            if (error instanceof Error) {\n                console.warn(\"Error in getCas:\", {\n                    message: error.message,\n                    name: error.name\n                });\n            } else {\n                console.warn(\"Unknown error in getCas:\", error);\n            }\n        }\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9hcGktY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBT08sZUFBZUEsU0FDbEJDLFFBQWdCO1FBQ2hCQyxVQUFBQSxpRUFBd0IsQ0FBQztJQUV6QixNQUFNLEVBQUVDLFNBQVMsS0FBSyxFQUFFQyxJQUFJLEVBQUUsR0FBR0Y7SUFFakMsTUFBTUcsV0FBVyxNQUFNQyxNQUFNTCxVQUFVO1FBQ25DRTtRQUNBSSxTQUFTO1lBQ0wsZ0JBQWdCO1lBQ2hCLEdBQUdMLFFBQVFLLE9BQU87UUFDdEI7UUFDQUMsYUFBYU4sUUFBUU0sV0FBVyxJQUFJO1FBQ3BDSixNQUFNQSxPQUFPSyxLQUFLQyxTQUFTLENBQUNOLFFBQVFPO0lBQ3hDO0lBRUEsSUFBSSxDQUFDTixTQUFTTyxFQUFFLEVBQUU7UUFDZCxJQUFJQyxlQUFlLHVCQUF1QyxPQUFoQlIsU0FBU1MsTUFBTTtRQUN6RCxJQUFJQyxZQUFpQjtRQUVyQixJQUFJO1lBQ0EsdUNBQXVDO1lBQ3ZDLE1BQU1DLFlBQVksTUFBTVgsU0FBU1ksSUFBSTtZQUVyQyxJQUFJRCxXQUFXO2dCQUNYLElBQUk7b0JBQ0EsZ0NBQWdDO29CQUNoQ0QsWUFBWU4sS0FBS1MsS0FBSyxDQUFDRjtvQkFDdkJILGVBQ0lFLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV0ksS0FBSyxNQUFJSixzQkFBQUEsZ0NBQUFBLFVBQVdLLE9BQU8sS0FBSUo7Z0JBQ2xELEVBQUUsT0FBT0ssWUFBWTtvQkFDakIsMERBQTBEO29CQUMxRFIsZUFBZUcsYUFBYUg7Z0JBQ2hDO1lBQ0o7UUFDSixFQUFFLE9BQU9TLFdBQVc7WUFDaEIscURBQXFEO1lBQ3JEQyxRQUFRQyxJQUFJLENBQUMsdUNBQXVDRjtRQUN4RDtRQUVBLGtFQUFrRTtRQUNsRSxJQUFJRyxJQUFzQyxFQUFFO1lBQ3hDRixRQUFRQyxJQUFJLENBQUMsc0JBQXNCO2dCQUMvQkUsS0FBS3JCLFNBQVNxQixHQUFHO2dCQUNqQlosUUFBUVQsU0FBU1MsTUFBTTtnQkFDdkJhLFlBQVl0QixTQUFTc0IsVUFBVTtnQkFDL0JkO2dCQUNBRTtZQUNKO1FBQ0o7UUFFQSw4QkFBOEI7UUFDOUIsSUFBSVYsU0FBU1MsTUFBTSxLQUFLLEtBQUs7WUFDekIsTUFBTSxJQUFJYyxNQUFNO1FBQ3BCLE9BQU8sSUFBSXZCLFNBQVNTLE1BQU0sS0FBSyxLQUFLO1lBQ2hDLE1BQU0sSUFBSWMsTUFDTjtRQUVSLE9BQU8sSUFBSXZCLFNBQVNTLE1BQU0sS0FBSyxLQUFLO1lBQ2hDLE1BQU0sSUFBSWMsTUFBTTtRQUNwQixPQUFPLElBQUl2QixTQUFTUyxNQUFNLElBQUksS0FBSztZQUMvQixNQUFNLElBQUljLE1BQU07UUFDcEI7UUFFQSxNQUFNLElBQUlBLE1BQU1mO0lBQ3BCO0lBRUEsSUFBSVIsU0FBU1MsTUFBTSxLQUFLLEtBQUs7UUFDekIsYUFBYTtRQUNiLE9BQU87SUFDWDtJQUVBLE9BQU9ULFNBQVN3QixJQUFJO0FBQ3hCO0FBRUEsc0NBQXNDO0FBQy9CLE1BQU1DLFlBQVk7SUFDckJDLEtBQUssQ0FDRDlCLFVBQ0FDO1FBRUEsT0FBT0YsU0FBWUMsVUFBVTtZQUFFLEdBQUdDLE9BQU87WUFBRUMsUUFBUTtRQUFNO0lBQzdEO0lBQ0E2QixNQUFNLENBQ0YvQixVQUNBRyxNQUNBRjtRQUVBLE9BQU9GLFNBQVlDLFVBQVU7WUFBRSxHQUFHQyxPQUFPO1lBQUVDLFFBQVE7WUFBUUM7UUFBSztJQUNwRTtJQUNBNkIsS0FBSyxDQUNEaEMsVUFDQUcsTUFDQUY7UUFFQSxPQUFPRixTQUFZQyxVQUFVO1lBQUUsR0FBR0MsT0FBTztZQUFFQyxRQUFRO1lBQU9DO1FBQUs7SUFDbkU7SUFDQThCLFFBQVEsQ0FDSmpDLFVBQ0FDO1FBRUEsT0FBT0YsU0FBWUMsVUFBVTtZQUFFLEdBQUdDLE9BQU87WUFBRUMsUUFBUTtRQUFTO0lBQ2hFO0FBS0osRUFBRTtBQTJCRixpQkFBaUI7QUFDVixNQUFNZ0MsVUFBVSxDQUFDQztJQUNwQixPQUFPTixVQUFVQyxHQUFHLENBQUMsY0FBb0IsT0FBTks7QUFDdkMsRUFBRTtBQUVLLE1BQU1DLGNBQWMsQ0FDdkJELE9BQ0FFO0lBRUEsT0FBT1IsVUFBVUUsSUFBSSxDQUFDLGNBQW9CLE9BQU5JLE9BQU0sY0FBWTtRQUFFRTtJQUFRO0FBQ3BFLEVBQUU7QUFjRixlQUFlQyxVQUNYQyxJQUFZLEVBQ1pDLFFBQWdCO0lBRWhCLE1BQU1wQyxXQUFXLE1BQU1DLE1BQU0saUJBQWtDbUMsT0FBakJELE1BQUssY0FBcUIsT0FBVEMsV0FBWTtRQUN2RXRDLFFBQVE7UUFDUkksU0FBUztZQUNMLGdCQUFnQjtRQUNwQjtRQUNBQyxhQUFhO0lBQ2pCO0lBRUEsSUFBSSxDQUFDSCxTQUFTTyxFQUFFLEVBQUU7UUFDZCxJQUFJQyxlQUFlLGtDQUFrRCxPQUFoQlIsU0FBU1MsTUFBTTtRQUVwRSxJQUFJO1lBQ0EsTUFBTUUsWUFBWSxNQUFNWCxTQUFTWSxJQUFJO1lBQ3JDLElBQUlELFdBQVc7Z0JBQ1gsSUFBSTtvQkFDQSxNQUFNRCxZQUFZTixLQUFLUyxLQUFLLENBQUNGO29CQUM3QkgsZUFDSUUsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXSSxLQUFLLE1BQUlKLHNCQUFBQSxnQ0FBQUEsVUFBV0ssT0FBTyxLQUFJSjtnQkFDbEQsRUFBRSxVQUFNO29CQUNKSCxlQUFlRztnQkFDbkI7WUFDSjtRQUNKLEVBQUUsVUFBTTtRQUNKLHlEQUF5RDtRQUM3RDtRQUVBLElBQUlTLElBQXNDLEVBQUU7WUFDeENGLFFBQVFDLElBQUksQ0FBQyxxQkFBcUI7Z0JBQzlCVixRQUFRVCxTQUFTUyxNQUFNO2dCQUN2QmEsWUFBWXRCLFNBQVNzQixVQUFVO2dCQUMvQkQsS0FBS3JCLFNBQVNxQixHQUFHO2dCQUNqQmI7WUFDSjtRQUNKO1FBRUEsTUFBTSxJQUFJZSxNQUFNZjtJQUNwQjtJQUVBLE9BQU9SLFNBQVN3QixJQUFJO0FBQ3hCO0FBRU8sZUFBZWE7SUFDbEIsSUFBSTtRQUNBbkIsUUFBUW9CLEdBQUcsQ0FBQztRQUNaLE1BQU1GLFdBQVcsS0FBSyw2QkFBNkI7UUFDbkQsSUFBSUcsY0FBYztRQUNsQixJQUFJQyxjQUFxQixFQUFFO1FBQzNCLElBQUlDLGVBQWU7UUFDbkIsSUFBSUMsYUFBYTtRQUVqQixrQkFBa0I7UUFDbEIsTUFBT0QsZ0JBQWdCRixlQUFlLEdBQUk7WUFDdEMsaUNBQWlDO1lBQ2pDckIsUUFBUW9CLEdBQUcsQ0FBQyxpQkFBNkIsT0FBWkMsYUFBWTtZQUN6QyxNQUFNSSxTQUFTLE1BQU1ULFVBQVVLLGFBQWFIO1lBRTVDLElBQUksQ0FBQ08sT0FBT0MsSUFBSSxJQUFJLENBQUNDLE1BQU1DLE9BQU8sQ0FBQ0gsT0FBT0MsSUFBSSxHQUFHO2dCQUM3QyxJQUFJeEIsSUFBc0MsRUFBRTtvQkFDeENGLFFBQVFDLElBQUksQ0FDUiwrQkFDQW9CLGFBQ0EsS0FDQUk7Z0JBRVI7Z0JBQ0EsTUFBTSxJQUFJcEIsTUFBTTtZQUNwQjtZQUVBaUIsY0FBYzttQkFBSUE7bUJBQWdCRyxPQUFPQyxJQUFJO2FBQUM7WUFDOUNGLGFBQWFDLE9BQU9JLFVBQVUsQ0FBQ0wsVUFBVTtZQUN6Q0QsZUFDSUUsT0FBT0ksVUFBVSxDQUFDQyxXQUFXLElBQUlULGNBQWNHO1lBQ25ESDtZQUVBLCtEQUErRDtZQUMvRCxJQUFJLENBQUNFLGdCQUFnQkYsY0FBY0csWUFBWTtnQkFDM0M7WUFDSjtRQUNKO1FBRUF4QixRQUFRb0IsR0FBRyxDQUNQLFdBQ0lDLE9BRE9DLFlBQVlTLE1BQU0sRUFBQyxtQkFFN0IsT0FER1YsY0FBYyxHQUNqQjtRQUVMLE9BQU9DO0lBQ1gsRUFBRSxPQUFPMUIsT0FBZ0I7UUFDckIsSUFBSU0sSUFBc0MsRUFBRTtZQUN4QyxJQUFJTixpQkFBaUJTLE9BQU87Z0JBQ3hCTCxRQUFRQyxJQUFJLENBQUMsb0JBQW9CO29CQUM3QkosU0FBU0QsTUFBTUMsT0FBTztvQkFDdEJtQyxNQUFNcEMsTUFBTW9DLElBQUk7Z0JBQ3BCO1lBQ0osT0FBTztnQkFDSGhDLFFBQVFDLElBQUksQ0FBQyw0QkFBNEJMO1lBQzdDO1FBQ0o7UUFDQSxNQUFNQTtJQUNWO0FBQ0oiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxhcGktY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbInR5cGUgRmV0Y2hPcHRpb25zID0ge1xuICAgIG1ldGhvZD86IFwiR0VUXCIgfCBcIlBPU1RcIiB8IFwiUFVUXCIgfCBcIkRFTEVURVwiIHwgXCJQQVRDSFwiOyAvLyBBZGRlZCBQQVRDSCBhcyBhbiBleGFtcGxlXG4gICAgYm9keT86IGFueTtcbiAgICBoZWFkZXJzPzogUmVjb3JkPHN0cmluZywgc3RyaW5nPjsgLy8gQWRkZWRcbiAgICBjcmVkZW50aWFscz86IFwiaW5jbHVkZVwiIHwgXCJzYW1lLW9yaWdpblwiIHwgXCJvbWl0XCI7IC8vIEFkZGVkXG59O1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZmV0Y2hBcGk8VD4oXG4gICAgZW5kcG9pbnQ6IHN0cmluZyxcbiAgICBvcHRpb25zOiBGZXRjaE9wdGlvbnMgPSB7fVxuKTogUHJvbWlzZTxUPiB7XG4gICAgY29uc3QgeyBtZXRob2QgPSBcIkdFVFwiLCBib2R5IH0gPSBvcHRpb25zO1xuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChlbmRwb2ludCwge1xuICAgICAgICBtZXRob2QsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICAgICAgLi4ub3B0aW9ucy5oZWFkZXJzLCAvLyBBbGxvdyBwYXNzaW5nIGN1c3RvbSBoZWFkZXJzXG4gICAgICAgIH0sXG4gICAgICAgIGNyZWRlbnRpYWxzOiBvcHRpb25zLmNyZWRlbnRpYWxzIHx8IFwiaW5jbHVkZVwiLCAvLyBJbmNsdWRlIGNyZWRlbnRpYWxzIChjb29raWVzKSB3aXRoIGV2ZXJ5IHJlcXVlc3RcbiAgICAgICAgYm9keTogYm9keSA/IEpTT04uc3RyaW5naWZ5KGJvZHkpIDogdW5kZWZpbmVkLFxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBsZXQgZXJyb3JNZXNzYWdlID0gYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YDtcbiAgICAgICAgbGV0IGVycm9yRGF0YTogYW55ID0gbnVsbDtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gUmVhZCB0aGUgcmVzcG9uc2UgYm9keSBhcyB0ZXh0IGZpcnN0XG4gICAgICAgICAgICBjb25zdCBlcnJvclRleHQgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XG5cbiAgICAgICAgICAgIGlmIChlcnJvclRleHQpIHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAvLyBUcnkgdG8gcGFyc2UgdGhlIHRleHQgYXMgSlNPTlxuICAgICAgICAgICAgICAgICAgICBlcnJvckRhdGEgPSBKU09OLnBhcnNlKGVycm9yVGV4dCk7XG4gICAgICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvckRhdGE/LmVycm9yIHx8IGVycm9yRGF0YT8ubWVzc2FnZSB8fCBlcnJvclRleHQ7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiBwYXJzaW5nIGZhaWxzLCB1c2UgdGhlIHJhdyB0ZXh0IGFzIHRoZSBlcnJvciBtZXNzYWdlXG4gICAgICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yVGV4dCB8fCBlcnJvck1lc3NhZ2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChyZWFkRXJyb3IpIHtcbiAgICAgICAgICAgIC8vIElmIHdlIGNhbid0IHJlYWQgdGhlIHJlc3BvbnNlIGJvZHksIHVzZSB0aGUgc3RhdHVzXG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXCJDb3VsZCBub3QgcmVhZCBlcnJvciByZXNwb25zZSBib2R5OlwiLCByZWFkRXJyb3IpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gTG9nIGVycm9yIGRldGFpbHMgZm9yIGRlYnVnZ2luZyAoYnV0IGRvbid0IHRocm93IGNvbnNvbGUuZXJyb3IpXG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZlbG9wbWVudFwiKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXCJBUEkgRXJyb3IgRGV0YWlsczpcIiwge1xuICAgICAgICAgICAgICAgIHVybDogcmVzcG9uc2UudXJsLFxuICAgICAgICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgICAgICAgIHN0YXR1c1RleHQ6IHJlc3BvbnNlLnN0YXR1c1RleHQsXG4gICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlLFxuICAgICAgICAgICAgICAgIGVycm9yRGF0YSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gSGFuZGxlIHNwZWNpZmljIGVycm9yIGNhc2VzXG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQXV0aGVudGljYXRpb24gcmVxdWlyZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uXCIpO1xuICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAzKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICAgICAgXCJBY2Nlc3MgZGVuaWVkLiBZb3UgZG9uJ3QgaGF2ZSBwZXJtaXNzaW9uIHRvIHBlcmZvcm0gdGhpcyBhY3Rpb24uXCJcbiAgICAgICAgICAgICk7XG4gICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDQpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIlJlc291cmNlIG5vdCBmb3VuZC5cIik7XG4gICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2Uuc3RhdHVzID49IDUwMCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiU2VydmVyIGVycm9yLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLlwiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgIH1cblxuICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDIwNCkge1xuICAgICAgICAvLyBObyBDb250ZW50XG4gICAgICAgIHJldHVybiBudWxsIGFzIFQ7XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlc3BvbnNlLmpzb24oKTtcbn1cblxuLy8gQWRkIHRoZSBmb2xsb3dpbmcgYXBpQ2xpZW50IGV4cG9ydDpcbmV4cG9ydCBjb25zdCBhcGlDbGllbnQgPSB7XG4gICAgZ2V0OiA8VD4oXG4gICAgICAgIGVuZHBvaW50OiBzdHJpbmcsXG4gICAgICAgIG9wdGlvbnM/OiBPbWl0PEZldGNoT3B0aW9ucywgXCJtZXRob2RcIiB8IFwiYm9keVwiPlxuICAgICk6IFByb21pc2U8VD4gPT4ge1xuICAgICAgICByZXR1cm4gZmV0Y2hBcGk8VD4oZW5kcG9pbnQsIHsgLi4ub3B0aW9ucywgbWV0aG9kOiBcIkdFVFwiIH0pO1xuICAgIH0sXG4gICAgcG9zdDogPFQ+KFxuICAgICAgICBlbmRwb2ludDogc3RyaW5nLFxuICAgICAgICBib2R5OiBhbnksXG4gICAgICAgIG9wdGlvbnM/OiBPbWl0PEZldGNoT3B0aW9ucywgXCJtZXRob2RcIiB8IFwiYm9keVwiPlxuICAgICk6IFByb21pc2U8VD4gPT4ge1xuICAgICAgICByZXR1cm4gZmV0Y2hBcGk8VD4oZW5kcG9pbnQsIHsgLi4ub3B0aW9ucywgbWV0aG9kOiBcIlBPU1RcIiwgYm9keSB9KTtcbiAgICB9LFxuICAgIHB1dDogPFQ+KFxuICAgICAgICBlbmRwb2ludDogc3RyaW5nLFxuICAgICAgICBib2R5OiBhbnksXG4gICAgICAgIG9wdGlvbnM/OiBPbWl0PEZldGNoT3B0aW9ucywgXCJtZXRob2RcIiB8IFwiYm9keVwiPlxuICAgICk6IFByb21pc2U8VD4gPT4ge1xuICAgICAgICByZXR1cm4gZmV0Y2hBcGk8VD4oZW5kcG9pbnQsIHsgLi4ub3B0aW9ucywgbWV0aG9kOiBcIlBVVFwiLCBib2R5IH0pO1xuICAgIH0sXG4gICAgZGVsZXRlOiA8VD4oXG4gICAgICAgIGVuZHBvaW50OiBzdHJpbmcsXG4gICAgICAgIG9wdGlvbnM/OiBPbWl0PEZldGNoT3B0aW9ucywgXCJtZXRob2RcIiB8IFwiYm9keVwiPlxuICAgICk6IFByb21pc2U8VD4gPT4ge1xuICAgICAgICByZXR1cm4gZmV0Y2hBcGk8VD4oZW5kcG9pbnQsIHsgLi4ub3B0aW9ucywgbWV0aG9kOiBcIkRFTEVURVwiIH0pO1xuICAgIH0sXG4gICAgLy8gWW91IGNhbiBhZGQgb3RoZXIgSFRUUCBtZXRob2RzIGxpa2UgUEFUQ0ggaWYgbmVlZGVkXG4gICAgLy8gcGF0Y2g6IDxUPihlbmRwb2ludDogc3RyaW5nLCBib2R5OiBhbnksIG9wdGlvbnM/OiBPbWl0PEZldGNoT3B0aW9ucywgJ21ldGhvZCcgfCAnYm9keSc+KTogUHJvbWlzZTxUPiA9PiB7XG4gICAgLy8gICByZXR1cm4gZmV0Y2hBcGk8VD4oZW5kcG9pbnQsIHsgLi4ub3B0aW9ucywgbWV0aG9kOiAnUEFUQ0gnLCBib2R5IH0pO1xuICAgIC8vIH0sXG59O1xuXG4vLyBUeXBlc1xuZXhwb3J0IGludGVyZmFjZSBDaGF0IHtcbiAgICBpZDogc3RyaW5nO1xuICAgIG1lc3NhZ2VzOiBNZXNzYWdlW107XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTWVzc2FnZSB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBjb250ZW50OiBzdHJpbmc7XG4gICAgdXNlcklkOiBzdHJpbmc7XG4gICAgdXNlcjoge1xuICAgICAgICB1c2VybmFtZTogc3RyaW5nO1xuICAgIH07XG4gICAgY3JlYXRlZEF0OiBEYXRlO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENhcyB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBub206IHN0cmluZztcbiAgICBjb21tdW5lPzoge1xuICAgICAgICBpZDogc3RyaW5nO1xuICAgICAgICBub206IHN0cmluZztcbiAgICB9O1xufVxuXG4vLyBDaGF0IEZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IGdldENoYXQgPSAoY2FzSWQ6IHN0cmluZyk6IFByb21pc2U8Q2hhdD4gPT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQuZ2V0KGAvYXBpL2NoYXRzLyR7Y2FzSWR9YCk7XG59O1xuXG5leHBvcnQgY29uc3Qgc2VuZE1lc3NhZ2UgPSAoXG4gICAgY2FzSWQ6IHN0cmluZyxcbiAgICBjb250ZW50OiBzdHJpbmdcbik6IFByb21pc2U8TWVzc2FnZT4gPT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucG9zdChgL2FwaS9jaGF0cy8ke2Nhc0lkfS9tZXNzYWdlc2AsIHsgY29udGVudCB9KTtcbn07XG5cbmludGVyZmFjZSBQYWdpbmF0ZWRSZXNwb25zZTxUPiB7XG4gICAgZGF0YTogVFtdO1xuICAgIHBhZ2luYXRpb246IHtcbiAgICAgICAgcGFnZTogbnVtYmVyO1xuICAgICAgICBwYWdlU2l6ZTogbnVtYmVyO1xuICAgICAgICB0b3RhbENvdW50OiBudW1iZXI7XG4gICAgICAgIHRvdGFsUGFnZXM6IG51bWJlcjtcbiAgICAgICAgaGFzTmV4dFBhZ2U6IGJvb2xlYW47XG4gICAgICAgIGhhc1ByZXZQYWdlOiBib29sZWFuO1xuICAgIH07XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGZldGNoUGFnZShcbiAgICBwYWdlOiBudW1iZXIsXG4gICAgcGFnZVNpemU6IG51bWJlclxuKTogUHJvbWlzZTxQYWdpbmF0ZWRSZXNwb25zZTxDYXM+PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9jYXM/cGFnZT0ke3BhZ2V9JnBhZ2VTaXplPSR7cGFnZVNpemV9YCwge1xuICAgICAgICBtZXRob2Q6IFwiR0VUXCIsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICB9LFxuICAgICAgICBjcmVkZW50aWFsczogXCJpbmNsdWRlXCIsXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSBgQVBJIHJlcXVlc3QgZmFpbGVkIHdpdGggc3RhdHVzICR7cmVzcG9uc2Uuc3RhdHVzfWA7XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcbiAgICAgICAgICAgIGlmIChlcnJvclRleHQpIHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBKU09OLnBhcnNlKGVycm9yVGV4dCk7XG4gICAgICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvckRhdGE/LmVycm9yIHx8IGVycm9yRGF0YT8ubWVzc2FnZSB8fCBlcnJvclRleHQ7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yVGV4dDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2gge1xuICAgICAgICAgICAgLy8gSWYgd2UgY2FuJ3QgcmVhZCB0aGUgcmVzcG9uc2UsIHVzZSB0aGUgZGVmYXVsdCBtZXNzYWdlXG4gICAgICAgIH1cblxuICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwiZGV2ZWxvcG1lbnRcIikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKFwiZ2V0Q2FzIEFQSSBFcnJvcjpcIiwge1xuICAgICAgICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgICAgICAgIHN0YXR1c1RleHQ6IHJlc3BvbnNlLnN0YXR1c1RleHQsXG4gICAgICAgICAgICAgICAgdXJsOiByZXNwb25zZS51cmwsXG4gICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cblxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICB9XG5cbiAgICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0Q2FzKCk6IFByb21pc2U8Q2FzW10+IHtcbiAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZyhcIkZldGNoaW5nIGRvc3NpZXJzIGZyb20gL2FwaS9jYXMuLi5cIik7XG4gICAgICAgIGNvbnN0IHBhZ2VTaXplID0gMTAwOyAvLyBNYXhpbXVtIGFsbG93ZWQgYnkgdGhlIEFQSVxuICAgICAgICBsZXQgY3VycmVudFBhZ2UgPSAxO1xuICAgICAgICBsZXQgYWxsRG9zc2llcnM6IENhc1tdID0gW107XG4gICAgICAgIGxldCBoYXNNb3JlUGFnZXMgPSB0cnVlO1xuICAgICAgICBsZXQgdG90YWxQYWdlcyA9IDE7XG5cbiAgICAgICAgLy8gRmV0Y2ggYWxsIHBhZ2VzXG4gICAgICAgIHdoaWxlIChoYXNNb3JlUGFnZXMgJiYgY3VycmVudFBhZ2UgPD0gMjApIHtcbiAgICAgICAgICAgIC8vIEFkZCBhIHNhZmV0eSBsaW1pdCBvZiAyMCBwYWdlc1xuICAgICAgICAgICAgY29uc29sZS5sb2coYEZldGNoaW5nIHBhZ2UgJHtjdXJyZW50UGFnZX0uLi5gKTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGZldGNoUGFnZShjdXJyZW50UGFnZSwgcGFnZVNpemUpO1xuXG4gICAgICAgICAgICBpZiAoIXJlc3VsdC5kYXRhIHx8ICFBcnJheS5pc0FycmF5KHJlc3VsdC5kYXRhKSkge1xuICAgICAgICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZlbG9wbWVudFwiKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgICAgICAgICAgICAgICAgIFwiSW52YWxpZCBkYXRhIGZvcm1hdCBpbiBwYWdlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50UGFnZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIFwiOlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0XG4gICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgZGF0YSBmb3JtYXQgcmVjZWl2ZWQgZnJvbSBzZXJ2ZXJcIik7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGFsbERvc3NpZXJzID0gWy4uLmFsbERvc3NpZXJzLCAuLi5yZXN1bHQuZGF0YV07XG4gICAgICAgICAgICB0b3RhbFBhZ2VzID0gcmVzdWx0LnBhZ2luYXRpb24udG90YWxQYWdlcztcbiAgICAgICAgICAgIGhhc01vcmVQYWdlcyA9XG4gICAgICAgICAgICAgICAgcmVzdWx0LnBhZ2luYXRpb24uaGFzTmV4dFBhZ2UgJiYgY3VycmVudFBhZ2UgPCB0b3RhbFBhZ2VzO1xuICAgICAgICAgICAgY3VycmVudFBhZ2UrKztcblxuICAgICAgICAgICAgLy8gSWYgd2UndmUgZmV0Y2hlZCBhbGwgcGFnZXMgb3IgcmVhY2hlZCB0aGUgc2FmZXR5IGxpbWl0LCBzdG9wXG4gICAgICAgICAgICBpZiAoIWhhc01vcmVQYWdlcyB8fCBjdXJyZW50UGFnZSA+IHRvdGFsUGFnZXMpIHtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgYEZldGNoZWQgJHthbGxEb3NzaWVycy5sZW5ndGh9IGRvc3NpZXJzIGZyb20gJHtcbiAgICAgICAgICAgICAgICBjdXJyZW50UGFnZSAtIDFcbiAgICAgICAgICAgIH0gcGFnZXNgXG4gICAgICAgICk7XG4gICAgICAgIHJldHVybiBhbGxEb3NzaWVycztcbiAgICB9IGNhdGNoIChlcnJvcjogdW5rbm93bikge1xuICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwiZGV2ZWxvcG1lbnRcIikge1xuICAgICAgICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXCJFcnJvciBpbiBnZXRDYXM6XCIsIHtcbiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogZXJyb3IubmFtZSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwiVW5rbm93biBlcnJvciBpbiBnZXRDYXM6XCIsIGVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG59XG4iXSwibmFtZXMiOlsiZmV0Y2hBcGkiLCJlbmRwb2ludCIsIm9wdGlvbnMiLCJtZXRob2QiLCJib2R5IiwicmVzcG9uc2UiLCJmZXRjaCIsImhlYWRlcnMiLCJjcmVkZW50aWFscyIsIkpTT04iLCJzdHJpbmdpZnkiLCJ1bmRlZmluZWQiLCJvayIsImVycm9yTWVzc2FnZSIsInN0YXR1cyIsImVycm9yRGF0YSIsImVycm9yVGV4dCIsInRleHQiLCJwYXJzZSIsImVycm9yIiwibWVzc2FnZSIsInBhcnNlRXJyb3IiLCJyZWFkRXJyb3IiLCJjb25zb2xlIiwid2FybiIsInByb2Nlc3MiLCJ1cmwiLCJzdGF0dXNUZXh0IiwiRXJyb3IiLCJqc29uIiwiYXBpQ2xpZW50IiwiZ2V0IiwicG9zdCIsInB1dCIsImRlbGV0ZSIsImdldENoYXQiLCJjYXNJZCIsInNlbmRNZXNzYWdlIiwiY29udGVudCIsImZldGNoUGFnZSIsInBhZ2UiLCJwYWdlU2l6ZSIsImdldENhcyIsImxvZyIsImN1cnJlbnRQYWdlIiwiYWxsRG9zc2llcnMiLCJoYXNNb3JlUGFnZXMiLCJ0b3RhbFBhZ2VzIiwicmVzdWx0IiwiZGF0YSIsIkFycmF5IiwiaXNBcnJheSIsInBhZ2luYXRpb24iLCJoYXNOZXh0UGFnZSIsImxlbmd0aCIsIm5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cadmin%5C%5Cperformance%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cadmin%5C%5Cperformance%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/performance/page.tsx */ \"(app-pages-browser)/./app/admin/performance/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDUm91bGElNUMlNUNEZXNrdG9wJTVDJTVDQVBQTElDQVRJT05TJTVDJTVDYXNzYWluaXNzZW1lbnRWNSU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDcGVyZm9ybWFuY2UlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDBMQUFpSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUm91bGFcXFxcRGVza3RvcFxcXFxBUFBMSUNBVElPTlNcXFxcYXNzYWluaXNzZW1lbnRWNVxcXFxhcHBcXFxcYWRtaW5cXFxccGVyZm9ybWFuY2VcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cadmin%5C%5Cperformance%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7QUFDYixLQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCwrQ0FBK0MsNkJBQTZCO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFnRDtBQUNoRSxnQkFBZ0IsYUFBYTtBQUM3QjtBQUNBO0FBQ0EsZ0NBQWdDLGtDQUFrQyxPQUFPO0FBQ3pFO0FBQ0EsZ0dBQWdHLFNBQVMsVUFBVSxzRkFBc0YsYUFBYSxVQUFVLFVBQVU7QUFDMU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQyxzR0FBMEI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLGdCQUFnQjtBQUNwQixJQUFJLGNBQWM7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFJvdWxhXFxEZXNrdG9wXFxBUFBMSUNBVElPTlNcXGFzc2Fpbmlzc2VtZW50VjVcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxjanNcXHJlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIFJlYWN0XG4gKiByZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanNcbiAqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cblwidXNlIHN0cmljdFwiO1xuXCJwcm9kdWN0aW9uXCIgIT09IHByb2Nlc3MuZW52Lk5PREVfRU5WICYmXG4gIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpIHtcbiAgICAgIGlmIChudWxsID09IHR5cGUpIHJldHVybiBudWxsO1xuICAgICAgaWYgKFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIHR5cGUpXG4gICAgICAgIHJldHVybiB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9DTElFTlRfUkVGRVJFTkNFXG4gICAgICAgICAgPyBudWxsXG4gICAgICAgICAgOiB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBudWxsO1xuICAgICAgaWYgKFwic3RyaW5nXCIgPT09IHR5cGVvZiB0eXBlKSByZXR1cm4gdHlwZTtcbiAgICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgICBjYXNlIFJFQUNUX0ZSQUdNRU5UX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiRnJhZ21lbnRcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9QUk9GSUxFUl9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlByb2ZpbGVyXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1RSSUNUX01PREVfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdHJpY3RNb2RlXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1VTUEVOU0VfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdXNwZW5zZVwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdXNwZW5zZUxpc3RcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9BQ1RJVklUWV9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIkFjdGl2aXR5XCI7XG4gICAgICB9XG4gICAgICBpZiAoXCJvYmplY3RcIiA9PT0gdHlwZW9mIHR5cGUpXG4gICAgICAgIHN3aXRjaCAoXG4gICAgICAgICAgKFwibnVtYmVyXCIgPT09IHR5cGVvZiB0eXBlLnRhZyAmJlxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgXCJSZWNlaXZlZCBhbiB1bmV4cGVjdGVkIG9iamVjdCBpbiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUoKS4gVGhpcyBpcyBsaWtlbHkgYSBidWcgaW4gUmVhY3QuIFBsZWFzZSBmaWxlIGFuIGlzc3VlLlwiXG4gICAgICAgICAgICApLFxuICAgICAgICAgIHR5cGUuJCR0eXBlb2YpXG4gICAgICAgICkge1xuICAgICAgICAgIGNhc2UgUkVBQ1RfUE9SVEFMX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gXCJQb3J0YWxcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0NPTlRFWFRfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAodHlwZS5kaXNwbGF5TmFtZSB8fCBcIkNvbnRleHRcIikgKyBcIi5Qcm92aWRlclwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfQ09OU1VNRVJfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAodHlwZS5fY29udGV4dC5kaXNwbGF5TmFtZSB8fCBcIkNvbnRleHRcIikgKyBcIi5Db25zdW1lclwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfRk9SV0FSRF9SRUZfVFlQRTpcbiAgICAgICAgICAgIHZhciBpbm5lclR5cGUgPSB0eXBlLnJlbmRlcjtcbiAgICAgICAgICAgIHR5cGUgPSB0eXBlLmRpc3BsYXlOYW1lO1xuICAgICAgICAgICAgdHlwZSB8fFxuICAgICAgICAgICAgICAoKHR5cGUgPSBpbm5lclR5cGUuZGlzcGxheU5hbWUgfHwgaW5uZXJUeXBlLm5hbWUgfHwgXCJcIiksXG4gICAgICAgICAgICAgICh0eXBlID0gXCJcIiAhPT0gdHlwZSA/IFwiRm9yd2FyZFJlZihcIiArIHR5cGUgKyBcIilcIiA6IFwiRm9yd2FyZFJlZlwiKSk7XG4gICAgICAgICAgICByZXR1cm4gdHlwZTtcbiAgICAgICAgICBjYXNlIFJFQUNUX01FTU9fVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIChpbm5lclR5cGUgPSB0eXBlLmRpc3BsYXlOYW1lIHx8IG51bGwpLFxuICAgICAgICAgICAgICBudWxsICE9PSBpbm5lclR5cGVcbiAgICAgICAgICAgICAgICA/IGlubmVyVHlwZVxuICAgICAgICAgICAgICAgIDogZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUudHlwZSkgfHwgXCJNZW1vXCJcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgY2FzZSBSRUFDVF9MQVpZX1RZUEU6XG4gICAgICAgICAgICBpbm5lclR5cGUgPSB0eXBlLl9wYXlsb2FkO1xuICAgICAgICAgICAgdHlwZSA9IHR5cGUuX2luaXQ7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICByZXR1cm4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUoaW5uZXJUeXBlKSk7XG4gICAgICAgICAgICB9IGNhdGNoICh4KSB7fVxuICAgICAgICB9XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgZnVuY3Rpb24gdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKSB7XG4gICAgICByZXR1cm4gXCJcIiArIHZhbHVlO1xuICAgIH1cbiAgICBmdW5jdGlvbiBjaGVja0tleVN0cmluZ0NvZXJjaW9uKHZhbHVlKSB7XG4gICAgICB0cnkge1xuICAgICAgICB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpO1xuICAgICAgICB2YXIgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gITE7XG4gICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9ICEwO1xuICAgICAgfVxuICAgICAgaWYgKEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCkge1xuICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSBjb25zb2xlO1xuICAgICAgICB2YXIgSlNDb21waWxlcl90ZW1wX2NvbnN0ID0gSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0LmVycm9yO1xuICAgICAgICB2YXIgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0JGpzY29tcCQwID1cbiAgICAgICAgICAoXCJmdW5jdGlvblwiID09PSB0eXBlb2YgU3ltYm9sICYmXG4gICAgICAgICAgICBTeW1ib2wudG9TdHJpbmdUYWcgJiZcbiAgICAgICAgICAgIHZhbHVlW1N5bWJvbC50b1N0cmluZ1RhZ10pIHx8XG4gICAgICAgICAgdmFsdWUuY29uc3RydWN0b3IubmFtZSB8fFxuICAgICAgICAgIFwiT2JqZWN0XCI7XG4gICAgICAgIEpTQ29tcGlsZXJfdGVtcF9jb25zdC5jYWxsKFxuICAgICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCxcbiAgICAgICAgICBcIlRoZSBwcm92aWRlZCBrZXkgaXMgYW4gdW5zdXBwb3J0ZWQgdHlwZSAlcy4gVGhpcyB2YWx1ZSBtdXN0IGJlIGNvZXJjZWQgdG8gYSBzdHJpbmcgYmVmb3JlIHVzaW5nIGl0IGhlcmUuXCIsXG4gICAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0JGpzY29tcCQwXG4gICAgICAgICk7XG4gICAgICAgIHJldHVybiB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpO1xuICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBnZXRUYXNrTmFtZSh0eXBlKSB7XG4gICAgICBpZiAodHlwZSA9PT0gUkVBQ1RfRlJBR01FTlRfVFlQRSkgcmV0dXJuIFwiPD5cIjtcbiAgICAgIGlmIChcbiAgICAgICAgXCJvYmplY3RcIiA9PT0gdHlwZW9mIHR5cGUgJiZcbiAgICAgICAgbnVsbCAhPT0gdHlwZSAmJlxuICAgICAgICB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9MQVpZX1RZUEVcbiAgICAgIClcbiAgICAgICAgcmV0dXJuIFwiPC4uLj5cIjtcbiAgICAgIHRyeSB7XG4gICAgICAgIHZhciBuYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuICAgICAgICByZXR1cm4gbmFtZSA/IFwiPFwiICsgbmFtZSArIFwiPlwiIDogXCI8Li4uPlwiO1xuICAgICAgfSBjYXRjaCAoeCkge1xuICAgICAgICByZXR1cm4gXCI8Li4uPlwiO1xuICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBnZXRPd25lcigpIHtcbiAgICAgIHZhciBkaXNwYXRjaGVyID0gUmVhY3RTaGFyZWRJbnRlcm5hbHMuQTtcbiAgICAgIHJldHVybiBudWxsID09PSBkaXNwYXRjaGVyID8gbnVsbCA6IGRpc3BhdGNoZXIuZ2V0T3duZXIoKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gVW5rbm93bk93bmVyKCkge1xuICAgICAgcmV0dXJuIEVycm9yKFwicmVhY3Qtc3RhY2stdG9wLWZyYW1lXCIpO1xuICAgIH1cbiAgICBmdW5jdGlvbiBoYXNWYWxpZEtleShjb25maWcpIHtcbiAgICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgXCJrZXlcIikpIHtcbiAgICAgICAgdmFyIGdldHRlciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoY29uZmlnLCBcImtleVwiKS5nZXQ7XG4gICAgICAgIGlmIChnZXR0ZXIgJiYgZ2V0dGVyLmlzUmVhY3RXYXJuaW5nKSByZXR1cm4gITE7XG4gICAgICB9XG4gICAgICByZXR1cm4gdm9pZCAwICE9PSBjb25maWcua2V5O1xuICAgIH1cbiAgICBmdW5jdGlvbiBkZWZpbmVLZXlQcm9wV2FybmluZ0dldHRlcihwcm9wcywgZGlzcGxheU5hbWUpIHtcbiAgICAgIGZ1bmN0aW9uIHdhcm5BYm91dEFjY2Vzc2luZ0tleSgpIHtcbiAgICAgICAgc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd24gfHxcbiAgICAgICAgICAoKHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duID0gITApLFxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICBcIiVzOiBga2V5YCBpcyBub3QgYSBwcm9wLiBUcnlpbmcgdG8gYWNjZXNzIGl0IHdpbGwgcmVzdWx0IGluIGB1bmRlZmluZWRgIGJlaW5nIHJldHVybmVkLiBJZiB5b3UgbmVlZCB0byBhY2Nlc3MgdGhlIHNhbWUgdmFsdWUgd2l0aGluIHRoZSBjaGlsZCBjb21wb25lbnQsIHlvdSBzaG91bGQgcGFzcyBpdCBhcyBhIGRpZmZlcmVudCBwcm9wLiAoaHR0cHM6Ly9yZWFjdC5kZXYvbGluay9zcGVjaWFsLXByb3BzKVwiLFxuICAgICAgICAgICAgZGlzcGxheU5hbWVcbiAgICAgICAgICApKTtcbiAgICAgIH1cbiAgICAgIHdhcm5BYm91dEFjY2Vzc2luZ0tleS5pc1JlYWN0V2FybmluZyA9ICEwO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHByb3BzLCBcImtleVwiLCB7XG4gICAgICAgIGdldDogd2FybkFib3V0QWNjZXNzaW5nS2V5LFxuICAgICAgICBjb25maWd1cmFibGU6ICEwXG4gICAgICB9KTtcbiAgICB9XG4gICAgZnVuY3Rpb24gZWxlbWVudFJlZkdldHRlcldpdGhEZXByZWNhdGlvbldhcm5pbmcoKSB7XG4gICAgICB2YXIgY29tcG9uZW50TmFtZSA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0aGlzLnR5cGUpO1xuICAgICAgZGlkV2FybkFib3V0RWxlbWVudFJlZltjb21wb25lbnROYW1lXSB8fFxuICAgICAgICAoKGRpZFdhcm5BYm91dEVsZW1lbnRSZWZbY29tcG9uZW50TmFtZV0gPSAhMCksXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgXCJBY2Nlc3NpbmcgZWxlbWVudC5yZWYgd2FzIHJlbW92ZWQgaW4gUmVhY3QgMTkuIHJlZiBpcyBub3cgYSByZWd1bGFyIHByb3AuIEl0IHdpbGwgYmUgcmVtb3ZlZCBmcm9tIHRoZSBKU1ggRWxlbWVudCB0eXBlIGluIGEgZnV0dXJlIHJlbGVhc2UuXCJcbiAgICAgICAgKSk7XG4gICAgICBjb21wb25lbnROYW1lID0gdGhpcy5wcm9wcy5yZWY7XG4gICAgICByZXR1cm4gdm9pZCAwICE9PSBjb21wb25lbnROYW1lID8gY29tcG9uZW50TmFtZSA6IG51bGw7XG4gICAgfVxuICAgIGZ1bmN0aW9uIFJlYWN0RWxlbWVudChcbiAgICAgIHR5cGUsXG4gICAgICBrZXksXG4gICAgICBzZWxmLFxuICAgICAgc291cmNlLFxuICAgICAgb3duZXIsXG4gICAgICBwcm9wcyxcbiAgICAgIGRlYnVnU3RhY2ssXG4gICAgICBkZWJ1Z1Rhc2tcbiAgICApIHtcbiAgICAgIHNlbGYgPSBwcm9wcy5yZWY7XG4gICAgICB0eXBlID0ge1xuICAgICAgICAkJHR5cGVvZjogUkVBQ1RfRUxFTUVOVF9UWVBFLFxuICAgICAgICB0eXBlOiB0eXBlLFxuICAgICAgICBrZXk6IGtleSxcbiAgICAgICAgcHJvcHM6IHByb3BzLFxuICAgICAgICBfb3duZXI6IG93bmVyXG4gICAgICB9O1xuICAgICAgbnVsbCAhPT0gKHZvaWQgMCAhPT0gc2VsZiA/IHNlbGYgOiBudWxsKVxuICAgICAgICA/IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcInJlZlwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgICAgIGdldDogZWxlbWVudFJlZkdldHRlcldpdGhEZXByZWNhdGlvbldhcm5pbmdcbiAgICAgICAgICB9KVxuICAgICAgICA6IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcInJlZlwiLCB7IGVudW1lcmFibGU6ICExLCB2YWx1ZTogbnVsbCB9KTtcbiAgICAgIHR5cGUuX3N0b3JlID0ge307XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZS5fc3RvcmUsIFwidmFsaWRhdGVkXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IDBcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnSW5mb1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBudWxsXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcIl9kZWJ1Z1N0YWNrXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IGRlYnVnU3RhY2tcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnVGFza1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBkZWJ1Z1Rhc2tcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmZyZWV6ZSAmJiAoT2JqZWN0LmZyZWV6ZSh0eXBlLnByb3BzKSwgT2JqZWN0LmZyZWV6ZSh0eXBlKSk7XG4gICAgICByZXR1cm4gdHlwZTtcbiAgICB9XG4gICAgZnVuY3Rpb24ganN4REVWSW1wbChcbiAgICAgIHR5cGUsXG4gICAgICBjb25maWcsXG4gICAgICBtYXliZUtleSxcbiAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICBzb3VyY2UsXG4gICAgICBzZWxmLFxuICAgICAgZGVidWdTdGFjayxcbiAgICAgIGRlYnVnVGFza1xuICAgICkge1xuICAgICAgdmFyIGNoaWxkcmVuID0gY29uZmlnLmNoaWxkcmVuO1xuICAgICAgaWYgKHZvaWQgMCAhPT0gY2hpbGRyZW4pXG4gICAgICAgIGlmIChpc1N0YXRpY0NoaWxkcmVuKVxuICAgICAgICAgIGlmIChpc0FycmF5SW1wbChjaGlsZHJlbikpIHtcbiAgICAgICAgICAgIGZvciAoXG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPSAwO1xuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuIDwgY2hpbGRyZW4ubGVuZ3RoO1xuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuKytcbiAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgdmFsaWRhdGVDaGlsZEtleXMoY2hpbGRyZW5baXNTdGF0aWNDaGlsZHJlbl0pO1xuICAgICAgICAgICAgT2JqZWN0LmZyZWV6ZSAmJiBPYmplY3QuZnJlZXplKGNoaWxkcmVuKTtcbiAgICAgICAgICB9IGVsc2VcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAgIFwiUmVhY3QuanN4OiBTdGF0aWMgY2hpbGRyZW4gc2hvdWxkIGFsd2F5cyBiZSBhbiBhcnJheS4gWW91IGFyZSBsaWtlbHkgZXhwbGljaXRseSBjYWxsaW5nIFJlYWN0LmpzeHMgb3IgUmVhY3QuanN4REVWLiBVc2UgdGhlIEJhYmVsIHRyYW5zZm9ybSBpbnN0ZWFkLlwiXG4gICAgICAgICAgICApO1xuICAgICAgICBlbHNlIHZhbGlkYXRlQ2hpbGRLZXlzKGNoaWxkcmVuKTtcbiAgICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgXCJrZXlcIikpIHtcbiAgICAgICAgY2hpbGRyZW4gPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZSk7XG4gICAgICAgIHZhciBrZXlzID0gT2JqZWN0LmtleXMoY29uZmlnKS5maWx0ZXIoZnVuY3Rpb24gKGspIHtcbiAgICAgICAgICByZXR1cm4gXCJrZXlcIiAhPT0gaztcbiAgICAgICAgfSk7XG4gICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPVxuICAgICAgICAgIDAgPCBrZXlzLmxlbmd0aFxuICAgICAgICAgICAgPyBcIntrZXk6IHNvbWVLZXksIFwiICsga2V5cy5qb2luKFwiOiAuLi4sIFwiKSArIFwiOiAuLi59XCJcbiAgICAgICAgICAgIDogXCJ7a2V5OiBzb21lS2V5fVwiO1xuICAgICAgICBkaWRXYXJuQWJvdXRLZXlTcHJlYWRbY2hpbGRyZW4gKyBpc1N0YXRpY0NoaWxkcmVuXSB8fFxuICAgICAgICAgICgoa2V5cyA9XG4gICAgICAgICAgICAwIDwga2V5cy5sZW5ndGggPyBcIntcIiArIGtleXMuam9pbihcIjogLi4uLCBcIikgKyBcIjogLi4ufVwiIDogXCJ7fVwiKSxcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgJ0EgcHJvcHMgb2JqZWN0IGNvbnRhaW5pbmcgYSBcImtleVwiIHByb3AgaXMgYmVpbmcgc3ByZWFkIGludG8gSlNYOlxcbiAgbGV0IHByb3BzID0gJXM7XFxuICA8JXMgey4uLnByb3BzfSAvPlxcblJlYWN0IGtleXMgbXVzdCBiZSBwYXNzZWQgZGlyZWN0bHkgdG8gSlNYIHdpdGhvdXQgdXNpbmcgc3ByZWFkOlxcbiAgbGV0IHByb3BzID0gJXM7XFxuICA8JXMga2V5PXtzb21lS2V5fSB7Li4ucHJvcHN9IC8+JyxcbiAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICAgICAgICBjaGlsZHJlbixcbiAgICAgICAgICAgIGtleXMsXG4gICAgICAgICAgICBjaGlsZHJlblxuICAgICAgICAgICksXG4gICAgICAgICAgKGRpZFdhcm5BYm91dEtleVNwcmVhZFtjaGlsZHJlbiArIGlzU3RhdGljQ2hpbGRyZW5dID0gITApKTtcbiAgICAgIH1cbiAgICAgIGNoaWxkcmVuID0gbnVsbDtcbiAgICAgIHZvaWQgMCAhPT0gbWF5YmVLZXkgJiZcbiAgICAgICAgKGNoZWNrS2V5U3RyaW5nQ29lcmNpb24obWF5YmVLZXkpLCAoY2hpbGRyZW4gPSBcIlwiICsgbWF5YmVLZXkpKTtcbiAgICAgIGhhc1ZhbGlkS2V5KGNvbmZpZykgJiZcbiAgICAgICAgKGNoZWNrS2V5U3RyaW5nQ29lcmNpb24oY29uZmlnLmtleSksIChjaGlsZHJlbiA9IFwiXCIgKyBjb25maWcua2V5KSk7XG4gICAgICBpZiAoXCJrZXlcIiBpbiBjb25maWcpIHtcbiAgICAgICAgbWF5YmVLZXkgPSB7fTtcbiAgICAgICAgZm9yICh2YXIgcHJvcE5hbWUgaW4gY29uZmlnKVxuICAgICAgICAgIFwia2V5XCIgIT09IHByb3BOYW1lICYmIChtYXliZUtleVtwcm9wTmFtZV0gPSBjb25maWdbcHJvcE5hbWVdKTtcbiAgICAgIH0gZWxzZSBtYXliZUtleSA9IGNvbmZpZztcbiAgICAgIGNoaWxkcmVuICYmXG4gICAgICAgIGRlZmluZUtleVByb3BXYXJuaW5nR2V0dGVyKFxuICAgICAgICAgIG1heWJlS2V5LFxuICAgICAgICAgIFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIHR5cGVcbiAgICAgICAgICAgID8gdHlwZS5kaXNwbGF5TmFtZSB8fCB0eXBlLm5hbWUgfHwgXCJVbmtub3duXCJcbiAgICAgICAgICAgIDogdHlwZVxuICAgICAgICApO1xuICAgICAgcmV0dXJuIFJlYWN0RWxlbWVudChcbiAgICAgICAgdHlwZSxcbiAgICAgICAgY2hpbGRyZW4sXG4gICAgICAgIHNlbGYsXG4gICAgICAgIHNvdXJjZSxcbiAgICAgICAgZ2V0T3duZXIoKSxcbiAgICAgICAgbWF5YmVLZXksXG4gICAgICAgIGRlYnVnU3RhY2ssXG4gICAgICAgIGRlYnVnVGFza1xuICAgICAgKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gdmFsaWRhdGVDaGlsZEtleXMobm9kZSkge1xuICAgICAgXCJvYmplY3RcIiA9PT0gdHlwZW9mIG5vZGUgJiZcbiAgICAgICAgbnVsbCAhPT0gbm9kZSAmJlxuICAgICAgICBub2RlLiQkdHlwZW9mID09PSBSRUFDVF9FTEVNRU5UX1RZUEUgJiZcbiAgICAgICAgbm9kZS5fc3RvcmUgJiZcbiAgICAgICAgKG5vZGUuX3N0b3JlLnZhbGlkYXRlZCA9IDEpO1xuICAgIH1cbiAgICB2YXIgUmVhY3QgPSByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0XCIpLFxuICAgICAgUkVBQ1RfRUxFTUVOVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnRyYW5zaXRpb25hbC5lbGVtZW50XCIpLFxuICAgICAgUkVBQ1RfUE9SVEFMX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QucG9ydGFsXCIpLFxuICAgICAgUkVBQ1RfRlJBR01FTlRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5mcmFnbWVudFwiKSxcbiAgICAgIFJFQUNUX1NUUklDVF9NT0RFX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3RyaWN0X21vZGVcIiksXG4gICAgICBSRUFDVF9QUk9GSUxFUl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnByb2ZpbGVyXCIpO1xuICAgIFN5bWJvbC5mb3IoXCJyZWFjdC5wcm92aWRlclwiKTtcbiAgICB2YXIgUkVBQ1RfQ09OU1VNRVJfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jb25zdW1lclwiKSxcbiAgICAgIFJFQUNUX0NPTlRFWFRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jb250ZXh0XCIpLFxuICAgICAgUkVBQ1RfRk9SV0FSRF9SRUZfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5mb3J3YXJkX3JlZlwiKSxcbiAgICAgIFJFQUNUX1NVU1BFTlNFX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3VzcGVuc2VcIiksXG4gICAgICBSRUFDVF9TVVNQRU5TRV9MSVNUX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3VzcGVuc2VfbGlzdFwiKSxcbiAgICAgIFJFQUNUX01FTU9fVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5tZW1vXCIpLFxuICAgICAgUkVBQ1RfTEFaWV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmxhenlcIiksXG4gICAgICBSRUFDVF9BQ1RJVklUWV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmFjdGl2aXR5XCIpLFxuICAgICAgUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jbGllbnQucmVmZXJlbmNlXCIpLFxuICAgICAgUmVhY3RTaGFyZWRJbnRlcm5hbHMgPVxuICAgICAgICBSZWFjdC5fX0NMSUVOVF9JTlRFUk5BTFNfRE9fTk9UX1VTRV9PUl9XQVJOX1VTRVJTX1RIRVlfQ0FOTk9UX1VQR1JBREUsXG4gICAgICBoYXNPd25Qcm9wZXJ0eSA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHksXG4gICAgICBpc0FycmF5SW1wbCA9IEFycmF5LmlzQXJyYXksXG4gICAgICBjcmVhdGVUYXNrID0gY29uc29sZS5jcmVhdGVUYXNrXG4gICAgICAgID8gY29uc29sZS5jcmVhdGVUYXNrXG4gICAgICAgIDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgfTtcbiAgICBSZWFjdCA9IHtcbiAgICAgIFwicmVhY3Qtc3RhY2stYm90dG9tLWZyYW1lXCI6IGZ1bmN0aW9uIChjYWxsU3RhY2tGb3JFcnJvcikge1xuICAgICAgICByZXR1cm4gY2FsbFN0YWNrRm9yRXJyb3IoKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIHZhciBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93bjtcbiAgICB2YXIgZGlkV2FybkFib3V0RWxlbWVudFJlZiA9IHt9O1xuICAgIHZhciB1bmtub3duT3duZXJEZWJ1Z1N0YWNrID0gUmVhY3RbXCJyZWFjdC1zdGFjay1ib3R0b20tZnJhbWVcIl0uYmluZChcbiAgICAgIFJlYWN0LFxuICAgICAgVW5rbm93bk93bmVyXG4gICAgKSgpO1xuICAgIHZhciB1bmtub3duT3duZXJEZWJ1Z1Rhc2sgPSBjcmVhdGVUYXNrKGdldFRhc2tOYW1lKFVua25vd25Pd25lcikpO1xuICAgIHZhciBkaWRXYXJuQWJvdXRLZXlTcHJlYWQgPSB7fTtcbiAgICBleHBvcnRzLkZyYWdtZW50ID0gUkVBQ1RfRlJBR01FTlRfVFlQRTtcbiAgICBleHBvcnRzLmpzeERFViA9IGZ1bmN0aW9uIChcbiAgICAgIHR5cGUsXG4gICAgICBjb25maWcsXG4gICAgICBtYXliZUtleSxcbiAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICBzb3VyY2UsXG4gICAgICBzZWxmXG4gICAgKSB7XG4gICAgICB2YXIgdHJhY2tBY3R1YWxPd25lciA9XG4gICAgICAgIDFlNCA+IFJlYWN0U2hhcmVkSW50ZXJuYWxzLnJlY2VudGx5Q3JlYXRlZE93bmVyU3RhY2tzKys7XG4gICAgICByZXR1cm4ganN4REVWSW1wbChcbiAgICAgICAgdHlwZSxcbiAgICAgICAgY29uZmlnLFxuICAgICAgICBtYXliZUtleSxcbiAgICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgICAgc291cmNlLFxuICAgICAgICBzZWxmLFxuICAgICAgICB0cmFja0FjdHVhbE93bmVyXG4gICAgICAgICAgPyBFcnJvcihcInJlYWN0LXN0YWNrLXRvcC1mcmFtZVwiKVxuICAgICAgICAgIDogdW5rbm93bk93bmVyRGVidWdTdGFjayxcbiAgICAgICAgdHJhY2tBY3R1YWxPd25lciA/IGNyZWF0ZVRhc2soZ2V0VGFza05hbWUodHlwZSkpIDogdW5rbm93bk93bmVyRGVidWdUYXNrXG4gICAgICApO1xuICAgIH07XG4gIH0pKCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cadmin%5C%5Cperformance%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);