"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/cas/page",{

/***/ "(app-pages-browser)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   fetchApi: () => (/* binding */ fetchApi),\n/* harmony export */   getCas: () => (/* binding */ getCas),\n/* harmony export */   getChat: () => (/* binding */ getChat),\n/* harmony export */   sendMessage: () => (/* binding */ sendMessage)\n/* harmony export */ });\nasync function fetchApi(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { method = \"GET\", body } = options;\n    const response = await fetch(endpoint, {\n        method,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...options.headers\n        },\n        credentials: options.credentials || \"include\",\n        body: body ? JSON.stringify(body) : undefined\n    });\n    if (!response.ok) {\n        let errorMessage = \"HTTP error! status: \".concat(response.status);\n        let errorData = null;\n        try {\n            // Read the response body as text first\n            const errorText = await response.text();\n            if (errorText) {\n                try {\n                    // Try to parse the text as JSON\n                    errorData = JSON.parse(errorText);\n                    errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.error) || (errorData === null || errorData === void 0 ? void 0 : errorData.message) || errorText;\n                } catch (parseError) {\n                    // If parsing fails, use the raw text as the error message\n                    errorMessage = errorText || errorMessage;\n                }\n            }\n        } catch (readError) {\n            // If we can't read the response body, use the status\n            console.warn(\"Could not read error response body:\", readError);\n        }\n        // Log error details for debugging (but don't throw console.error)\n        if (true) {\n            console.warn(\"API Error Details:\", {\n                url: response.url,\n                status: response.status,\n                statusText: response.statusText,\n                errorMessage,\n                errorData\n            });\n        }\n        // Handle specific error cases\n        if (response.status === 401) {\n            throw new Error(\"Authentication required. Please log in again.\");\n        } else if (response.status === 403) {\n            throw new Error(\"Access denied. You don't have permission to perform this action.\");\n        } else if (response.status === 404) {\n            throw new Error(\"Resource not found.\");\n        } else if (response.status >= 500) {\n            throw new Error(\"Server error. Please try again later.\");\n        }\n        throw new Error(errorMessage);\n    }\n    if (response.status === 204) {\n        // No Content\n        return null;\n    }\n    return response.json();\n}\n// Add the following apiClient export:\nconst apiClient = {\n    get: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"GET\"\n        });\n    },\n    post: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"POST\",\n            body\n        });\n    },\n    put: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"PUT\",\n            body\n        });\n    },\n    delete: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"DELETE\"\n        });\n    }\n};\n// Chat Functions\nconst getChat = (casId)=>{\n    return apiClient.get(\"/api/chats/\".concat(casId));\n};\nconst sendMessage = (casId, content)=>{\n    return apiClient.post(\"/api/chats/\".concat(casId, \"/messages\"), {\n        content\n    });\n};\nasync function fetchPage(page, pageSize) {\n    const response = await fetch(\"/api/cas?page=\".concat(page, \"&pageSize=\").concat(pageSize), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        credentials: \"include\"\n    });\n    if (!response.ok) {\n        let errorMessage = \"API request failed with status \".concat(response.status);\n        try {\n            const errorText = await response.text();\n            if (errorText) {\n                try {\n                    const errorData = JSON.parse(errorText);\n                    errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.error) || (errorData === null || errorData === void 0 ? void 0 : errorData.message) || errorText;\n                } catch (e) {\n                    errorMessage = errorText;\n                }\n            }\n        } catch (e) {\n        // If we can't read the response, use the default message\n        }\n        if (true) {\n            console.warn(\"getCas API Error:\", {\n                status: response.status,\n                statusText: response.statusText,\n                url: response.url,\n                errorMessage\n            });\n        }\n        throw new Error(errorMessage);\n    }\n    return response.json();\n}\nasync function getCas() {\n    try {\n        console.log(\"Fetching dossiers from /api/cas...\");\n        const pageSize = 100; // Maximum allowed by the API\n        let currentPage = 1;\n        let allDossiers = [];\n        let hasMorePages = true;\n        let totalPages = 1;\n        // Fetch all pages\n        while(hasMorePages && currentPage <= 20){\n            // Add a safety limit of 20 pages\n            console.log(\"Fetching page \".concat(currentPage, \"...\"));\n            const result = await fetchPage(currentPage, pageSize);\n            if (!result.data || !Array.isArray(result.data)) {\n                if (true) {\n                    console.warn(\"Invalid data format in page\", currentPage, \":\", result);\n                }\n                throw new Error(\"Invalid data format received from server\");\n            }\n            allDossiers = [\n                ...allDossiers,\n                ...result.data\n            ];\n            totalPages = result.pagination.totalPages;\n            hasMorePages = result.pagination.hasNextPage && currentPage < totalPages;\n            currentPage++;\n            // If we've fetched all pages or reached the safety limit, stop\n            if (!hasMorePages || currentPage > totalPages) {\n                break;\n            }\n        }\n        console.log(\"Fetched \".concat(allDossiers.length, \" dossiers from \").concat(currentPage - 1, \" pages\"));\n        return allDossiers;\n    } catch (error) {\n        if (error instanceof Error) {\n            console.error(\"Error in getCas:\", {\n                message: error.message,\n                name: error.name,\n                stack: error.stack\n            });\n        } else {\n            console.error(\"Unknown error in getCas:\", error);\n        }\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-client.ts\n"));

/***/ })

});