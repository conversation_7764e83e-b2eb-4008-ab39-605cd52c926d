/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/blocages/[id]/route";
exports.ids = ["app/api/blocages/[id]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/blocages/[id]/route.ts":
/*!****************************************!*\
  !*** ./app/api/blocages/[id]/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_resolution_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/resolution-utils */ \"(rsc)/./lib/resolution-utils.ts\");\n\n\n\n\n\n\n\n// Schéma pour la mise à jour d'un blocage\nconst blocageUpdateSchema = zod__WEBPACK_IMPORTED_MODULE_6__.z.object({\n    description: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(1).optional(),\n    secteurId: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(1).optional(),\n    regularise: zod__WEBPACK_IMPORTED_MODULE_6__.z.boolean().optional(),\n    solution: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().datetime().nullable().optional(),\n    resolution: zod__WEBPACK_IMPORTED_MODULE_6__.z.enum([\n        \"ATTENTE\",\n        \"ACCEPTE\",\n        \"AJOURNE\",\n        \"REJETE\"\n    ]).optional(),\n    detail_resolution: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().nullable().optional()\n});\n// Gérer la requête GET pour un seul blocage (optionnel, mais utile)\nasync function GET(request, { params }) {\n    try {\n        const { id: blocageId } = await params;\n        // Optionnel: Vérification du token si l'accès est restreint\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_4__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)(\"Token non fourni.\");\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.verifyToken)(token);\n        if (!userPayload) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)(\"Token invalide.\");\n        // Ajoutez ici une logique de permission si nécessaire pour GET\n        const blocage = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.blocage.findUnique({\n            where: {\n                id: blocageId\n            },\n            include: {\n                secteur: true,\n                cas: {\n                    select: {\n                        id: true,\n                        nom: true\n                    }\n                }\n            }\n        });\n        if (!blocage) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.notFound)(\"Blocage non trouvé.\");\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(blocage);\n    } catch (error) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n// Gérer la requête PUT pour mettre à jour un blocage\nasync function PUT(request, { params }) {\n    try {\n        const { id: blocageId } = await params;\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_4__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)(\"Token non fourni.\");\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.verifyToken)(token);\n        if (!userPayload || userPayload.role !== \"ADMIN\" && userPayload.role !== \"EDITOR\") {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)(\"Accès non autorisé pour modifier le blocage.\");\n        }\n        const body = await request.json();\n        const parsedData = blocageUpdateSchema.parse(body);\n        const existingBlocage = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.blocage.findUnique({\n            where: {\n                id: blocageId\n            }\n        });\n        if (!existingBlocage) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.notFound)(\"Blocage non trouvé.\");\n        }\n        // Si secteurId est fourni dans la mise à jour, vérifier son existence\n        if (parsedData.secteurId) {\n            const secteurExists = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.secteur.findUnique({\n                where: {\n                    id: parsedData.secteurId\n                }\n            });\n            if (!secteurExists) {\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                    error: \"Le secteur spécifié pour la mise à jour n'existe pas.\"\n                }, {\n                    status: 404\n                });\n            }\n        }\n        // Préparer les données pour la mise à jour\n        const updateData = {\n            description: parsedData.description,\n            secteurId: parsedData.secteurId,\n            regularise: parsedData.regularise,\n            resolution: parsedData.resolution,\n            detail_resolution: parsedData.detail_resolution\n        };\n        // Si regularise est false, mettre solution à null\n        // Si regularise est true, utiliser la valeur de solution fournie (ou laisser inchangée si non fournie)\n        if (parsedData.regularise === false) {\n            updateData.solution = null;\n        } else if (parsedData.solution !== undefined) {\n            updateData.solution = parsedData.solution;\n        }\n        const updatedBlocage = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.blocage.update({\n            where: {\n                id: blocageId\n            },\n            data: updateData,\n            include: {\n                secteur: true,\n                cas: {\n                    select: {\n                        id: true,\n                        nom: true\n                    }\n                }\n            }\n        });\n        // Mettre à jour le statut de régularisation du cas basé sur les résolutions des blocages\n        await (0,_lib_resolution_utils__WEBPACK_IMPORTED_MODULE_5__.updateCasRegularisationFromResolutions)(existingBlocage.casId);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            updatedBlocage\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_6__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Données d'entrée invalides pour la mise à jour.\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n// Gérer la requête DELETE pour supprimer un blocage (optionnel, mais utile)\nasync function DELETE(request, { params }) {\n    try {\n        const { id: blocageId } = await params;\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_4__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)(\"Token non fourni.\");\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.verifyToken)(token);\n        if (!userPayload || userPayload.role !== \"ADMIN\" && userPayload.role !== \"EDITOR\") {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)(\"Accès non autorisé pour supprimer le blocage.\");\n        }\n        const existingBlocage = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.blocage.findUnique({\n            where: {\n                id: blocageId\n            },\n            select: {\n                casId: true\n            }\n        });\n        if (!existingBlocage) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.notFound)(\"Blocage non trouvé.\");\n        }\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.blocage.delete({\n            where: {\n                id: blocageId\n            }\n        });\n        // Après la suppression d'un blocage, mettre à jour le statut `regularisation` du Cas parent.\n        if (existingBlocage.casId) {\n            const remainingBlocages = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.blocage.findMany({\n                where: {\n                    casId: existingBlocage.casId\n                },\n                select: {\n                    regularise: true\n                }\n            });\n            const allRemainingBlocagesRegularised = remainingBlocages.length > 0 && remainingBlocages.every((b)=>b.regularise);\n            const casIsNowRegularised = remainingBlocages.length === 0 ? false : allRemainingBlocagesRegularised;\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.update({\n                where: {\n                    id: existingBlocage.casId\n                },\n                data: {\n                    regularisation: casIsNowRegularised\n                }\n            });\n        }\n        return new next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse(null, {\n            status: 204\n        }); // No Content\n    } catch (error) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/blocages/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   forbidden: () => (/* binding */ forbidden),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   notFound: () => (/* binding */ notFound),\n/* harmony export */   unauthorized: () => (/* binding */ unauthorized),\n/* harmony export */   updateCasRegularisationStatus: () => (/* binding */ updateCasRegularisationStatus)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n // Importez Prisma pour typer les erreurs spécifiques\n\nasync function updateCasRegularisationStatus(casId) {\n    const relatedCasBlocages = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.blocage.findMany({\n        where: {\n            casId: casId\n        },\n        select: {\n            regularise: true\n        }\n    });\n    const allBlocagesRegularised = relatedCasBlocages.length > 0 && relatedCasBlocages.every((b)=>b.regularise);\n    await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.cas.update({\n        where: {\n            id: casId\n        },\n        data: {\n            regularisation: allBlocagesRegularised\n        }\n    });\n}\nfunction handleError(error) {\n    console.error(error); // Bon pour le débogage côté serveur\n    if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_1__.Prisma.PrismaClientKnownRequestError) {\n        // Erreurs connues de Prisma (contraintes uniques, etc.)\n        // Vous pouvez ajouter des codes d'erreur spécifiques ici si nécessaire\n        // Par exemple, P2002 pour violation de contrainte unique\n        if (error.code === \"P2002\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Une ressource avec ces identifiants existe déjà.\",\n                details: error.meta\n            }, {\n                status: 409\n            }); // Conflict\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur de base de données.\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n    if (error instanceof Error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Une erreur interne est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Une erreur inconnue est survenue.\"\n    }, {\n        status: 500\n    });\n}\nfunction forbidden(message = \"Accès interdit.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 403\n    });\n}\nfunction notFound(message = \"Ressource non trouvée.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 404\n    });\n}\nfunction unauthorized() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Non autorisé\"\n    }, {\n        status: 401\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./lib/resolution-utils.ts":
/*!*********************************!*\
  !*** ./lib/resolution-utils.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterCasByStatus: () => (/* binding */ filterCasByStatus),\n/* harmony export */   getCasStatisticsByResolution: () => (/* binding */ getCasStatisticsByResolution),\n/* harmony export */   getCasStatus: () => (/* binding */ getCasStatus),\n/* harmony export */   isCasAjourne: () => (/* binding */ isCasAjourne),\n/* harmony export */   isCasNonExamine: () => (/* binding */ isCasNonExamine),\n/* harmony export */   isCasRegularise: () => (/* binding */ isCasRegularise),\n/* harmony export */   updateCasRegularisationFromResolutions: () => (/* binding */ updateCasRegularisationFromResolutions)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n/**\n * Met à jour le statut de régularisation d'un cas basé sur les résolutions de ses blocages\n * Règles:\n * - Si tous les blocages sont ACCEPTE -> regularisation = true\n * - Si au moins un blocage est AJOURNE -> regularisation = false\n * - Si tous les blocages sont ATTENTE -> regularisation = false\n */ async function updateCasRegularisationFromResolutions(casId) {\n    try {\n        // Récupérer tous les blocages du cas avec leurs résolutions\n        const blocages = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.blocage.findMany({\n            where: {\n                casId\n            },\n            select: {\n                resolution: true\n            }\n        });\n        if (blocages.length === 0) {\n            // Aucun blocage, le cas reste non régularisé\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.update({\n                where: {\n                    id: casId\n                },\n                data: {\n                    regularisation: false\n                }\n            });\n            return;\n        }\n        const resolutions = blocages.map((b)=>b.resolution);\n        // Déterminer le nouveau statut de régularisation\n        let newRegularisationStatus = false;\n        if (resolutions.every((resolution)=>resolution === \"ACCEPTE\")) {\n            // Tous acceptés -> régularisé\n            newRegularisationStatus = true;\n        } else {\n            // Au moins un ajourné ou en attente -> non régularisé\n            newRegularisationStatus = false;\n        }\n        // Mettre à jour le cas\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.update({\n            where: {\n                id: casId\n            },\n            data: {\n                regularisation: newRegularisationStatus\n            }\n        });\n        console.log(`Cas ${casId} mis à jour: regularisation = ${newRegularisationStatus}`);\n    } catch (error) {\n        console.error(`Erreur lors de la mise à jour du cas ${casId}:`, error);\n        throw error;\n    }\n}\n/**\n * Calcule les statistiques des cas basées sur les résolutions des blocages\n * Filtre par wilaya selon le rôle de l'utilisateur\n */ async function getCasStatisticsByResolution(userPayload) {\n    try {\n        // Construire les conditions de filtrage\n        const where = {};\n        // Filtrage par wilayaId selon le rôle\n        if (userPayload) {\n            if (userPayload.role === \"BASIC\" || userPayload.role === \"EDITOR\") {\n                // Pour BASIC et EDITOR, filtrer par leur wilayaId uniquement\n                if (userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {\n                    where.wilayaId = Number(userPayload.wilayaId);\n                }\n            } else if (userPayload.role === \"ADMIN\" || userPayload.role === \"VIEWER\") {\n            // Pour ADMIN et VIEWER, pas de filtrage par défaut (accès à toutes les wilayas)\n            // Le filtrage peut être ajouté via des paramètres si nécessaire\n            }\n        }\n        console.log(\"📊 getCasStatisticsByResolution - Utilisation de requête SQL optimisée...\");\n        console.time(\"resolution-stats-optimized\");\n        // Version optimisée avec requête SQL brute pour éviter les limites\n        let whereClauseSQL = \"\";\n        let params = [];\n        if (where.wilayaId) {\n            whereClauseSQL = 'WHERE c.\"wilayaId\" = $1';\n            params = [\n                where.wilayaId\n            ];\n        }\n        let casAjournes = 0;\n        let casNonExamines = 0;\n        let casRegularises = 0;\n        let casNonRegularises = 0;\n        let casRejetes = 0;\n        let totalCas = 0;\n        try {\n            // Requête SQL optimisée pour calculer les statistiques de résolution\n            const statsQuery = `\n                SELECT\n                    COUNT(DISTINCT c.id) as total_cas,\n                    COUNT(DISTINCT CASE WHEN b.resolution = 'ACCEPTE' THEN c.id END) as acceptes,\n                    COUNT(DISTINCT CASE WHEN b.resolution = 'AJOURNE' THEN c.id END) as ajournes,\n                    COUNT(DISTINCT CASE WHEN b.resolution = 'REJETE' THEN c.id END) as rejetes,\n                    COUNT(DISTINCT CASE WHEN b.resolution = 'ATTENTE' OR b.resolution IS NULL THEN c.id END) as attente_ou_null,\n                    COUNT(DISTINCT CASE WHEN b.id IS NULL THEN c.id END) as sans_blocage\n                FROM \"cas\" c\n                LEFT JOIN \"blocages\" b ON c.id = b.\"casId\"\n                ${whereClauseSQL}\n            `;\n            const rawResults = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$queryRawUnsafe(statsQuery, ...params);\n            if (Array.isArray(rawResults) && rawResults.length > 0) {\n                const result = rawResults[0];\n                totalCas = Number(result.total_cas || 0);\n                casRegularises = Number(result.acceptes || 0);\n                casAjournes = Number(result.ajournes || 0);\n                casRejetes = Number(result.rejetes || 0);\n                // Les cas non examinés sont ceux sans blocage ou avec tous les blocages en attente\n                const sansBlocage = Number(result.sans_blocage || 0);\n                const attenteOuNull = Number(result.attente_ou_null || 0);\n                casNonExamines = sansBlocage + (attenteOuNull - casRegularises - casAjournes - casRejetes);\n                // Les cas non régularisés sont le reste\n                casNonRegularises = totalCas - casRegularises - casAjournes - casRejetes - casNonExamines;\n                // S'assurer que les valeurs sont positives\n                casNonExamines = Math.max(0, casNonExamines);\n                casNonRegularises = Math.max(0, casNonRegularises);\n            }\n            console.log(`📊 ${totalCas} cas traités avec requête optimisée`);\n        } catch (sqlError) {\n            console.error(\"Erreur SQL, fallback vers requête simple:\", sqlError);\n            // Fallback vers requête simple avec limite\n            totalCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                where\n            });\n            // Estimations simples\n            casRegularises = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                where: {\n                    ...where,\n                    regularisation: true\n                }\n            });\n            casNonExamines = totalCas - casRegularises;\n        }\n        console.timeEnd(\"resolution-stats-optimized\");\n        return {\n            total: totalCas,\n            regularises: casRegularises,\n            nonRegularises: casNonRegularises,\n            ajournes: casAjournes,\n            nonExamines: casNonExamines,\n            rejetes: casRejetes\n        };\n    } catch (error) {\n        console.error(\"Erreur lors du calcul des statistiques:\", error);\n        throw error;\n    }\n}\n/**\n * Détermine si un cas est ajourné (au moins un blocage ajourné)\n */ function isCasAjourne(resolutions) {\n    return resolutions.some((resolution)=>resolution === \"AJOURNE\");\n}\n/**\n * Détermine si un cas est non examiné (tous les blocages en attente ou aucun blocage)\n */ function isCasNonExamine(resolutions) {\n    return resolutions.length === 0 || resolutions.every((resolution)=>resolution === \"ATTENTE\");\n}\n/**\n * Détermine si un cas est régularisé (tous les blocages acceptés)\n */ function isCasRegularise(resolutions) {\n    return resolutions.length > 0 && resolutions.every((resolution)=>resolution === \"ACCEPTE\");\n}\n/**\n * Détermine le statut d'un cas basé sur ses résolutions\n * IMPORTANT: Cette fonction définit la logique de priorité officielle\n * Utilisez cette fonction dans tous les filtres pour maintenir la cohérence\n */ function getCasStatus(resolutions) {\n    if (resolutions.length === 0) {\n        return \"NON_EXAMINE\"; // Cas sans blocage\n    } else if (resolutions.every((r)=>r === \"ATTENTE\")) {\n        return \"NON_EXAMINE\"; // Tous en attente\n    } else if (resolutions.some((r)=>r === \"REJETE\")) {\n        return \"REJETE\"; // Au moins un rejeté (priorité la plus haute)\n    } else if (resolutions.some((r)=>r === \"AJOURNE\")) {\n        return \"AJOURNE\"; // Au moins un ajourné\n    } else if (resolutions.every((r)=>r === \"ACCEPTE\")) {\n        return \"REGULARISE\"; // Tous acceptés\n    } else {\n        return \"NON_EXAMINE\"; // Cas par défaut\n    }\n}\n/**\n * Filtre les cas par statut en utilisant la logique de priorité officielle\n * Utilisez cette fonction dans toutes les APIs pour maintenir la cohérence\n */ function filterCasByStatus(cas, targetStatus) {\n    if (![\n        \"REGULARISE\",\n        \"AJOURNE\",\n        \"NON_EXAMINE\",\n        \"REJETE\"\n    ].includes(targetStatus)) {\n        return cas; // Pas de filtrage si statut invalide\n    }\n    return cas.filter((c)=>{\n        const resolutions = c.blocage.map((b)=>b.resolution);\n        const actualStatus = getCasStatus(resolutions);\n        return actualStatus === targetStatus;\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/resolution-utils.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblocages%2F%5Bid%5D%2Froute&page=%2Fapi%2Fblocages%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblocages%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblocages%2F%5Bid%5D%2Froute&page=%2Fapi%2Fblocages%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblocages%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_blocages_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/blocages/[id]/route.ts */ \"(rsc)/./app/api/blocages/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/blocages/[id]/route\",\n        pathname: \"/api/blocages/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/blocages/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\blocages\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_blocages_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblocages%2F%5Bid%5D%2Froute&page=%2Fapi%2Fblocages%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblocages%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblocages%2F%5Bid%5D%2Froute&page=%2Fapi%2Fblocages%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblocages%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();