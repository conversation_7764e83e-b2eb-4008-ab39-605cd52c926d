import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyToken } from "@/lib/auth";
import { cookies } from "next/headers";
import { getCasStatus } from "@/lib/resolution-utils";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token manquant" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload || userPayload.role !== "ADMIN") {
            return NextResponse.json(
                { error: "Accès non autorisé - Admin requis" },
                { status: 403 }
            );
        }

        console.log("🧪 Test de cohérence des statistiques");
        const startTime = performance.now();

        // 1. Récupérer tous les cas avec leurs blocages
        const allCas = await prisma.cas.findMany({
            include: {
                blocage: {
                    select: {
                        resolution: true
                    }
                }
            }
        });

        // 2. Calculer les statistiques manuellement avec la logique officielle
        const manualStats = {
            total: allCas.length,
            regularises: 0,
            ajournes: 0,
            rejetes: 0,
            nonExamines: 0,
            regularisationTrue: 0 // Basé sur le champ regularisation
        };

        allCas.forEach(cas => {
            const resolutions = cas.blocage.map(b => b.resolution as any);
            const status = getCasStatus(resolutions);
            
            switch (status) {
                case "REGULARISE":
                    manualStats.regularises++;
                    break;
                case "AJOURNE":
                    manualStats.ajournes++;
                    break;
                case "REJETE":
                    manualStats.rejetes++;
                    break;
                case "NON_EXAMINE":
                    manualStats.nonExamines++;
                    break;
            }

            if (cas.regularisation) {
                manualStats.regularisationTrue++;
            }
        });

        // 3. Tester l'API des statistiques des cas
        const casStatsResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/stats/cas`, {
            headers: { 'Cookie': `token=${token}` }
        });

        let casStatsAPI = null;
        if (casStatsResponse.ok) {
            casStatsAPI = await casStatsResponse.json();
        }

        // 4. Tester l'API des statistiques de résolution
        const resolutionStatsResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/stats/resolution`, {
            headers: { 'Cookie': `token=${token}` }
        });

        let resolutionStatsAPI = null;
        if (resolutionStatsResponse.ok) {
            resolutionStatsAPI = await resolutionStatsResponse.json();
        }

        // 5. Tester le filtrage par statut
        const statusTests = [];
        const statuses = ["REGULARISE", "AJOURNE", "REJETE", "NON_EXAMINE"];
        
        for (const status of statuses) {
            const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/cas?casStatus=${status}&pageSize=1000`, {
                headers: { 'Cookie': `token=${token}` }
            });
            
            if (response.ok) {
                const data = await response.json();
                statusTests.push({
                    status,
                    count: data.pagination?.totalCount || 0,
                    success: true
                });
            } else {
                statusTests.push({
                    status,
                    count: 0,
                    success: false,
                    error: response.status
                });
            }
        }

        // 6. Vérifications de cohérence
        const checks = {
            casStatsTotal: {
                expected: manualStats.total,
                actual: casStatsAPI?.total || 0,
                passed: manualStats.total === (casStatsAPI?.total || 0)
            },
            casStatsRegularises: {
                expected: manualStats.regularisationTrue, // Basé sur le champ regularisation
                actual: casStatsAPI?.regularises || 0,
                passed: manualStats.regularisationTrue === (casStatsAPI?.regularises || 0)
            },
            resolutionStatsTotal: {
                expected: manualStats.total,
                actual: resolutionStatsAPI?.total || 0,
                passed: manualStats.total === (resolutionStatsAPI?.total || 0)
            },
            resolutionStatsRegularises: {
                expected: manualStats.regularises,
                actual: resolutionStatsAPI?.regularises || 0,
                passed: manualStats.regularises === (resolutionStatsAPI?.regularises || 0)
            },
            statusFilteringRegularises: {
                expected: manualStats.regularises,
                actual: statusTests.find(t => t.status === "REGULARISE")?.count || 0,
                passed: manualStats.regularises === (statusTests.find(t => t.status === "REGULARISE")?.count || 0)
            },
            statusFilteringAjournes: {
                expected: manualStats.ajournes,
                actual: statusTests.find(t => t.status === "AJOURNE")?.count || 0,
                passed: manualStats.ajournes === (statusTests.find(t => t.status === "AJOURNE")?.count || 0)
            },
            statusFilteringRejetes: {
                expected: manualStats.rejetes,
                actual: statusTests.find(t => t.status === "REJETE")?.count || 0,
                passed: manualStats.rejetes === (statusTests.find(t => t.status === "REJETE")?.count || 0)
            },
            statusFilteringNonExamines: {
                expected: manualStats.nonExamines,
                actual: statusTests.find(t => t.status === "NON_EXAMINE")?.count || 0,
                passed: manualStats.nonExamines === (statusTests.find(t => t.status === "NON_EXAMINE")?.count || 0)
            }
        };

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        const allChecksPassed = Object.values(checks).every(check => check.passed);

        return NextResponse.json({
            success: true,
            message: "Test de cohérence des statistiques terminé",
            allChecksPassed,
            manualStats,
            apiStats: {
                cas: casStatsAPI,
                resolution: resolutionStatsAPI
            },
            statusFiltering: statusTests,
            checks: Object.entries(checks).map(([name, check]) => ({
                name,
                expected: check.expected,
                actual: check.actual,
                passed: check.passed,
                message: check.passed ? "✅ Cohérent" : `❌ Incohérent: attendu ${check.expected}, reçu ${check.actual}`
            })),
            performance: {
                duration,
                timestamp: new Date().toISOString()
            },
            recommendations: allChecksPassed ? [
                "✅ Toutes les statistiques sont cohérentes",
                "✅ Le filtrage par statut fonctionne correctement",
                "✅ Les APIs retournent des données correctes"
            ] : [
                "⚠️ Des incohérences ont été détectées",
                "🔍 Vérifiez les APIs qui échouent",
                "🛠️ Corrigez les problèmes avant la mise en production"
            ]
        });

    } catch (error: any) {
        console.error('❌ Erreur dans test cohérence statistiques:', error);
        return NextResponse.json({
            success: false,
            error: "Erreur lors du test de cohérence des statistiques",
            details: error.message
        }, { status: 500 });
    }
}
