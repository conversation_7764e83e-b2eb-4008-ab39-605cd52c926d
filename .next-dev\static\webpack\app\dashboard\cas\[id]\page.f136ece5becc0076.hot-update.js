"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/cas/[id]/page",{

/***/ "(app-pages-browser)/./app/cas/[id]/page.tsx":
/*!*******************************!*\
  !*** ./app/cas/[id]/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CasDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @prisma/client */ \"(app-pages-browser)/./node_modules/@prisma/client/index-browser.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/contexts/DataRefreshContext */ \"(app-pages-browser)/./app/contexts/DataRefreshContext.tsx\");\n/* harmony import */ var _app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/components/Skeleton */ \"(app-pages-browser)/./app/components/Skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_PlusCircleIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,PlusCircleIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_PlusCircleIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,PlusCircleIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_PlusCircleIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,PlusCircleIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_PlusCircleIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,PlusCircleIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _app_components_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/components/switch */ \"(app-pages-browser)/./app/components/switch.tsx\");\n/* harmony import */ var _app_components_ResolutionSelect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/components/ResolutionSelect */ \"(app-pages-browser)/./app/components/ResolutionSelect.tsx\");\n/* harmony import */ var _app_components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/components/CasStatusBadge */ \"(app-pages-browser)/./app/components/CasStatusBadge.tsx\");\n/* harmony import */ var _app_components_CasKMLEditor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/components/CasKMLEditor */ \"(app-pages-browser)/./app/components/CasKMLEditor.tsx\");\n/* harmony import */ var _app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/components/RoleBasedAccess */ \"(app-pages-browser)/./app/components/RoleBasedAccess.tsx\");\n/* harmony import */ var _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/hooks/usePermissions */ \"(app-pages-browser)/./lib/hooks/usePermissions.ts\");\n// app/cas/[id]/page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Correction des imports dynamiques pour retourner le composant par défaut\nconst LazyLoadingSpinner = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/app/components/LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\cas\\\\[id]\\\\page.tsx -> \" + \"@/app/components/LoadingSpinner\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            height: 32,\n            width: 32\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 55,\n            columnNumber: 34\n        }, undefined)\n});\n_c = LazyLoadingSpinner;\nconst LazyFormError = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_app_components_FormError_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/app/components/FormError */ \"(app-pages-browser)/./app/components/FormError.tsx\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\cas\\\\[id]\\\\page.tsx -> \" + \"@/app/components/FormError\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            height: 20,\n            width: 200\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 34\n        }, undefined)\n});\n_c1 = LazyFormError;\nconst LazyButton = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/app/components/Button */ \"(app-pages-browser)/./app/components/Button.tsx\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\cas\\\\[id]\\\\page.tsx -> \" + \"@/app/components/Button\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            height: 36,\n            width: 100\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 63,\n            columnNumber: 34\n        }, undefined)\n});\n_c2 = LazyButton;\nconst LazyInput = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_app_components_Input_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/app/components/Input */ \"(app-pages-browser)/./app/components/Input.tsx\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\cas\\\\[id]\\\\page.tsx -> \" + \"@/app/components/Input\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            height: 36,\n            width: 200\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 67,\n            columnNumber: 34\n        }, undefined)\n});\n_c3 = LazyInput;\nconst LazyTextArea = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_app_components_TextArea_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/app/components/TextArea */ \"(app-pages-browser)/./app/components/TextArea.tsx\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\cas\\\\[id]\\\\page.tsx -> \" + \"@/app/components/TextArea\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            height: 80,\n            width: 200\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 71,\n            columnNumber: 34\n        }, undefined)\n});\n_c4 = LazyTextArea;\nconst LazyModal = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_app_components_Modal_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/app/components/Modal */ \"(app-pages-browser)/./app/components/Modal.tsx\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\cas\\\\[id]\\\\page.tsx -> \" + \"@/app/components/Modal\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            height: 200,\n            width: 400\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 75,\n            columnNumber: 34\n        }, undefined)\n});\n_c5 = LazyModal;\n// Import du composant de carte personnalisé\nconst CasMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_c6 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_app_components_CasMap_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/app/components/CasMap */ \"(app-pages-browser)/./app/components/CasMap.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\cas\\\\[id]\\\\page.tsx -> \" + \"@/app/components/CasMap\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-[300px] bg-gray-200 rounded animate-pulse\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 82,\n            columnNumber: 9\n        }, undefined)\n});\n_c7 = CasMap;\nfunction CasDetailsPage() {\n    var _casMain_problematique, _casMain_problematique1, _casMain_user, _casMain_user1;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const casId = params.id;\n    // Hooks pour la gestion des rafraîchissements\n    const { afterCreate, afterUpdate, afterDelete } = (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useOperationRefresh)();\n    // Séparez les states\n    const [casMain, setCasMain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [blocages, setBlocages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [secteurs, setSecteurs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [problematiques, setProblematiques] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Ajout pour les problématiques\n    const [encrages, setEncrages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Ajout pour les encrages\n    const [isLoadingMain, setIsLoadingMain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingBlocages, setIsLoadingBlocages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingProblematiques, setIsLoadingProblematiques] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingEncrages, setIsLoadingEncrages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [blocageForm, setBlocageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        description: \"\",\n        secteurId: \"\",\n        blocageDate: \"\"\n    });\n    const [blocageFormData, setBlocageFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        description: \"\",\n        secteurId: \"\",\n        blocageDate: \"\"\n    });\n    const [isSubmittingBlocage, setIsSubmittingBlocage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [blocageError, setBlocageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [blocageToDeleteId, setBlocageToDeleteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New states for editing Cas details\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFormData, setEditFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmittingEdit, setIsSubmittingEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editError, setEditError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSolutionModalOpen, setIsSolutionModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentBlocageForSolution, setCurrentBlocageForSolution] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [solutionDate, setSolutionDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Stockera la date au format YYYY-MM-DD\n    const [solutionDateError, setSolutionDateError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [solutionError, setSolutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [solutionResolution, setSolutionResolution] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ACCEPTE\");\n    const [solutionDetailResolution, setSolutionDetailResolution] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isBlocageFormModalOpen, setIsBlocageFormModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // <-- Add this line\n    const [allCommunes, setAllCommunes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingCommunes, setIsLoadingCommunes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errorCommunes, setErrorCommunes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [geojson, setGeojson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // États pour la gestion des KML\n    const [kmlError, setKmlError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fonctions de gestion des KML\n    const handleKMLUpdated = (casId, kmlData, fileName)=>{\n        if (casMain) {\n            setCasMain({\n                ...casMain,\n                kmlData,\n                kmlFileName: fileName\n            });\n        }\n        setKmlError(null);\n    };\n    const handleKMLRemoved = (casId)=>{\n        if (casMain) {\n            setCasMain({\n                ...casMain,\n                kmlData: null,\n                kmlFileName: undefined\n            });\n        }\n        setKmlError(null);\n    };\n    const handleKMLError = (error)=>{\n        setKmlError(error);\n        setTimeout(()=>setKmlError(null), 5000);\n    };\n    // Fonction pour mettre à jour le statut du cas basé sur les résolutions des blocages\n    const checkAndUpdateCasRegularisationStatus = async (blocagesList)=>{\n        // Calculer le nouveau statut basé sur les résolutions\n        const resolutions = blocagesList.map((b)=>b.resolution || \"ATTENTE\");\n        const newRegularisationStatus = resolutions.length > 0 && resolutions.every((r)=>r === \"ACCEPTE\");\n        if (casMain && casMain.regularisation !== newRegularisationStatus) {\n            // Mettre à jour l'état local\n            setCasMain((prevCas)=>prevCas ? {\n                    ...prevCas,\n                    regularisation: newRegularisationStatus,\n                    blocage: blocagesList\n                } : null);\n            // Mettre à jour sur le serveur\n            try {\n                await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.put(\"/api/cas/\".concat(casId), {\n                    regularisation: newRegularisationStatus\n                });\n            } catch (error) {\n                console.error(\"Failed to update Cas regularisation status on server:\", error);\n            }\n        } else if (casMain) {\n            // Même si le statut de régularisation n'a pas changé,\n            // mettre à jour les blocages pour le badge de statut\n            setCasMain((prevCas)=>prevCas ? {\n                    ...prevCas,\n                    blocage: blocagesList\n                } : null);\n        }\n    };\n    // Populate edit form when cas data is loaded and isEditing is true\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasDetailsPage.useEffect\": ()=>{\n            if (casMain && isEditing) {\n                var _casMain_problematique_encrage, _casMain_problematique;\n                setEditFormData({\n                    nom: casMain.nom || \"\",\n                    nif: casMain.nif,\n                    nin: casMain.nin,\n                    superficie: casMain.superficie || undefined,\n                    regularisation: casMain.regularisation || false,\n                    observation: casMain.observation,\n                    problematiqueId: casMain.problematiqueId || \"\",\n                    genre: casMain.genre || undefined,\n                    date_depot: casMain.date_depot ? new Date(casMain.date_depot).toISOString().split(\"T\")[0] : \"\",\n                    communeIds: casMain.communes ? casMain.communes.map({\n                        \"CasDetailsPage.useEffect\": (c)=>String(c.id)\n                    }[\"CasDetailsPage.useEffect\"]) : [],\n                    encrageId: ((_casMain_problematique = casMain.problematique) === null || _casMain_problematique === void 0 ? void 0 : (_casMain_problematique_encrage = _casMain_problematique.encrage) === null || _casMain_problematique_encrage === void 0 ? void 0 : _casMain_problematique_encrage.id) || \"\"\n                });\n            } else if (!isEditing) {\n                setEditFormData({}); // Clear form when not editing\n                setEditError(null); // Clear edit errors\n            }\n        }\n    }[\"CasDetailsPage.useEffect\"], [\n        casMain,\n        isEditing\n    ]);\n    // Pré-remplir le geojson à l’ouverture de l’édition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasDetailsPage.useEffect\": ()=>{\n            if (casMain && isEditing) {\n                setGeojson(casMain.geojson || null);\n            }\n        }\n    }[\"CasDetailsPage.useEffect\"], [\n        casMain,\n        isEditing\n    ]);\n    // Handler for commune checkbox changes\n    const handleCommuneChange = (communeId)=>{\n        setEditFormData((prev)=>{\n            const currentCommuneIds = prev.communeIds || [];\n            const newCommuneIds = currentCommuneIds.includes(communeId) ? currentCommuneIds.filter((id)=>id !== communeId) : [\n                ...currentCommuneIds,\n                communeId\n            ];\n            return {\n                ...prev,\n                communeIds: newCommuneIds\n            };\n        });\n    };\n    // 1. Charger les infos principales\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasDetailsPage.useEffect\": ()=>{\n            async function fetchMain() {\n                console.time(\"fetchMain\");\n                setIsLoadingMain(true);\n                try {\n                    const data = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/cas/\".concat(casId, \"?mainOnly=true\"));\n                    setCasMain(data);\n                } finally{\n                    setIsLoadingMain(false);\n                    console.timeEnd(\"fetchMain\");\n                }\n            }\n            fetchMain();\n        }\n    }[\"CasDetailsPage.useEffect\"], [\n        casId\n    ]);\n    // 2. Charger le reste en parallèle\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasDetailsPage.useEffect\": ()=>{\n            if (!casMain) return;\n            ({\n                \"CasDetailsPage.useEffect\": async ()=>{\n                    setIsLoadingBlocages(true);\n                    setIsLoadingCommunes(true);\n                    setIsLoadingProblematiques(true);\n                    setIsLoadingEncrages(true);\n                    // Ajoutez d'autres isLoading si besoin\n                    console.time(\"fetchBlocages\");\n                    console.time(\"fetchAllCommunes\");\n                    console.time(\"fetchProblematiques\");\n                    console.time(\"fetchEncrages\");\n                    try {\n                        const [blocagesData, communesData, problematiquesData, encragesData] = await Promise.all([\n                            _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/cas/\".concat(casId, \"/blocages\")),\n                            _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/communes\"),\n                            _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/problematiques\"),\n                            _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/encrages\")\n                        ]);\n                        setBlocages(blocagesData);\n                        setAllCommunes(communesData);\n                        setProblematiques(problematiquesData);\n                        setEncrages(encragesData);\n                    } catch (err) {\n                        console.error(\"Erreur lors du chargement parallèle :\", err);\n                    } finally{\n                        setIsLoadingBlocages(false);\n                        setIsLoadingCommunes(false);\n                        setIsLoadingProblematiques(false);\n                        setIsLoadingEncrages(false);\n                        // Ajoutez d'autres setIsLoading si besoin\n                        console.timeEnd(\"fetchBlocages\");\n                        console.timeEnd(\"fetchAllCommunes\");\n                        console.timeEnd(\"fetchProblematiques\");\n                        console.timeEnd(\"fetchEncrages\");\n                    }\n                }\n            })[\"CasDetailsPage.useEffect\"]();\n        }\n    }[\"CasDetailsPage.useEffect\"], [\n        casMain,\n        casId\n    ]);\n    const fetchAllCommunes = async ()=>{\n        console.time(\"fetchAllCommunes\");\n        setIsLoadingCommunes(true);\n        setErrorCommunes(null);\n        try {\n            const data = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/communes\");\n            setAllCommunes(data);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Failed to fetch communes:\", err);\n            setErrorCommunes(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || \"Failed to fetch communes\");\n        } finally{\n            setIsLoadingCommunes(false);\n            console.timeEnd(\"fetchAllCommunes\");\n        }\n    };\n    const fetchProblematiques = async ()=>{\n        console.time(\"fetchProblematiques\");\n        try {\n            const data = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/problematiques\");\n            setProblematiques(data);\n        } catch (err) {\n            console.error(\"Failed to fetch problematiques:\", err);\n        // Gérer l'erreur (par exemple, afficher un message à l'utilisateur)\n        } finally{\n            console.timeEnd(\"fetchProblematiques\");\n        }\n    };\n    const fetchEncrages = async ()=>{\n        console.time(\"fetchEncrages\");\n        try {\n            const data = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/encrages\");\n            setEncrages(data);\n        } catch (err) {\n            console.error(\"Failed to fetch encrages:\", err);\n        // Gérer l'erreur\n        } finally{\n            console.timeEnd(\"fetchEncrages\");\n        }\n    };\n    async function handleToggleRegularisation(blocageId, currentStatus) {\n        const blocageToUpdate = blocages.find((b)=>b.id === blocageId);\n        if (!blocageToUpdate) return;\n        // Toujours ouvrir le modal pour permettre de choisir la résolution\n        setCurrentBlocageForSolution(blocageToUpdate);\n        setSolutionDate(blocageToUpdate.solution ? new Date(blocageToUpdate.solution).toISOString().split(\"T\")[0] : \"\");\n        // Si le blocage est déjà régularisé, préremplir avec les valeurs existantes\n        if (currentStatus) {\n            setSolutionResolution(blocageToUpdate.resolution || \"ACCEPTE\");\n            setSolutionDetailResolution(blocageToUpdate.detail_resolution || \"\");\n        } else {\n            // Nouveau blocage, valeurs par défaut\n            setSolutionResolution(\"ACCEPTE\");\n            setSolutionDetailResolution(\"\");\n        }\n        setSolutionError(\"\");\n        setIsSolutionModalOpen(true);\n    }\n    function closeSolutionModal() {\n        setIsSolutionModalOpen(false);\n        setCurrentBlocageForSolution(null);\n        setSolutionDate(\"\");\n        setSolutionResolution(\"ACCEPTE\");\n        setSolutionDetailResolution(\"\");\n        setSolutionError(\"\");\n    }\n    const handleSolutionSubmit = async (e)=>{\n        if (e) e.preventDefault();\n        if (!currentBlocageForSolution || !solutionDate) {\n            setSolutionDateError(\"Solution date is required\");\n            return;\n        }\n        setSolutionDateError(null);\n        try {\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.put(\"/api/blocages/\".concat(currentBlocageForSolution.id), {\n                regularise: solutionResolution === \"ACCEPTE\",\n                solution: new Date(solutionDate).toISOString(),\n                resolution: solutionResolution,\n                detail_resolution: solutionDetailResolution || null\n            });\n            if (!response) {\n                throw new Error(\"Failed to submit solution date\");\n            }\n            // Mettre à jour le blocage dans l'état local\n            const updatedBlocages = blocages.map((b)=>b.id === currentBlocageForSolution.id ? {\n                    ...b,\n                    regularise: solutionResolution === \"ACCEPTE\",\n                    solution: new Date(solutionDate),\n                    resolution: solutionResolution,\n                    detail_resolution: solutionDetailResolution || null\n                } : b);\n            setBlocages(updatedBlocages);\n            // Mettre à jour le statut du cas basé sur les nouvelles résolutions\n            await checkAndUpdateCasRegularisationStatus(updatedBlocages);\n            // Déclencher le rafraîchissement des données dans toute l'application\n            await afterUpdate(\"blocage\");\n            setIsSolutionModalOpen(false);\n            setSolutionDate(\"\");\n            setSolutionResolution(\"ACCEPTE\");\n            setSolutionDetailResolution(\"\");\n            setCurrentBlocageForSolution(null);\n        } catch (error) {\n            console.error(\"Error submitting solution date:\", error);\n            setSolutionDateError(error.message || \"Failed to submit solution\");\n        }\n    };\n    const fetchCasDetails = async ()=>{\n        setIsLoadingMain(true);\n        setError(null);\n        try {\n            const casData = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/cas/\".concat(casId));\n            setCasMain(casData);\n            const updatedBlocages = casData.blocage || [];\n            setBlocages(updatedBlocages);\n            // Call checkAndUpdateCasRegularisationStatus with the newly fetched blocages\n            if (casData) {\n                // Ensure casData is not null\n                await checkAndUpdateCasRegularisationStatus(updatedBlocages);\n            }\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Failed to fetch case details:\", err);\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || \"Failed to fetch case details\");\n        }\n        setIsLoadingMain(false);\n    };\n    const fetchSecteurs = async ()=>{\n        try {\n            const secteursData = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/secteurs\");\n            setSecteurs(secteursData);\n        } catch (err) {\n            console.error(\"Failed to fetch sectors:\", err);\n        // Handle error silently\n        }\n    };\n    const handleBlocageFormChange = (e)=>{\n        setBlocageForm({\n            ...blocageForm,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleAddBlocage = async (e)=>{\n        e.preventDefault();\n        if (!blocageForm.description || !blocageForm.secteurId) {\n            setBlocageError(\"La description et le secteur sont requis.\");\n            return;\n        }\n        setIsSubmittingBlocage(true);\n        setBlocageError(null);\n        try {\n            const newBlocage = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.post(\"/api/blocages\", {\n                ...blocageForm,\n                casId: casId,\n                blocage: blocageForm.blocageDate ? new Date(blocageForm.blocageDate) : null\n            });\n            // Recharge les blocages via le nouveau endpoint scoped\n            const data = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/cas/\".concat(casId, \"/blocages\"));\n            setBlocages(data);\n            // Mettre à jour le statut du cas basé sur les nouvelles résolutions\n            await checkAndUpdateCasRegularisationStatus(data);\n            setBlocageForm({\n                description: \"\",\n                secteurId: \"\",\n                blocageDate: \"\"\n            });\n            setIsBlocageFormModalOpen(false);\n            // Déclencher le rafraîchissement via le système centralisé\n            await afterCreate(\"blocage\");\n        } catch (err) {\n            setBlocageError(err.message || \"Erreur lors de l'ajout du blocage.\");\n        } finally{\n            setIsSubmittingBlocage(false);\n        }\n    };\n    const initiateDeleteBlocage = (id)=>{\n        setBlocageToDeleteId(id);\n        setIsDeleteModalOpen(true);\n    };\n    const confirmDeleteBlocage = async ()=>{\n        if (!blocageToDeleteId) return;\n        setError(null);\n        try {\n            await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.delete(\"/api/blocages/\".concat(blocageToDeleteId));\n            const updatedBlocages = blocages.filter((b)=>b.id !== blocageToDeleteId);\n            setBlocages(updatedBlocages);\n            await checkAndUpdateCasRegularisationStatus(updatedBlocages); // Add this line\n            setBlocageToDeleteId(null);\n            setIsDeleteModalOpen(false);\n            // Déclencher le rafraîchissement via le système centralisé\n            await afterDelete(\"blocage\");\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Error deleting blocage:\", err);\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || \"Error deleting blocage.\");\n            setBlocageToDeleteId(null);\n            setIsDeleteModalOpen(false);\n        }\n    };\n    const cancelDeleteBlocage = ()=>{\n        setBlocageToDeleteId(null);\n        setIsDeleteModalOpen(false);\n    };\n    // Handlers for editing Cas details\n    const handleEditFormChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        if (type === \"number\") {\n            processedValue = value === \"\" ? undefined : parseFloat(value);\n        }\n        // For checkbox, specific handling if you use a standard HTML input\n        if (e.target.type === \"checkbox\") {\n            processedValue = e.target.checked;\n        }\n        setEditFormData((prev)=>({\n                ...prev,\n                [name]: processedValue\n            }));\n    };\n    const handleEditSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmittingEdit(true);\n        setEditError(null);\n        try {\n            // Convertir les IDs de communes en objets complets\n            const selectedCommunes = editFormData.communeIds ? allCommunes.filter((commune)=>editFormData.communeIds.includes(String(commune.id))).map((commune)=>({\n                    nom: commune.nom,\n                    wilayaId: commune.wilayaId\n                })) : [];\n            const { communeIds, ...otherFormData } = editFormData;\n            const dataToSend = {\n                ...otherFormData,\n                communes: selectedCommunes,\n                geojson: geojson || null\n            };\n            console.log(\"Données envoyées pour la modification:\", dataToSend);\n            const updatedCas = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.put(\"/api/cas/\".concat(casId), dataToSend);\n            setIsEditing(false);\n            // Déclencher le rafraîchissement via le système centralisé\n            await afterUpdate(\"cas\");\n        } catch (err) {\n            console.error(\"Erreur lors de la modification:\", err);\n            setEditError(\"Erreur lors de la modification du cas.\");\n        }\n        setIsSubmittingEdit(false);\n    };\n    // Charger les communes, problématiques et encrages au montage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasDetailsPage.useEffect\": ()=>{\n            fetchAllCommunes();\n            fetchProblematiques();\n            fetchEncrages();\n        }\n    }[\"CasDetailsPage.useEffect\"], []);\n    // Enregistrer les callbacks de rafraîchissement\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh)(\"cas-details\", fetchCasDetails, [\n        casId\n    ]);\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh)(\"communes-details\", fetchAllCommunes, []);\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh)(\"problematiques-details\", fetchProblematiques, []);\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh)(\"encrages-details\", fetchEncrages, []);\n    // Charger les secteurs au montage pour le formulaire de blocage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasDetailsPage.useEffect\": ()=>{\n            fetchSecteurs();\n        }\n    }[\"CasDetailsPage.useEffect\"], []);\n    // Ajout d'un état pour l'utilisateur courant\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Permissions hook for role-based access control\n    const { canWrite, canDelete, canUploadFiles, isViewer } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_13__.usePermissions)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasDetailsPage.useEffect\": ()=>{\n            async function loadCurrentUser() {\n                try {\n                    const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.apiClient.get(\"/api/auth/me\");\n                    setCurrentUser(response);\n                } catch (error) {\n                    setCurrentUser(null);\n                }\n            }\n            loadCurrentUser();\n        }\n    }[\"CasDetailsPage.useEffect\"], []);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-1 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyFormError, {\n                    message: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                    lineNumber: 764,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                    onClick: fetchCasDetails,\n                    className: \"mt-4\",\n                    children: \"R\\xe9essayer\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                    lineNumber: 765,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 763,\n            columnNumber: 13\n        }, this);\n    }\n    // Remove the global isLoadingMain loader so the page always renders\n    // if (isLoadingMain) {\n    //     return <LazyLoadingSpinner />;\n    // }\n    // Main component render\n    // Prevent rendering sections if casMain is null (still loading)\n    if (!casMain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-1 text-center\",\n            children: isLoadingMain ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyLoadingSpinner, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                lineNumber: 784,\n                columnNumber: 21\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Cas non trouv\\xe9.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/cas\",\n                        className: \"text-purple-600 hover:underline\",\n                        children: \"Retour \\xe0 la liste des cas\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 788,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 782,\n            columnNumber: 13\n        }, this);\n    }\n    // Memoized BlocageRow component\n    const BlocageRow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(function BlocageRow(param) {\n        let { blocage, blocageToDeleteId, onDelete, onCancelDelete, onConfirmDelete, onToggleRegularisation, isDeleteModalOpen, setIsDeleteModalOpen } = param;\n        var _blocage_secteur;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-white rounded-md shadow border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-start mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-800 font-medium\",\n                                    children: blocage.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: [\n                                        \"Secteur: \",\n                                        ((_blocage_secteur = blocage.secteur) === null || _blocage_secteur === void 0 ? void 0 : _blocage_secteur.nom) || \"N/A\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 834,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"R\\xe9solution:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_ResolutionSelect__WEBPACK_IMPORTED_MODULE_9__.ResolutionBadge, {\n                                            resolution: blocage.resolution || \"ATTENTE\",\n                                            className: \"text-xs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 837,\n                                    columnNumber: 29\n                                }, this),\n                                blocage.detail_resolution && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 mt-1 italic\",\n                                    children: blocage.detail_resolution\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 flex-shrink-0\",\n                            children: blocageToDeleteId === blocage.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                        variant: \"primary\",\n                                        size: \"icon\",\n                                        onClick: onConfirmDelete,\n                                        className: \"p-1.5 rounded-full bg-green-100 hover:bg-green-200 text-green-700\",\n                                        \"aria-label\": \"Confirmer la suppression\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_PlusCircleIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                        variant: \"secondary\",\n                                        size: \"icon\",\n                                        onClick: ()=>setIsDeleteModalOpen(false),\n                                        className: \"p-1.5 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700\",\n                                        \"aria-label\": \"Annuler la suppression\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_PlusCircleIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 876,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 867,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_12__.DeleteAccess, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                    variant: \"destructive\",\n                                    size: \"icon\",\n                                    onClick: ()=>onDelete(blocage.id),\n                                    className: \"ml-2 flex-shrink-0 p-1.5 rounded-full hover:bg-red-100 hover:text-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                    \"aria-label\": \"Supprimer le blocage\",\n                                    title: !canDelete ? \"Vous n'avez pas les permissions pour supprimer des blocages\" : undefined,\n                                    disabled: !canDelete,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_PlusCircleIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 881,\n                                    columnNumber: 37\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 880,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 855,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                    lineNumber: 829,\n                    columnNumber: 21\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mt-2 pt-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_12__.WriteAccess, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex h-6 w-11 items-center rounded-full bg-gray-200 border border-gray-300 opacity-50 cursor-not-allowed\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-block h-4 w-4 rounded-full bg-white shadow transform transition-transform \".concat(blocage.regularise ? \"translate-x-6\" : \"translate-x-1\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 41\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 37\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                        checked: blocage.regularise,\n                                        onChange: ()=>onToggleRegularisation(blocage.id, blocage.regularise),\n                                        disabled: !canWrite,\n                                        className: blocage.resolution === \"ACCEPTE\" ? \"bg-green-600 border-green-600 focus:ring-green-500\" : blocage.resolution === \"REJETE\" ? \"bg-red-600 border-red-600 focus:ring-red-500\" : blocage.resolution === \"AJOURNE\" ? \"bg-orange-600 border-orange-600 focus:ring-orange-500\" : \"bg-gray-400 border-gray-400 focus:ring-gray-500\" + \" inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 915,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 902,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm font-medium cursor-pointer \".concat(blocage.resolution === \"ACCEPTE\" ? \"text-green-700\" : blocage.resolution === \"REJETE\" ? \"text-red-700\" : blocage.resolution === \"AJOURNE\" ? \"text-orange-700\" : \"text-gray-600\"),\n                                    onClick: ()=>onToggleRegularisation(blocage.id, blocage.regularise),\n                                    children: blocage.resolution === \"ACCEPTE\" ? \"Accepté\" : blocage.resolution === \"REJETE\" ? \"Rejeté\" : blocage.resolution === \"AJOURNE\" ? \"Ajourné\" : \"En attente\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 29\n                                }, this),\n                                blocage.regularise && blocage.solution && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-3 text-sm text-green-700 \",\n                                    children: [\n                                        \"Solutionn\\xe9 le:\",\n                                        \" \",\n                                        new Date(blocage.solution).toLocaleDateString(\"fr-CA\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 901,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: blocage.blocage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"Ajout\\xe9 le:\",\n                                    \" \",\n                                    new Date(blocage.blocage).toLocaleDateString(\"fr-CA\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                    lineNumber: 900,\n                    columnNumber: 21\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 828,\n            columnNumber: 17\n        }, this);\n    });\n    // Memoized status badge component\n    const CasStatusBadgeLocal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(function CasStatusBadgeLocal(param) {\n        let { casData } = param;\n        const status = (0,_app_components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__.determineCasStatus)((casData === null || casData === void 0 ? void 0 : casData.blocage) || []);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"ml-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__.CasStatusBadgeWithText, {\n                status: status\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                lineNumber: 997,\n                columnNumber: 21\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n            lineNumber: 996,\n            columnNumber: 17\n        }, this);\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-2 md:p-2 bg-gray-50 min-h-screen\",\n        children: [\n            isLoadingMain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50 transition-opacity duration-300 animate-fade-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyLoadingSpinner, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                    lineNumber: 1008,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                lineNumber: 1007,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyModal, {\n                isOpen: isBlocageFormModalOpen,\n                onClose: ()=>setIsBlocageFormModalOpen(false),\n                title: \"Ajouter une Nouvelle Contrainte\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleAddBlocage,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"description\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Description de la contrainte\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1019,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyTextArea, {\n                                    name: \"description\",\n                                    id: \"description\",\n                                    value: blocageForm.description,\n                                    onChange: handleBlocageFormChange,\n                                    required: true,\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1025,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1018,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"secteurId\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Secteur Concern\\xe9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1035,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    name: \"secteurId\",\n                                    id: \"secteurId\",\n                                    value: blocageForm.secteurId,\n                                    onChange: handleBlocageFormChange,\n                                    required: true,\n                                    className: \"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"S\\xe9lectionner un secteur\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1049,\n                                            columnNumber: 29\n                                        }, this),\n                                        secteurs.map((secteur)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: secteur.id,\n                                                children: secteur.nom\n                                            }, secteur.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1051,\n                                                columnNumber: 33\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1041,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1034,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"blocageDate\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Date de blocage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1059,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyInput, {\n                                    type: \"date\",\n                                    name: \"blocageDate\",\n                                    id: \"blocageDate\",\n                                    value: blocageForm.blocageDate,\n                                    onChange: handleBlocageFormChange,\n                                    className: \"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1065,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1058,\n                            columnNumber: 21\n                        }, this),\n                        blocageError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyFormError, {\n                            message: blocageError\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1075,\n                            columnNumber: 38\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                    type: \"button\",\n                                    variant: \"secondary\",\n                                    onClick: ()=>setIsBlocageFormModalOpen(false),\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1077,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_12__.WriteAccess, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                        type: \"submit\",\n                                        disabled: isSubmittingBlocage || !canWrite,\n                                        title: !canWrite ? \"Vous n'avez pas les permissions pour ajouter des blocages\" : undefined,\n                                        children: isSubmittingBlocage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyLoadingSpinner, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1095,\n                                            columnNumber: 37\n                                        }, this) : \"Ajouter\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1085,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1084,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1076,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                    lineNumber: 1017,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                lineNumber: 1012,\n                columnNumber: 13\n            }, this),\n            isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleEditSubmit,\n                className: \"bg-white shadow-lg rounded-xl p-6 border border-gray-200 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-6\",\n                        children: \"Modifier le Cas\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1110,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Communes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1115,\n                                columnNumber: 25\n                            }, this),\n                            isLoadingCommunes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyLoadingSpinner, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1118,\n                                columnNumber: 47\n                            }, this),\n                            errorCommunes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyFormError, {\n                                message: errorCommunes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1120,\n                                columnNumber: 29\n                            }, this),\n                            !isLoadingCommunes && !errorCommunes && allCommunes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-2 max-h-60 overflow-y-auto p-2 border rounded\",\n                                children: allCommunes.map((commune)=>{\n                                    var _editFormData_communeIds;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"commune-\".concat(commune.id),\n                                                name: \"communes\",\n                                                value: commune.id,\n                                                checked: ((_editFormData_communeIds = editFormData.communeIds) === null || _editFormData_communeIds === void 0 ? void 0 : _editFormData_communeIds.includes(String(commune.id))) || false,\n                                                onChange: ()=>handleCommuneChange(String(commune.id)),\n                                                className: \"h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1131,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"commune-\".concat(commune.id),\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: commune.nom\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1148,\n                                                columnNumber: 45\n                                            }, this)\n                                        ]\n                                    }, commune.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1127,\n                                        columnNumber: 41\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1125,\n                                columnNumber: 33\n                            }, this),\n                            !isLoadingCommunes && !errorCommunes && allCommunes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Aucune commune disponible.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1161,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1114,\n                        columnNumber: 21\n                    }, this),\n                    editError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyFormError, {\n                        message: editError\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1166,\n                        columnNumber: 35\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"nom_edit\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Nom du Cas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1170,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyInput, {\n                                        type: \"text\",\n                                        name: \"nom\",\n                                        id: \"nom_edit\",\n                                        value: editFormData.nom || \"\",\n                                        onChange: handleEditFormChange,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1176,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1169,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"date_depot_edit\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Date d\\xe9p\\xf4t dossier\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1186,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyInput, {\n                                        type: \"date\",\n                                        name: \"date_depot\",\n                                        id: \"date_depot_edit\",\n                                        value: editFormData.date_depot || \"\",\n                                        onChange: handleEditFormChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1192,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1185,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"nif_edit\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"NIF\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1201,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyInput, {\n                                        type: \"text\",\n                                        name: \"nif\",\n                                        id: \"nif_edit\",\n                                        value: editFormData.nif || \"\",\n                                        onChange: handleEditFormChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1207,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1200,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"nin_edit\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"NIN\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1216,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyInput, {\n                                        type: \"text\",\n                                        name: \"nin\",\n                                        id: \"nin_edit\",\n                                        value: editFormData.nin || \"\",\n                                        onChange: handleEditFormChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1222,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1215,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"superficie_edit\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Superficie (Ha)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1231,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyInput, {\n                                        type: \"number\",\n                                        name: \"superficie\",\n                                        id: \"superficie_edit\",\n                                        value: editFormData.superficie || \"\",\n                                        onChange: handleEditFormChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1237,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1230,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"genre_edit\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Genre\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1246,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        name: \"genre\",\n                                        id: \"genre_edit\",\n                                        value: editFormData.genre || \"\",\n                                        onChange: handleEditFormChange,\n                                        className: \"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"S\\xe9lectionner un genre\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: _prisma_client__WEBPACK_IMPORTED_MODULE_17__.TypePersonne.PERSONNE_PHYSIQUE,\n                                                children: \"Personne Physique\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: _prisma_client__WEBPACK_IMPORTED_MODULE_17__.TypePersonne.PERSONNE_MORALE,\n                                                children: \"Personne Morale\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1263,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1252,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1245,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"problematiqueId_edit\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Probl\\xe9matique\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1270,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        name: \"problematiqueId\",\n                                        id: \"problematiqueId_edit\",\n                                        value: editFormData.problematiqueId || \"\",\n                                        onChange: handleEditFormChange,\n                                        className: \"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"S\\xe9lectionner une probl\\xe9matique\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1283,\n                                                columnNumber: 33\n                                            }, this),\n                                            problematiques.filter((prob)=>prob.encrageId === editFormData.encrageId).map((prob)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: prob.id,\n                                                    children: prob.problematique\n                                                }, prob.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1293,\n                                                    columnNumber: 41\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1276,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1269,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"encrageId_edit\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Encrage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1305,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        name: \"encrageId\",\n                                        id: \"encrageId_edit\",\n                                        value: editFormData.encrageId || \"\",\n                                        onChange: handleEditFormChange,\n                                        className: \"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"S\\xe9lectionner un encrage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1318,\n                                                columnNumber: 33\n                                            }, this),\n                                            encrages.map((encrage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: encrage.id,\n                                                    children: encrage.nom\n                                                }, encrage.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1322,\n                                                    columnNumber: 37\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1311,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1304,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1168,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"observation_edit\",\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Observation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1330,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyTextArea, {\n                                name: \"observation\",\n                                id: \"observation_edit\",\n                                value: editFormData.observation || \"\",\n                                onChange: handleEditFormChange,\n                                rows: 3\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1336,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1329,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flex justify-end space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                type: \"button\",\n                                onClick: ()=>setIsEditing(false),\n                                variant: \"secondary\",\n                                children: \"Annuler\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1355,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_12__.WriteAccess, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                    type: \"submit\",\n                                    isLoading: isSubmittingEdit,\n                                    disabled: isSubmittingEdit || !canWrite,\n                                    title: !canWrite ? \"Vous n'avez pas les permissions pour modifier ce dossier\" : undefined,\n                                    children: \"Enregistrer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1363,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1362,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1354,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                lineNumber: 1106,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-lg rounded-xl overflow-hidden border border-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-100 flex flex-col md:flex-row md:items-center md:justify-between gap-4 \",\n                        children: isLoadingMain ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            height: 48,\n                            width: \"100%\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1382,\n                            columnNumber: 29\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 truncate\",\n                                        children: casMain.nom\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1386,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CasStatusBadgeLocal, {\n                                        casData: casMain\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1389,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1385,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1380,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-end gap-4 min-w-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/cas/\".concat(casId, \"/cartographie\"),\n                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1406,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1400,\n                                            columnNumber: 33\n                                        }, this),\n                                        \"Cartographie\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1396,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_12__.WriteAccess, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                        onClick: ()=>setIsEditing(!isEditing),\n                                        variant: isEditing ? \"secondary\" : \"primary\",\n                                        title: !canWrite ? \"Vous n'avez pas les permissions pour modifier ce dossier\" : undefined,\n                                        disabled: !canWrite,\n                                        children: isEditing ? \"Annuler la Modification\" : \"Modifier le Dossier\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1416,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1415,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_12__.WriteAccess, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                        onClick: ()=>setIsBlocageFormModalOpen(true),\n                                        variant: \"primary\",\n                                        className: \"flex items-center space-x-2\",\n                                        title: !canWrite ? \"Vous n'avez pas les permissions pour ajouter des blocages\" : undefined,\n                                        disabled: !canWrite,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_PlusCircleIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1447,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Ajouter une Contrainte\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1448,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1434,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1433,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1395,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1394,\n                        columnNumber: 21\n                    }, this),\n                    isViewer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_12__.ReadOnlyMessage, {\n                            message: \"Vous \\xeates en mode lecture seule. Vous pouvez consulter toutes les informations du dossier mais ne pouvez pas les modifier.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1457,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1456,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-6 space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-gray-500\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zM6 8a2 2 0 11-4 0 2 2 0 014 0zM1.49 15.326a.78.78 0 01-.358-.442 3 3 0 014.308-3.516 6.484 6.484 0 00-1.905 3.959c-.023.222-.014.442.025.654a4.97 4.97 0 01-2.07-.655zM16.44 15.98a4.97 4.97 0 002.07-.654.78.78 0 00.357-.442 3 3 0 00-4.308-3.517 6.484 6.484 0 011.907 3.96 2.32 2.32 0 01-.026.654zM18 8a2 2 0 11-4 0 2 2 0 014 0zM5.304 16.19a.844.844 0 01-.277-.71 5 5 0 019.947 0 .843.843 0 01-.277.71A6.975 6.975 0 0110 18a6.974 6.974 0 01-4.696-1.81z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1470,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1465,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    \"Informations G\\xe9n\\xe9rales\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1464,\n                                                columnNumber: 33\n                                            }, this),\n                                            isLoadingMain ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                height: 120,\n                                                width: \"100%\",\n                                                className: \"transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1475,\n                                                columnNumber: 37\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                className: \"grid grid-cols-1 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between py-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500\",\n                                                                children: \"Identifiant\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1483,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: casMain.nif ? \"NIF: \".concat(casMain.nif) : casMain.nin ? \"NIN: \".concat(casMain.nin) : \"Non spécifié\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1486,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1482,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between py-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500\",\n                                                                children: \"Genre\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1495,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: casMain.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_17__.TypePersonne.PERSONNE_PHYSIQUE ? \"Personne Physique\" : casMain.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_17__.TypePersonne.PERSONNE_MORALE ? \"Personne Morale\" : \"Non spécifié\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1498,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1494,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between py-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500\",\n                                                                children: \"Commune(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1509,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: casMain.communes && casMain.communes.length > 0 ? casMain.communes.map((c)=>c.nom).join(\", \") : \"Non spécifiée\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1512,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1508,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between py-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500\",\n                                                                children: \"Superficie\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1522,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: casMain.superficie ? \"\".concat(casMain.superficie, \" Ha\") : \"Non spécifiée\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1525,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1521,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between py-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500\",\n                                                                children: \"Date d\\xe9p\\xf4t dossier\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1532,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: casMain.date_depot ? new Date(casMain.date_depot).toLocaleDateString(\"fr-CA\") : \"Non spécifiée\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1535,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1531,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1481,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1463,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-yellow-600\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zm0 16a3 3 0 01-3-3h6a3 3 0 01-3 3z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1556,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1551,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    \"Observations\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1550,\n                                                columnNumber: 33\n                                            }, this),\n                                            isLoadingMain ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                height: 40,\n                                                width: \"100%\",\n                                                className: \"transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1565,\n                                                columnNumber: 37\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 whitespace-pre-wrap\",\n                                                children: casMain.observation || \"Aucune observation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1571,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1549,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1462,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1587,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1582,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    \"Probl\\xe9matique Associ\\xe9e\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1581,\n                                                columnNumber: 33\n                                            }, this),\n                                            isLoadingMain ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                height: 80,\n                                                width: \"100%\",\n                                                className: \"transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1592,\n                                                columnNumber: 37\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-blue-700\",\n                                                                children: \"Probl\\xe9matique\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1600,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"mt-1 text-sm text-gray-900\",\n                                                                children: (_casMain_problematique = casMain.problematique) === null || _casMain_problematique === void 0 ? void 0 : _casMain_problematique.problematique\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1603,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1599,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-blue-700\",\n                                                                children: \"Encrage Juridique\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1611,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"mt-1 text-sm text-gray-900\",\n                                                                children: (_casMain_problematique1 = casMain.problematique) === null || _casMain_problematique1 === void 0 ? void 0 : _casMain_problematique1.encrage.nom\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1614,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1610,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1598,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1580,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-green-600\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1632,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1627,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    \"Informations du cr\\xe9ateur\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1626,\n                                                columnNumber: 33\n                                            }, this),\n                                            isLoadingMain ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                height: 60,\n                                                width: \"100%\",\n                                                className: \"transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1641,\n                                                columnNumber: 37\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-green-700\",\n                                                                children: \"Nom d'utilisateur\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1649,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"mt-1 text-sm text-gray-900\",\n                                                                children: (_casMain_user = casMain.user) === null || _casMain_user === void 0 ? void 0 : _casMain_user.username\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1652,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1648,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-green-700\",\n                                                                children: \"R\\xf4le\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1657,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"mt-1 text-sm text-gray-900\",\n                                                                children: (_casMain_user1 = casMain.user) === null || _casMain_user1 === void 0 ? void 0 : _casMain_user1.role\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1660,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1656,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1647,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1625,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1579,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1461,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                lineNumber: 1379,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 bg-white shadow-lg rounded-xl overflow-hidden border border-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-5 border-b border-gray-100 bg-purple-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 mr-2 text-purple-600\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M3.505 2.365A41.369 41.369 0 009 2c1.863 0 3.697.124 5.495.365 1.247.167 2.18 1.108 2.435 2.268a4.45 4.45 0 00-.577-.069 43.141 43.141 0 00-4.706 0C9.229 4.696 7.5 6.727 7.5 8.998v2.24c0 1.413.67 2.735 1.76 3.562l-2.98 2.98A.75.75 0 015 17.25v-3.443c-.501-.048-1-.106-1.495-.172C2.033 13.438 1 12.162 1 10.72V5.28c0-1.441 1.033-2.717 2.505-2.914z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1681,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M14 6c-.762 0-1.52.02-2.271.062C10.157 6.148 9 7.472 9 8.998v2.24c0 1.519 1.147 2.839 2.71 2.935.214.013.428.024.642.034.2.009.385.09.518.224l2.35 2.35a.75.75 0 001.28-.531v-2.07c1.453-.195 2.5-1.463 2.5-2.915V8.998c0-1.526-1.157-2.85-2.729-2.936A41.645 41.645 0 0014 6z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1682,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1676,\n                                    columnNumber: 25\n                                }, this),\n                                \"Niveaux de Contrainte\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1675,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1674,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-1\",\n                        children: isLoadingBlocages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Skeleton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            height: 80,\n                            width: \"100%\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1689,\n                            columnNumber: 25\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 space-y-3\",\n                            children: blocages.length > 0 ? blocages.map((blocage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BlocageRow, {\n                                    blocage: blocage,\n                                    blocageToDeleteId: blocageToDeleteId,\n                                    onDelete: initiateDeleteBlocage,\n                                    onCancelDelete: cancelDeleteBlocage,\n                                    onConfirmDelete: confirmDeleteBlocage,\n                                    onToggleRegularisation: handleToggleRegularisation,\n                                    isDeleteModalOpen: isDeleteModalOpen,\n                                    setIsDeleteModalOpen: setIsDeleteModalOpen\n                                }, blocage.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1694,\n                                    columnNumber: 37\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center py-8 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-12 h-12 mb-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        strokeWidth: \"2\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            d: \"M9.75 17L6 21m0 0l-3.75-4M6 21V3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1719,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1712,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Aucun point de blocage identifi\\xe9 pour ce cas.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1725,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1711,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1691,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1687,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                lineNumber: 1673,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyModal, {\n                isOpen: isDeleteModalOpen,\n                onClose: cancelDeleteBlocage,\n                title: \"\\xcates-vous s\\xfbr de vouloir supprimer ce blocage ? Cette action est irr\\xe9versible.\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex justify-end space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                            variant: \"secondary\",\n                            onClick: cancelDeleteBlocage,\n                            children: [\n                                \" \",\n                                \"Annuler\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1743,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                            variant: \"destructive\",\n                            onClick: confirmDeleteBlocage,\n                            children: \"Supprimer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1751,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                    lineNumber: 1742,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                lineNumber: 1737,\n                columnNumber: 13\n            }, this),\n            isSolutionModalOpen && currentBlocageForSolution && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyModal, {\n                isOpen: isSolutionModalOpen,\n                onClose: ()=>{\n                    setIsSolutionModalOpen(false);\n                    setCurrentBlocageForSolution(null); // Important de réinitialiser\n                    setSolutionDateError(\"\");\n                },\n                title: \" \".concat(currentBlocageForSolution.description),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSolutionSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"solutionDate_modal\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Date de R\\xe9solution\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1773,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyInput, {\n                                    type: \"date\",\n                                    name: \"solutionDate\",\n                                    id: \"solutionDate_modal\",\n                                    value: solutionDate,\n                                    onChange: (e)=>setSolutionDate(e.target.value),\n                                    required: true,\n                                    className: \"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1779,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1772,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"solutionResolution_modal\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"R\\xe9solution\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1793,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_ResolutionSelect__WEBPACK_IMPORTED_MODULE_9__.ResolutionSelect, {\n                                    value: solutionResolution,\n                                    onChange: setSolutionResolution,\n                                    className: \"mt-1 block w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1799,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1792,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"solutionDetailResolution_modal\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"D\\xe9tail de la r\\xe9solution\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1807,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyTextArea, {\n                                    name: \"solutionDetailResolution\",\n                                    id: \"solutionDetailResolution_modal\",\n                                    value: solutionDetailResolution,\n                                    onChange: (e)=>setSolutionDetailResolution(e.target.value),\n                                    rows: 3,\n                                    placeholder: \"D\\xe9tails sur la r\\xe9solution...\",\n                                    className: \"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1813,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1806,\n                            columnNumber: 25\n                        }, this),\n                        solutionDateError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyFormError, {\n                            message: solutionDateError\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1826,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        setIsSolutionModalOpen(false);\n                                        setCurrentBlocageForSolution(null);\n                                        setSolutionDateError(\"\");\n                                    },\n                                    className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1829,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyButton, {\n                                    type: \"submit\",\n                                    className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Valider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1840,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1828,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                    lineNumber: 1771,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                lineNumber: 1762,\n                columnNumber: 17\n            }, this),\n            casMain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_CasKMLEditor__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        cas: casMain,\n                        onKMLUpdated: handleKMLUpdated,\n                        onKMLRemoved: handleKMLRemoved,\n                        onError: handleKMLError,\n                        readOnly: !canUploadFiles\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1854,\n                        columnNumber: 21\n                    }, this),\n                    kmlError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm\",\n                            children: kmlError\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1863,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1862,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n                lineNumber: 1853,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\",\n        lineNumber: 1004,\n        columnNumber: 9\n    }, this);\n} // Add this CSS to your global styles or Tailwind config:\n /*\r\n@keyframes fade-in {\r\n  from { opacity: 0; }\r\n  to { opacity: 1; }\r\n}\r\n.animate-fade-in {\r\n  animation: fade-in 0.5s ease-in;\r\n}\r\n*/ \n_s(CasDetailsPage, \"Li//1TL8rkxdfTlIJDBNkTyiWLY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useOperationRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh,\n        _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_13__.usePermissions\n    ];\n});\n_c8 = CasDetailsPage;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"LazyLoadingSpinner\");\n$RefreshReg$(_c1, \"LazyFormError\");\n$RefreshReg$(_c2, \"LazyButton\");\n$RefreshReg$(_c3, \"LazyInput\");\n$RefreshReg$(_c4, \"LazyTextArea\");\n$RefreshReg$(_c5, \"LazyModal\");\n$RefreshReg$(_c6, \"CasMap$dynamic\");\n$RefreshReg$(_c7, \"CasMap\");\n$RefreshReg$(_c8, \"CasDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/cas/[id]/page.tsx\n"));

/***/ })

});