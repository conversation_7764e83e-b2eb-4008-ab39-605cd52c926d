import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyToken } from "@/lib/auth";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token manquant" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload || userPayload.role !== "ADMIN") {
            return NextResponse.json(
                { error: "Accès non autorisé - Admin requis" },
                { status: 403 }
            );
        }

        console.log("👀 Aperçu des données avant réinitialisation...");

        // Statistiques actuelles
        const currentStats = {
            cas: {
                total: await prisma.cas.count(),
                regularises: await prisma.cas.count({
                    where: { regularisation: true }
                }),
                nonRegularises: await prisma.cas.count({
                    where: { regularisation: false }
                })
            },
            blocages: {
                total: await prisma.blocage.count(),
                acceptes: await prisma.blocage.count({
                    where: { resolution: "ACCEPTE" }
                }),
                ajournes: await prisma.blocage.count({
                    where: { resolution: "AJOURNE" }
                }),
                rejetes: await prisma.blocage.count({
                    where: { resolution: "REJETE" }
                }),
                attente: await prisma.blocage.count({
                    where: { resolution: "ATTENTE" }
                })
            }
        };

        // Calcul de l'impact
        const impact = {
            casAResetterRegularisation: currentStats.cas.regularises,
            blocagesAResetterResolution: currentStats.blocages.total - currentStats.blocages.attente,
            pourcentageCasRegularises: currentStats.cas.total > 0 
                ? Math.round((currentStats.cas.regularises / currentStats.cas.total) * 100) 
                : 0,
            pourcentageBlocagesNonAttente: currentStats.blocages.total > 0 
                ? Math.round(((currentStats.blocages.total - currentStats.blocages.attente) / currentStats.blocages.total) * 100) 
                : 0
        };

        // Aperçu des changements
        const preview = {
            avant: {
                cas: {
                    regularises: currentStats.cas.regularises,
                    nonRegularises: currentStats.cas.nonRegularises
                },
                blocages: {
                    acceptes: currentStats.blocages.acceptes,
                    ajournes: currentStats.blocages.ajournes,
                    rejetes: currentStats.blocages.rejetes,
                    attente: currentStats.blocages.attente
                }
            },
            apres: {
                cas: {
                    regularises: 0, // Tous seront à false
                    nonRegularises: currentStats.cas.total // Tous seront à false
                },
                blocages: {
                    acceptes: 0, // Tous seront en ATTENTE
                    ajournes: 0, // Tous seront en ATTENTE
                    rejetes: 0, // Tous seront en ATTENTE
                    attente: currentStats.blocages.total // Tous seront en ATTENTE
                }
            }
        };

        return NextResponse.json({
            success: true,
            message: "Aperçu de la réinitialisation des données",
            currentStats,
            impact: {
                casAffectes: impact.casAResetterRegularisation,
                blocagesAffectes: impact.blocagesAResetterResolution,
                pourcentageCasRegularises: impact.pourcentageCasRegularises,
                pourcentageBlocagesNonAttente: impact.pourcentageBlocagesNonAttente
            },
            preview,
            warnings: [
                "⚠️ Cette action est IRRÉVERSIBLE",
                "⚠️ Tous les cas régularisés seront marqués comme non régularisés",
                "⚠️ Toutes les résolutions de blocage seront remises en attente",
                "⚠️ Cette action affectera les statistiques et rapports",
                "⚠️ Assurez-vous d'avoir une sauvegarde de la base de données"
            ],
            recommendations: [
                "💾 Effectuez une sauvegarde complète avant de procéder",
                "📊 Exportez les statistiques actuelles si nécessaire",
                "👥 Informez les utilisateurs de cette maintenance",
                "🕐 Effectuez cette opération pendant une période de faible activité"
            ],
            actionRequired: {
                endpoint: "/api/admin/reset-data",
                method: "POST",
                confirmation: "Pour procéder, envoyez une requête POST à l'endpoint ci-dessus"
            },
            timestamp: new Date().toISOString()
        });

    } catch (error: any) {
        console.error('❌ Erreur lors de l\'aperçu:', error);
        return NextResponse.json({
            success: false,
            error: "Erreur lors de la génération de l'aperçu",
            details: error.message
        }, { status: 500 });
    }
}
