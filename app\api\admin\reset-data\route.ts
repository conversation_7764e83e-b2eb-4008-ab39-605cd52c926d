import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyToken } from "@/lib/auth";
import { cookies } from "next/headers";

export async function POST(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token manquant" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload || userPayload.role !== "ADMIN") {
            return NextResponse.json(
                { error: "Accès non autorisé - Admin requis" },
                { status: 403 }
            );
        }

        console.log("🔄 Début de la réinitialisation des données...");
        const startTime = performance.now();

        // Étape 1: Compter les données avant réinitialisation
        const beforeStats = {
            totalCas: await prisma.cas.count(),
            casRegularises: await prisma.cas.count({
                where: { regularisation: true }
            }),
            totalBlocages: await prisma.blocage.count(),
            blocagesAcceptes: await prisma.blocage.count({
                where: { resolution: "ACCEPTE" }
            }),
            blocagesAjournes: await prisma.blocage.count({
                where: { resolution: "AJOURNE" }
            }),
            blocagesRejetes: await prisma.blocage.count({
                where: { resolution: "REJETE" }
            }),
            blocagesAttente: await prisma.blocage.count({
                where: { resolution: "ATTENTE" }
            })
        };

        console.log("📊 Statistiques avant réinitialisation:", beforeStats);

        // Étape 2: Réinitialiser toutes les résolutions de blocage à "ATTENTE"
        console.log("🔄 Réinitialisation des résolutions de blocage...");
        const blocageUpdateResult = await prisma.blocage.updateMany({
            data: {
                resolution: "ATTENTE"
            }
        });

        console.log(`✅ ${blocageUpdateResult.count} résolutions de blocage mises à jour vers ATTENTE`);

        // Étape 3: Réinitialiser toutes les régularisations de cas à false
        console.log("🔄 Réinitialisation des régularisations de cas...");
        const casUpdateResult = await prisma.cas.updateMany({
            data: {
                regularisation: false
            }
        });

        console.log(`✅ ${casUpdateResult.count} régularisations de cas mises à jour vers false`);

        // Étape 4: Vérifier les résultats après réinitialisation
        const afterStats = {
            totalCas: await prisma.cas.count(),
            casRegularises: await prisma.cas.count({
                where: { regularisation: true }
            }),
            totalBlocages: await prisma.blocage.count(),
            blocagesAcceptes: await prisma.blocage.count({
                where: { resolution: "ACCEPTE" }
            }),
            blocagesAjournes: await prisma.blocage.count({
                where: { resolution: "AJOURNE" }
            }),
            blocagesRejetes: await prisma.blocage.count({
                where: { resolution: "REJETE" }
            }),
            blocagesAttente: await prisma.blocage.count({
                where: { resolution: "ATTENTE" }
            })
        };

        console.log("📊 Statistiques après réinitialisation:", afterStats);

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        // Vérifications de cohérence
        const verifications = {
            allCasNonRegularises: afterStats.casRegularises === 0,
            allBlocagesEnAttente: afterStats.blocagesAttente === afterStats.totalBlocages,
            aucunBlocageAccepte: afterStats.blocagesAcceptes === 0,
            aucunBlocageAjourne: afterStats.blocagesAjournes === 0,
            aucunBlocageRejete: afterStats.blocagesRejetes === 0
        };

        const allVerificationsPassed = Object.values(verifications).every(v => v === true);

        return NextResponse.json({
            success: true,
            message: "Réinitialisation des données terminée avec succès",
            allVerificationsPassed,
            operations: {
                blocagesUpdated: blocageUpdateResult.count,
                casUpdated: casUpdateResult.count
            },
            statistics: {
                before: beforeStats,
                after: afterStats
            },
            verifications: {
                allCasNonRegularises: verifications.allCasNonRegularises ? "✅ Tous les cas sont non régularisés" : "❌ Certains cas restent régularisés",
                allBlocagesEnAttente: verifications.allBlocagesEnAttente ? "✅ Tous les blocages sont en attente" : "❌ Certains blocages ne sont pas en attente",
                aucunBlocageAccepte: verifications.aucunBlocageAccepte ? "✅ Aucun blocage accepté" : "❌ Des blocages restent acceptés",
                aucunBlocageAjourne: verifications.aucunBlocageAjourne ? "✅ Aucun blocage ajourné" : "❌ Des blocages restent ajournés",
                aucunBlocageRejete: verifications.aucunBlocageRejete ? "✅ Aucun blocage rejeté" : "❌ Des blocages restent rejetés"
            },
            performance: {
                duration,
                timestamp: new Date().toISOString()
            },
            summary: {
                message: allVerificationsPassed 
                    ? "🎉 Réinitialisation réussie ! Toutes les données ont été correctement réinitialisées."
                    : "⚠️ Réinitialisation terminée mais avec des anomalies. Vérifiez les détails.",
                casAffectes: casUpdateResult.count,
                blocagesAffectes: blocageUpdateResult.count,
                tempsExecution: `${duration}ms`
            }
        });

    } catch (error: any) {
        console.error('❌ Erreur lors de la réinitialisation:', error);
        return NextResponse.json({
            success: false,
            error: "Erreur lors de la réinitialisation des données",
            details: error.message,
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}
