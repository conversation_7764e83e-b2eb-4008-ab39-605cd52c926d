"use client";

import { useState, useEffect } from "react";
import { Button } from "../components/Button";
import { Input } from "../components/Input";
import { TextArea } from "../components/TextArea";
import { Table } from "../components/Table";
import { Modal } from "../components/Modal";
import { FormError } from "../components/FormError";
import { fetchApi } from "@/lib/api-client";
import { useRouter, useSearchParams } from "next/navigation"; // Ajout de useSearchParams
import {
    CasStatusBadge,
    determineCasStatus,
} from "../components/CasStatusBadge";
import { ExportExcelButton } from "../components/ExportExcelButton";
import { ExportBatchButton } from "../components/ExportBatchButton";
import {
    useRegisterDataRefresh,
    useOperationRefresh,
} from "@/app/contexts/DataRefreshContext";
import {
    PlusCircleIcon, // Pour le bouton "Ajouter un Cas"
    TrashIcon, // Pour le bouton "Supprimer"
    EyeIcon, // Pour le bouton "Détails"
    DocumentTextIcon, // Pour le titre "Gestion des Dossiers"
    DocumentArrowDownIcon, // Pour le bouton "Export Excel"
} from "@heroicons/react/24/outline";
import {
    WriteAccess,
    DeleteAccess,
    UserRoleBadge,
    ReadOnlyMessage,
    PermissionButton,
} from "../components/RoleBasedAccess";
import { usePermissions } from "@/lib/hooks/usePermissions";

// Simple component for viewer read-only message
function ViewerReadOnlyMessage() {
    const { isViewer } = usePermissions();

    if (!isViewer) return null;

    return (
        <ReadOnlyMessage
            message="Vous êtes en mode lecture seule. Vous pouvez consulter les dossiers mais ne pouvez pas les modifier."
            className="mb-4"
        />
    );
}

interface Encrage {
    id: string;
    nom: string;
    problematiques?: Problematique[]; // Optional, as we might fetch them separately or they might be nested
}

interface Problematique {
    id: string;
    problematique: string;
    encrage: {
        id: string;
        nom: string;
    };
}

// Interface pour les données de commune provenant de communes.json
interface CommuneData {
    id: string; // MODIFIÉ: Doit être string si l'ID est un CUID
    nom: string;
    wilayaId: number; // Ou string si wilayaId est aussi un CUID lié à une autre table
}

interface Cas {
    id: string;
    nom: string;
    nif: string;
    nin?: string;
    communes: CommuneData[]; // Doit être un tableau d'objets CommuneData
    superficie: number;
    regularisation: boolean;
    observation?: string | null;
    problematiqueId: string;
    userId: string;
    date_depot?: string | null;
    problematique?: Problematique;
    user?: {
        id: string;
        username: string;
        role: string;
    };
}

import { TypePersonne } from "@prisma/client"; // Assurez-vous que l'import est correct
import { Select } from "../components/Select"; //
// Si vous avez une interface pour formData, mettez-la à jour :
interface CasFormData {
    nom: string;
    genre: TypePersonne | "";
    nif?: string;
    nin?: string;
    superficie: string;
    observation?: string;
    problematiqueId: string;
    communeIds: string[]; // Ceci doit être un tableau d'IDs de communes (chaînes)
    date_depot?: string;
    regularisation: boolean;
    userId: string;
}
// Correction suggérée pour formatNinInput (et similaire pour formatNifInput)
// pour gérer le problème de la variable 'name' non définie.
// Vous devrez décider comment identifier si c'est un NIF ou NIN.
// Une solution est de créer deux fonctions distinctes ou de passer un type.
function formatGenericNumericInput(value: string, type: "nif" | "nin"): string {
    const maxLength = type === "nin" ? 20 : 15;
    const rawDigits = value.replace(/\D/g, "").substring(0, maxLength);
    if (!rawDigits) return "";

    let resultFormatted = "";
    if (type === "nin") {
        for (let j = 0; j < rawDigits.length; j++) {
            resultFormatted += rawDigits[j];
            if ((j + 1) % 3 === 0 && j < 17 && j + 1 < rawDigits.length) {
                resultFormatted += ".";
            } else if (j + 1 === 18 && j + 1 < rawDigits.length) {
                resultFormatted += ".";
            }
        }
    } else {
        // nif
        for (let i = 0; i < rawDigits.length; i++) {
            resultFormatted += rawDigits[i];
            if ((i + 1) % 3 === 0 && i + 1 < rawDigits.length) {
                // Éviter le point final pour NIF
                resultFormatted += ".";
            }
        }
    }
    return resultFormatted;
}
export default function CasPage() {
    const router = useRouter();
    const searchParams = useSearchParams(); // Récupérer les searchParams
    const problematiqueIdFromUrl = searchParams.get("problematiqueId"); // Extraire problematiqueId
    const encrageIdFromUrl = searchParams.get("encrageId"); // Extraire encrageId

    // Hooks pour la gestion des rafraîchissements
    const { afterCreate, afterUpdate, afterDelete } = useOperationRefresh();

    // Hook pour les permissions
    const { isAdmin, isViewer } = usePermissions();

    const [cas, setCas] = useState<Cas[]>([]);
    const [problematiques, setProblematiques] = useState<Problematique[]>([]);
    const [encrages, setEncrages] = useState<Encrage[]>([]);
    const [allCommunesData, setAllCommunesData] = useState<CommuneData[]>([]); // Pour stocker les communes de communes.json
    const [currentEncrageName, setCurrentEncrageName] = useState<string>(""); // NOUVELLE LIGNE: État pour le nom de l'encrage actuel
    const [availableCommunesForForm, setAvailableCommunesForForm] = useState<
        CommuneData[]
    >([]); // Communes filtrées pour le formulaire

    const [selectedEncrageId, setSelectedEncrageId] = useState<string>("");
    const [filteredProblematiques, setFilteredProblematiques] = useState<
        Problematique[]
    >([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [currentCas, setCurrentCas] = useState<Cas | null>(null);
    const [currentUser, setCurrentUser] = useState<{
        id: string;
        username: string;
        role: string;
        wilayaId?: number; // Assurez-vous que cela est inclus si nécessaire
    } | null>(null);

    const [formData, setFormData] = useState<CasFormData>({
        nom: "",
        genre: "",
        nif: "",
        nin: "",
        superficie: "",
        observation: "",
        problematiqueId: "",
        communeIds: [], // Initialisé comme un tableau vide
        date_depot: "",
        regularisation: false,
        userId: "",
    });
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [casToDelete, setCasToDelete] = useState<Cas | null>(null);
    function openModal() {
        setIsModalOpen(true);
    }

    function closeModal() {
        setIsModalOpen(false);
        setIsEditing(false);
        setCurrentCas(null);
        // Réinitialiser formData aux valeurs par défaut
        setFormData({
            nom: "",
            genre: "",
            nif: "",
            nin: "",
            superficie: "",
            observation: "",
            problematiqueId:
                selectedEncrageId && filteredProblematiques.length > 0
                    ? formData.problematiqueId
                    : "",
            communeIds: [], // Réinitialiser communeIds
            date_depot: "",
            regularisation: false,
            userId: currentUser?.id || "",
        });
        setError(""); // Effacer les erreurs précédentes lors de la fermeture de la modale
    }

    const [error, setError] = useState("");
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        // Charger les données des communes depuis l'API
        async function loadCommunesData() {
            try {
                // const response = await fetch("/data/communes.json"); // ANCIENNE MÉTHODE
                // if (!response.ok) {
                //     throw new Error(`HTTP error! status: ${response.status}`);
                // }
                // const data: CommuneData[] = await response.json();
                const data = await fetchApi<CommuneData[]>("/api/communes"); // NOUVELLE MÉTHODE
                setAllCommunesData(data || []); // fetchApi peut retourner null
            } catch (error) {
                console.error("Error fetching communes from API:", error); // Message de log mis à jour
                setError("Erreur lors du chargement des communes.");
            }
        }
        loadCommunesData();
    }, []);

    useEffect(() => {
        loadCas(problematiqueIdFromUrl, encrageIdFromUrl);
        loadProblematiques();
        loadEncrages();
    }, [problematiqueIdFromUrl, encrageIdFromUrl]);

    // Enregistrer les callbacks de rafraîchissement
    useRegisterDataRefresh(
        "cas-list",
        () => loadCas(problematiqueIdFromUrl, encrageIdFromUrl),
        [problematiqueIdFromUrl, encrageIdFromUrl]
    );
    useRegisterDataRefresh("encrages-list", loadEncrages, []);
    useRegisterDataRefresh("problematiques-list", loadProblematiques, []);

    // NOUVEAU BLOC: useEffect pour définir le nom de l'encrage actuel
    useEffect(() => {
        if (encrageIdFromUrl && encrages.length > 0) {
            const foundEncrage = encrages.find(
                (enc) => enc.id === encrageIdFromUrl
            );
            if (foundEncrage) {
                setCurrentEncrageName(foundEncrage.nom);
            } else {
                setCurrentEncrageName(""); // Réinitialiser si non trouvé
            }
        } else {
            setCurrentEncrageName(""); // Réinitialiser si pas d'ID ou pas d'encrages chargés
        }
    }, [encrageIdFromUrl, encrages]);

    // Filtrer les communes disponibles pour le formulaire en fonction du rôle et wilayaId de l'utilisateur
    useEffect(() => {
        if (currentUser && allCommunesData.length > 0) {
            if (currentUser.role === "ADMIN" || !currentUser.wilayaId) {
                setAvailableCommunesForForm(allCommunesData);
            } else {
                setAvailableCommunesForForm(
                    allCommunesData.filter(
                        (c) => c.wilayaId === currentUser.wilayaId
                    )
                );
            }
        } else {
            setAvailableCommunesForForm([]);
        }
    }, [currentUser, allCommunesData]);

    useEffect(() => {
        // AJOUTER LE CONSOLE.LOG POUR selectedEncrageId ICI
        console.log("ID de l'encrage sélectionné:", selectedEncrageId);

        let newFiltered: Problematique[];
        if (selectedEncrageId) {
            newFiltered = problematiques.filter(
                // Ajout d'une vérification pour p.encrage avant d'accéder à p.encrage.id
                (p) => p.encrage && p.encrage.id === selectedEncrageId
            );
        } else {
            newFiltered = [];
        }
        // CONSOLE.LOG EXISTANT POUR LES PROBLÉMATIQUES FILTRÉES
        console.log(
            "Problématiques filtrées pour l'encrage sélectionné:",
            newFiltered
        );
        setFilteredProblematiques(newFiltered);

        // Réinitialiser problematiqueId dans formData uniquement si la sélection actuelle
        // n'est plus valide pour le nouvel encrage sélectionné, ou si aucun encrage n'est sélectionné.
        const currentProblematiqueStillValid = newFiltered.some(
            (p) => p.id === formData.problematiqueId
        );
        if (!currentProblematiqueStillValid) {
            setFormData((prev) => ({ ...prev, problematiqueId: "" }));
        }
    }, [selectedEncrageId, problematiques, formData.problematiqueId]); // Ajout de formData.problematiqueId aux dépendances

    async function loadCas(
        problematiqueId?: string | null,
        encrageId?: string | null
    ) {
        // Accepter problematiqueId et encrageId comme paramètres optionnels
        try {
            setIsLoading(true);
            let url = "/api/cas";
            const params = new URLSearchParams();
            if (problematiqueId) {
                params.append("problematiqueId", problematiqueId);
            }
            if (encrageId) {
                params.append("encrageId", encrageId);
            }

            // Add pagination parameters
            params.append("page", page.toString());
            params.append("pageSize", pageSize.toString());

            // Add search parameters
            const searchTerms = [];
            if (searchNom) searchTerms.push(searchNom);
            if (searchCommune) searchTerms.push(searchCommune);
            if (searchNifNin) searchTerms.push(searchNifNin);

            if (searchTerms.length > 0) {
                params.append("search", searchTerms.join(" "));
            }

            // Add wilaya filter
            if (searchWilaya) {
                params.append("wilayaId", searchWilaya);
            }

            // Add cas status filter
            if (searchStatut) {
                params.append("casStatus", searchStatut);
            }

            if (params.toString()) {
                url += `?${params.toString()}`;
            }

            const response = await fetchApi<{
                data: Cas[];
                pagination: {
                    page: number;
                    pageSize: number;
                    totalCount: number;
                    totalPages: number;
                    hasNextPage: boolean;
                    hasPrevPage: boolean;
                };
            }>(url);

            if (response) {
                setCas(response.data || []);
                // Update pagination state
                setTotalPages(response.pagination.totalPages);
                setTotalCount(response.pagination.totalCount);
            } else {
                setCas([]);
            }
        } catch (error) {
            console.error("Error fetching cas:", error);
            // Handle authentication errors
            if (
                error instanceof Error &&
                error.message.includes("Authentication")
            ) {
                window.location.href = "/login";
            } else {
                setError("Erreur lors du chargement des cas");
            }
        } finally {
            setIsLoading(false);
        }
    }

    async function loadEncrages() {
        try {
            setIsLoading(true);
            const data = await fetchApi<Encrage[]>("/api/encrages");
            setEncrages(data || []);
        } catch (error) {
            console.error("Error fetching encrages:", error);
            if (
                error instanceof Error &&
                error.message.includes("Authentication")
            ) {
                window.location.href = "/login";
            } else {
                setError("Erreur lors du chargement des encrages");
            }
        } finally {
            setIsLoading(false);
        }
    }

    async function loadProblematiques() {
        try {
            setIsLoading(true);
            // Ajouter ?context=formCreation à l'URL
            const data = await fetchApi<Problematique[]>(
                "/api/problematiques?context=formCreation"
            );
            setProblematiques(data || []);
        } catch (error) {
            console.error("Error fetching problematiques:", error);
            // Handle authentication errors
            if (
                error instanceof Error &&
                error.message.includes("Authentication")
            ) {
                window.location.href = "/login";
            } else {
                setError("Erreur lors du chargement des problématiques");
            }
        } finally {
            setIsLoading(false);
        }
    }

    async function handleDelete(cas: Cas) {
        try {
            setIsLoading(true);
            setError(""); // Clear any previous errors

            console.log("Suppression du cas:", cas.id);
            const response = await fetchApi(`/api/cas/${cas.id}`, {
                method: "DELETE",
            });
            console.log("Réponse de suppression:", response);

            // Recharger immédiatement la liste des cas
            await loadCas(problematiqueIdFromUrl, encrageIdFromUrl);

            // Déclencher le rafraîchissement via le système centralisé
            await afterDelete("cas");

            console.log("Suppression réussie et liste rechargée");
        } catch (error) {
            console.error("Erreur lors de la suppression:", error);
            setError((error as Error).message);
        } finally {
            setIsLoading(false);
        }
    }

    // Load current user from JWT token on component mount
    useEffect(() => {
        async function loadCurrentUser() {
            try {
                const response = await fetchApi<{
                    id: string;
                    username: string;
                    role: string;
                    wilayaId?: number; // Assurez-vous que l'API retourne wilayaId
                }>("/api/auth/me");
                if (response) {
                    setCurrentUser(response);
                } else {
                    // This case might occur if fetchApi returns null/undefined without throwing an error
                    // for certain non-error responses that still indicate no user.
                    // Depending on fetchApi's behavior, this might not be strictly necessary
                    // if it always throws for auth issues.
                    console.warn(
                        "No current user data received from /api/auth/me"
                    );
                    // Optionally, redirect or set error if 'null' response means unauthenticated
                    // window.location.href = "/login";
                }
            } catch (error) {
                console.error("Error fetching current user:", error);
                if (
                    error instanceof Error &&
                    error.message.includes("Authentication")
                ) {
                    window.location.href = "/login"; // Redirect on authentication failure
                } else {
                    setError(
                        "Erreur lors de la récupération des informations utilisateur."
                    ); // Set a general error for other issues
                }
            }
        }

        loadCurrentUser();
    }, []); // Dependencies: router, setError (if used for consistency, but typically stable)

    function handleEdit(casToEdit: Cas) {
        setCurrentCas(casToEdit);
        setIsEditing(true);
        let formattedDateDepot = "";
        if (casToEdit.date_depot) {
            try {
                const dateObj = new Date(casToEdit.date_depot);
                if (!isNaN(dateObj.getTime())) {
                    formattedDateDepot = dateObj.toISOString().split("T")[0];
                }
            } catch (error) {
                console.warn(
                    "Date de dépôt invalide lors de l'édition:",
                    casToEdit.date_depot
                );
            }
        }

        // CORRECTION : forcer les IDs en string
        const existingCommuneIds = Array.isArray(casToEdit.communes)
            ? casToEdit.communes.map((c) => String(c.id))
            : [];

        setFormData({
            nom: casToEdit.nom || "",
            genre: (casToEdit as any).genre || "",
            nif: casToEdit.nif || "",
            nin: casToEdit.nin || "",
            superficie: casToEdit.superficie?.toString() || "",
            observation: casToEdit.observation || "",
            problematiqueId: casToEdit.problematiqueId || "",
            communeIds: existingCommuneIds, // <-- IDs en string !
            date_depot: formattedDateDepot,
            regularisation: casToEdit.regularisation || false,
            userId: casToEdit.userId || "",
        });
        setIsModalOpen(true);
    }

    function handleAdd() {
        setCurrentCas(null);
        setFormData({
            nom: "",
            genre: "", // Requis par CasFormData
            nif: "",
            nin: "", // Optionnel, initialisé
            superficie: "", // Initialisé comme string
            regularisation: false,
            observation: "",
            problematiqueId: "",
            userId: currentUser?.id || "",
            communeIds: [], // Requis par CasFormData
            date_depot: "", // Optionnel, initialisé
        });
        setSelectedEncrageId("");
        setIsEditing(false);
        openModal();
    }

    // MODIFICATION 1: Définition de handleCommuneChange
    // Cette fonction remplace celle qui se trouvait aux lignes 551-583
    function handleCommuneChange(communeId: string, checked: boolean) {
        setFormData((prev) => {
            const currentCommuneIds = prev.communeIds || [];
            if (checked) {
                if (!currentCommuneIds.includes(communeId)) {
                    return {
                        ...prev,
                        communeIds: [...currentCommuneIds, String(communeId)], // <-- S'assurer que c'est une string
                    };
                }
            } else {
                return {
                    ...prev,
                    communeIds: currentCommuneIds.filter(
                        (id) => String(id) !== String(communeId)
                    ),
                };
            }
            return prev;
        });
    }

    const columns = [
        {
            header: "Nom",
            accessorKey: (row: Cas) => row.nom,
            className:
                "max-w-[100px] w-28 truncate whitespace-nowrap overflow-hidden text-ellipsis",
            cell: (row: Cas) => (
                <span
                    className="truncate max-w-[100px] whitespace-nowrap overflow-hidden text-ellipsis cursor-help"
                    title={row.nom}
                >
                    {row.nom}
                </span>
            ),
        },
        {
            header: "NIF/NIN",
            accessorKey: (row: Cas) => row.nif || row.nin || "N/A",
            className:
                "max-w-[110px] w-32 truncate whitespace-nowrap overflow-hidden text-ellipsis",
        },
        {
            header: "Commune(s)",
            accessorKey: (row: Cas) =>
                row.communes && row.communes.length > 0
                    ? row.communes.map((c) => c.nom).join(", ")
                    : "N/A",
            className:
                "max-w-[110px] w-32 truncate whitespace-nowrap overflow-hidden text-ellipsis",
            cell: (row: Cas) => {
                const communes =
                    row.communes && row.communes.length > 0
                        ? row.communes.map((c) => c.nom).join(", ")
                        : "N/A";
                return (
                    <span
                        className="truncate max-w-[110px] whitespace-nowrap overflow-hidden text-ellipsis cursor-help"
                        title={communes}
                    >
                        {communes}
                    </span>
                );
            },
        },
        {
            header: "Superficie",
            accessorKey: (row: Cas) => `${row.superficie} Ha`,
            className:
                "max-w-[80px] w-20 text-right truncate whitespace-nowrap overflow-hidden text-ellipsis",
        },
        {
            header: "Statut",
            accessorKey: (row: Cas) => {
                const status = determineCasStatus((row as any).blocage || []);
                return status === "REGULARISE"
                    ? "Régularisé"
                    : status === "AJOURNE"
                    ? "Ajourné"
                    : "Non examiné";
            },
            className:
                "max-w-[120px] w-28 truncate whitespace-nowrap overflow-hidden text-ellipsis text-center",
            cell: (row: Cas) => {
                const status = determineCasStatus((row as any).blocage || []);
                return (
                    <div className="flex items-center justify-center px-2 py-1">
                        <CasStatusBadge status={status} />
                    </div>
                );
            },
        },
        {
            header: "Problématique",
            accessorKey: (row: Cas) =>
                row.problematique?.problematique || "N/A",
            className:
                "max-w-[110px] w-32 truncate whitespace-nowrap overflow-hidden text-ellipsis",
            cell: (row: Cas) => (
                <span
                    className="truncate max-w-[110px] whitespace-nowrap overflow-hidden text-ellipsis cursor-help"
                    title={row.problematique?.problematique || ""}
                >
                    {row.problematique?.problematique || "N/A"}
                </span>
            ),
        },
    ];
    function handleChange(
        e: React.ChangeEvent<
            HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
        >
    ) {
        const { name, value, type } = e.target;

        if (type === "checkbox") {
            const { checked } = e.target as HTMLInputElement;
            setFormData((prev) => ({ ...prev, [name]: checked }));
        } else if (name === "communeIds") {
            // Gestion spécifique pour la sélection multiple des communes
            const selectedOptions = (e.target as HTMLSelectElement)
                .selectedOptions;
            const ids = Array.from(selectedOptions).map(
                (option) => option.value
            );
            setFormData((prev) => ({ ...prev, communeIds: ids }));
        } else if (name === "nif") {
            setFormData((prev) => ({
                ...prev,
                nif: formatGenericNumericInput(value, "nif"),
            }));
        } else if (name === "nin") {
            setFormData((prev) => ({
                ...prev,
                nin: formatGenericNumericInput(value, "nin"),
            }));
        } else {
            setFormData((prev) => ({ ...prev, [name]: value }));
        }
    }

    // function handleEdit(casToEdit: Cas) {
    //     setCurrentCas(casToEdit);
    //     setIsEditing(true); // Mettre à jour l'état d'édition
    //     setFormData({
    //         nom: casToEdit.nom,
    //         genre: (casToEdit as any).genre || "",
    //         nif: casToEdit.nif || "",
    //         nin: (casToEdit as any).nin || "",
    //         superficie: String(casToEdit.superficie),
    //         observation: casToEdit.observation || "",
    //         problematiqueId: casToEdit.problematiqueId,
    //         communeIds: casToEdit.communes
    //             ? casToEdit.communes.map((c) => String(c.id))
    //             : [],
    //         date_depot: (casToEdit as any).date_depot || "",
    //         communes: casToEdit.communes || [],
    //         regularisation: casToEdit.regularisation,
    //         userId: casToEdit.userId,
    //     });
    //     // Si vous filtrez les problématiques par encrage dans le modal:
    //     if (casToEdit.problematique && casToEdit.problematique.encrage) {
    //         setSelectedEncrageId(casToEdit.problematique.encrage.id);
    //     } else {
    //         setSelectedEncrageId("");
    //     }
    //     setIsModalOpen(true);
    // }

    async function handleSubmit(e: React.FormEvent) {
        e.preventDefault();
        setError("");
        setIsLoading(true);

        if (!formData.communeIds || formData.communeIds.length === 0) {
            setError("Au moins une commune doit être sélectionnée.");
            setIsLoading(false);
            return;
        }

        const superficieValue = parseFloat(formData.superficie);
        if (isNaN(superficieValue) || superficieValue <= 0) {
            setError("La superficie doit être un nombre positif valide.");
            setIsLoading(false);
            return;
        }

        const dataToSend = {
            nom: formData.nom,
            genre: formData.genre,
            nif: formData.nif || null,
            nin: formData.nin || null,
            superficie: superficieValue,
            observation: formData.observation || null,
            problematiqueId: formData.problematiqueId,
            date_depot: formData.date_depot
                ? new Date(formData.date_depot).toISOString()
                : null,
            communes: formData.communeIds
                .map((id) => {
                    const commune = allCommunesData.find((c) => c.id === id);
                    return commune
                        ? { nom: commune.nom, wilayaId: commune.wilayaId }
                        : null;
                })
                .filter(Boolean),
        };

        console.log("Données envoyées à /api/cas:", dataToSend);

        try {
            if (isEditing && currentCas) {
                await fetchApi(`/api/cas/${currentCas.id}`, {
                    method: "PUT",
                    body: dataToSend,
                });
            } else {
                await fetchApi("/api/cas", {
                    method: "POST",
                    body: {
                        ...dataToSend,
                        communeIds: formData.communeIds.map(String), // <-- Forcer string[]
                    },
                });
            }
            closeModal();

            // Déclencher le rafraîchissement via le système centralisé
            if (isEditing) {
                await afterUpdate("cas");
            } else {
                await afterCreate("cas");
            }
        } catch (err: any) {
            console.error("Erreur lors de la soumission du formulaire:", err);
            setError(err.message || "Une erreur est survenue.");
        } finally {
            setIsLoading(false);
        }
    }
    const [searchNom, setSearchNom] = useState("");
    const [searchCommune, setSearchCommune] = useState("");
    const [searchNifNin, setSearchNifNin] = useState("");
    const [searchWilaya, setSearchWilaya] = useState("");
    const [searchStatut, setSearchStatut] = useState("");
    const [page, setPage] = useState(1);

    // Liste des 58 wilayas d'Algérie (DSA)
    const wilayasList = [
        { id: 1, nom: "DSA Adrar" },
        { id: 2, nom: "DSA Chlef" },
        { id: 3, nom: "DSA Laghouat" },
        { id: 4, nom: "DSA Oum El Bouaghi" },
        { id: 5, nom: "DSA Batna" },
        { id: 6, nom: "DSA Béjaïa" },
        { id: 7, nom: "DSA Biskra" },
        { id: 8, nom: "DSA Béchar" },
        { id: 9, nom: "DSA Blida" },
        { id: 10, nom: "DSA Bouira" },
        { id: 11, nom: "DSA Tamanrasset" },
        { id: 12, nom: "DSA Tébessa" },
        { id: 13, nom: "DSA Tlemcen" },
        { id: 14, nom: "DSA Tiaret" },
        { id: 15, nom: "DSA Tizi Ouzou" },
        { id: 16, nom: "DSA Alger" },
        { id: 17, nom: "DSA Djelfa" },
        { id: 18, nom: "DSA Jijel" },
        { id: 19, nom: "DSA Sétif" },
        { id: 20, nom: "DSA Saïda" },
        { id: 21, nom: "DSA Skikda" },
        { id: 22, nom: "DSA Sidi Bel Abbès" },
        { id: 23, nom: "DSA Annaba" },
        { id: 24, nom: "DSA Guelma" },
        { id: 25, nom: "DSA Constantine" },
        { id: 26, nom: "DSA Médéa" },
        { id: 27, nom: "DSA Mostaganem" },
        { id: 28, nom: "DSA M'Sila" },
        { id: 29, nom: "DSA Mascara" },
        { id: 30, nom: "DSA Ouargla" },
        { id: 31, nom: "DSA Oran" },
        { id: 32, nom: "DSA El Bayadh" },
        { id: 33, nom: "DSA Illizi" },
        { id: 34, nom: "DSA Bordj Bou Arréridj" },
        { id: 35, nom: "DSA Boumerdès" },
        { id: 36, nom: "DSA El Tarf" },
        { id: 37, nom: "DSA Tindouf" },
        { id: 38, nom: "DSA Tissemsilt" },
        { id: 39, nom: "DSA El Oued" },
        { id: 40, nom: "DSA Khenchela" },
        { id: 41, nom: "DSA Souk Ahras" },
        { id: 42, nom: "DSA Tipaza" },
        { id: 43, nom: "DSA Mila" },
        { id: 44, nom: "DSA Aïn Defla" },
        { id: 45, nom: "DSA Naâma" },
        { id: 46, nom: "DSA Aïn Témouchent" },
        { id: 47, nom: "DSA Ghardaïa" },
        { id: 48, nom: "DSA Relizane" },
        { id: 49, nom: "DSA Timimoun" },
        { id: 50, nom: "DSA Bordj Badji Mokhtar" },
        { id: 51, nom: "DSA Ouled Djellal" },
        { id: 52, nom: "DSA Béni Abbès" },
        { id: 53, nom: "DSA In Salah" },
        { id: 54, nom: "DSA In Guezzam" },
        { id: 55, nom: "DSA Touggourt" },
        { id: 56, nom: "DSA Djanet" },
        { id: 57, nom: "DSA El M'Ghair" },
        { id: 58, nom: "DSA El Meniaa" },
    ];
    const [pageSize, setPageSize] = useState(100); // Augmenté à 100 pour afficher plus de dossiers
    const [totalPages, setTotalPages] = useState(1);
    const [totalCount, setTotalCount] = useState(0);

    // Remove client-side filtering since we're using server-side pagination
    // const filteredCas = cas.filter((row) => {
    //     const matchesNom = row.nom
    //         .toLowerCase()
    //         .includes(searchNom.toLowerCase());
    //     const matchesCommune = row.communes.some((commune) =>
    //         commune.nom.toLowerCase().includes(searchCommune.toLowerCase())
    //     );
    //     const matchesNifNin =
    //         (row.nif &&
    //             row.nif.toLowerCase().includes(searchNifNin.toLowerCase())) ||
    //         (row.nin &&
    //             row.nin.toLowerCase().includes(searchNifNin.toLowerCase()));
    //     // Tous les champs de recherche doivent matcher (AND logique)
    //     return (
    //         (!searchNom || matchesNom) &&
    //         (!searchCommune || matchesCommune) &&
    //         (!searchNifNin || matchesNifNin)
    //     );
    // });

    // Pagination - now using server-side data
    // const totalPages = Math.max(1, Math.ceil(filteredCas.length / pageSize));
    // const paginatedCas = filteredCas.slice(
    //     (page - 1) * pageSize,
    //     page * pageSize
    // );

    // Réinitialiser la page si le filtre réduit le nombre de pages
    useEffect(() => {
        if (page > totalPages) setPage(1);
    }, [totalPages, page]);

    // Trigger data loading when page or search parameters change
    useEffect(() => {
        loadCas(problematiqueIdFromUrl, encrageIdFromUrl);
    }, [
        page,
        pageSize,
        searchNom,
        searchCommune,
        searchNifNin,
        searchWilaya,
        searchStatut,
    ]);

    return (
        <div
            className="min-h-screen bg-gray-50 transition-all duration-300"
            style={{
                paddingLeft: "var(--sidebar-width, 0.5rem)", // Utilise une variable CSS si possible, sinon adapter dynamiquement
            }}
        >
            <div className="max-w-9xl mx-auto px-1 sm:px-1 md:px-1 py-2">
                <header className="sticky top-0 z-10 bg-white shadow-md py-4 px-1 mb-3 rounded-xl flex items-center gap-4">
                    <DocumentTextIcon className="h-8 w-8 text-indigo-600" />
                    <div className="flex-1">
                        <h1 className="text-2xl md:text-3xl font-bold text-gray-800 tracking-tight">
                            Gestion des Dossiers
                        </h1>
                        <UserRoleBadge className="mt-2" />
                        {/* Affichage du nombre de dossiers filtrés */}
                        <div className="mt-2 flex items-center gap-4 text-sm">
                            <div className="flex items-center gap-2">
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    📊 {totalCount.toLocaleString()} dossier
                                    {totalCount > 1 ? "s" : ""} trouvé
                                    {totalCount > 1 ? "s" : ""}
                                </span>
                                {(searchNom ||
                                    searchCommune ||
                                    searchNifNin ||
                                    searchWilaya ||
                                    searchStatut ||
                                    problematiqueIdFromUrl ||
                                    encrageIdFromUrl) && (
                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        🔍 Filtres actifs
                                    </span>
                                )}
                            </div>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        {/* Bouton Export Excel - Limite 50k */}
                        <ExportExcelButton
                            filters={{
                                search: searchNom,
                                casStatus: searchStatut,
                                wilayaId: searchWilaya,
                                problematiqueId:
                                    problematiqueIdFromUrl || undefined,
                                encrageId: encrageIdFromUrl || undefined,
                            }}
                            totalCasCount={totalCount}
                            className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold px-2 py-2 rounded-lg shadow transition-all duration-200"
                        >
                            <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
                            <span className="hidden md:inline text-xs">
                                {totalCount > 50000
                                    ? "Export désactivé"
                                    : "Export (≤50k)"}
                            </span>
                        </ExportExcelButton>

                        {/* Bouton Export par Batch - Gros volumes */}
                        <ExportBatchButton
                            filters={{
                                search: searchNom,
                                casStatus: searchStatut,
                                wilayaId: searchWilaya,
                                problematiqueId:
                                    problematiqueIdFromUrl || undefined,
                                encrageId: encrageIdFromUrl || undefined,
                            }}
                            className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold px-2 py-2 rounded-lg shadow transition-all duration-200"
                        >
                            <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
                            <span className="hidden md:inline text-xs">
                                Batch (&gt;50k)
                            </span>
                        </ExportBatchButton>

                        <WriteAccess>
                            <PermissionButton
                                onClick={openModal}
                                requirePermission="canWrite"
                                disabledMessage="Vous n'avez pas les permissions pour ajouter des dossiers"
                                className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold px-2 py-2 rounded-lg shadow transition-all duration-200 flex items-center gap-2"
                            >
                                <PlusCircleIcon className="h-5 w-5" />
                                <span className="hidden sm:inline">
                                    Ajouter un dossier
                                </span>
                            </PermissionButton>
                        </WriteAccess>
                    </div>
                </header>

                {/* Read-only message for viewers */}
                <ViewerReadOnlyMessage />
                {/* Affichage unique de l'encrage sélectionné si présent */}
                {currentEncrageName && (
                    <div className="mb-2">
                        <span className="inline-block bg-indigo-100 text-indigo-700 px-1 py-1 rounded-full text-sm font-medium shadow">
                            Encrage : {currentEncrageName}
                        </span>
                    </div>
                )}

                {/* Filtres de recherche */}
                <div className="flex flex-row items-center gap-2 mb-2 w-full">
                    <Input
                        type="text"
                        placeholder="Recherche par nom..."
                        className="border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow"
                        value={searchNom}
                        onChange={(e) => {
                            setSearchNom(e.target.value);
                            setPage(1);
                        }}
                        onKeyDown={(e) => {
                            if (e.key === "Enter") {
                                loadCas(
                                    problematiqueIdFromUrl,
                                    encrageIdFromUrl
                                );
                            }
                        }}
                    />
                    <Input
                        type="text"
                        placeholder="Recherche par commune..."
                        className="border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow"
                        value={searchCommune}
                        onChange={(e) => {
                            setSearchCommune(e.target.value);
                            setPage(1);
                        }}
                        onKeyDown={(e) => {
                            if (e.key === "Enter") {
                                loadCas(
                                    problematiqueIdFromUrl,
                                    encrageIdFromUrl
                                );
                            }
                        }}
                    />
                    <Input
                        type="text"
                        placeholder="Recherche par NIF/NIN..."
                        className="border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow"
                        value={searchNifNin}
                        onChange={(e) => {
                            setSearchNifNin(e.target.value);
                            setPage(1);
                        }}
                        onKeyDown={(e) => {
                            if (e.key === "Enter") {
                                loadCas(
                                    problematiqueIdFromUrl,
                                    encrageIdFromUrl
                                );
                            }
                        }}
                    />
                </div>

                {/* Deuxième ligne de filtres */}
                <div className="flex flex-row items-center gap-2 mb-4 w-full">
                    {/* Filtre Wilaya - Visible uniquement pour ADMIN et VIEWER */}
                    {(isAdmin || isViewer) && (
                        <select
                            className="border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow"
                            value={searchWilaya}
                            onChange={(e) => {
                                setSearchWilaya(e.target.value);
                                setPage(1);
                            }}
                        >
                            <option value="">Toutes les wilayas (DSA)</option>
                            {wilayasList.map((wilaya) => (
                                <option
                                    key={wilaya.id}
                                    value={wilaya.id.toString()}
                                >
                                    {wilaya.nom}
                                </option>
                            ))}
                        </select>
                    )}

                    <select
                        className="border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow"
                        value={searchStatut}
                        onChange={(e) => {
                            setSearchStatut(e.target.value);
                            setPage(1);
                        }}
                    >
                        <option value="">Tous les statuts</option>
                        <option value="REGULARISE">Régularisé</option>
                        <option value="AJOURNE">Ajourné</option>
                        <option value="NON_EXAMINE">Non examiné</option>
                        <option value="REJETE">Rejeté</option>
                    </select>

                    <Button
                        onClick={() =>
                            loadCas(problematiqueIdFromUrl, encrageIdFromUrl)
                        }
                        className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
                        disabled={isLoading}
                    >
                        {isLoading ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        ) : (
                            "Rechercher"
                        )}
                    </Button>

                    <Button
                        onClick={() => {
                            setSearchNom("");
                            setSearchCommune("");
                            setSearchNifNin("");
                            // Réinitialiser le filtre wilaya seulement pour ADMIN et VIEWER
                            if (isAdmin || isViewer) {
                                setSearchWilaya("");
                            }
                            setSearchStatut("");
                            setPage(1);
                            // Recharger les données après réinitialisation
                            setTimeout(() => {
                                loadCas(
                                    problematiqueIdFromUrl,
                                    encrageIdFromUrl
                                );
                            }, 100);
                        }}
                        className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm"
                        disabled={isLoading}
                    >
                        Réinitialiser
                    </Button>
                </div>

                {/* Loading spinner */}
                {isLoading && (
                    <div className="flex justify-center items-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span className="ml-2 text-gray-600">
                            Chargement des données...
                        </span>
                    </div>
                )}

                {/* Table responsive */}
                {!isLoading && (
                    <Table
                        data={cas} // Use the 'cas' state directly
                        columns={columns}
                        showPaginationInfo={false} // Désactiver la pagination interne du composant Table
                        actions={(row: Cas) => (
                            <div className="flex items-center space-x-2 py-2">
                                <Button
                                    onClick={() =>
                                        router.push(`/dashboard/cas/${row.id}`)
                                    }
                                    className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-bold py-1 px-2 rounded-lg text-xs flex items-center shadow transition-all duration-200"
                                    title="Voir les détails du dossier"
                                >
                                    <EyeIcon className="h-4 w-4 mr-1" />
                                </Button>
                                <DeleteAccess>
                                    <PermissionButton
                                        onClick={() => {
                                            setCasToDelete(row);
                                            setIsDeleteModalOpen(true);
                                        }}
                                        requirePermission="canDelete"
                                        disabledMessage="Vous n'avez pas les permissions pour supprimer des dossiers"
                                        className="bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white font-bold py-1 px-2 rounded-lg text-xs flex items-center shadow transition-all duration-200"
                                        title="Supprimer ce dossier"
                                    >
                                        <TrashIcon className="h-4 w-4 mr-1" />
                                    </PermissionButton>
                                </DeleteAccess>
                            </div>
                        )}
                    />
                )}
                {/* Pagination améliorée et unifiée */}
                <div className="bg-white border-t border-gray-200 px-4 py-3 sm:px-6 rounded-b-xl">
                    <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                        {/* Informations sur les résultats */}
                        <div className="flex items-center gap-4 text-sm text-gray-700">
                            <span>
                                Affichage de{" "}
                                <span className="font-medium">
                                    {Math.min(
                                        (page - 1) * pageSize + 1,
                                        totalCount
                                    )}
                                </span>{" "}
                                à{" "}
                                <span className="font-medium">
                                    {Math.min(page * pageSize, totalCount)}
                                </span>{" "}
                                sur{" "}
                                <span className="font-medium">
                                    {totalCount.toLocaleString()}
                                </span>{" "}
                                résultat{totalCount > 1 ? "s" : ""}
                            </span>
                        </div>

                        {/* Contrôles de pagination */}
                        <div className="flex items-center gap-3">
                            {/* Sélecteur de taille de page */}
                            <div className="flex items-center gap-2">
                                <label
                                    htmlFor="pageSize"
                                    className="text-sm text-gray-700"
                                >
                                    Par page :
                                </label>
                                <select
                                    id="pageSize"
                                    className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                                    value={pageSize}
                                    onChange={(e) => {
                                        setPageSize(Number(e.target.value));
                                        setPage(1);
                                        loadCas(
                                            problematiqueIdFromUrl,
                                            encrageIdFromUrl
                                        );
                                    }}
                                    disabled={isLoading}
                                >
                                    {[10, 25, 50, 100, 200, 500, 1000].map(
                                        (size) => (
                                            <option key={size} value={size}>
                                                {size}
                                            </option>
                                        )
                                    )}
                                </select>
                            </div>

                            {/* Navigation des pages */}
                            <div className="flex items-center gap-1">
                                <Button
                                    onClick={() => {
                                        setPage(1);
                                        loadCas(
                                            problematiqueIdFromUrl,
                                            encrageIdFromUrl
                                        );
                                    }}
                                    disabled={page === 1 || isLoading}
                                    className="px-2 py-1 text-sm rounded-md border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="Première page"
                                >
                                    ««
                                </Button>
                                <Button
                                    onClick={() => {
                                        setPage(page - 1);
                                        loadCas(
                                            problematiqueIdFromUrl,
                                            encrageIdFromUrl
                                        );
                                    }}
                                    disabled={page === 1 || isLoading}
                                    className="px-3 py-1 text-sm rounded-md border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Précédent
                                </Button>

                                <span className="px-3 py-1 text-sm text-gray-700 bg-gray-50 border border-gray-300 rounded-md">
                                    Page {page} / {totalPages}
                                </span>

                                <Button
                                    onClick={() => {
                                        setPage(page + 1);
                                        loadCas(
                                            problematiqueIdFromUrl,
                                            encrageIdFromUrl
                                        );
                                    }}
                                    disabled={page === totalPages || isLoading}
                                    className="px-3 py-1 text-sm rounded-md border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Suivant
                                </Button>
                                <Button
                                    onClick={() => {
                                        setPage(totalPages);
                                        loadCas(
                                            problematiqueIdFromUrl,
                                            encrageIdFromUrl
                                        );
                                    }}
                                    disabled={page === totalPages || isLoading}
                                    className="px-2 py-1 text-sm rounded-md border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="Dernière page"
                                >
                                    »»
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* Modals restent inchangés */}
            <Modal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                title="Confirmer la suppression"
            >
                <div className="py-4">
                    <p>
                        Êtes-vous sûr de vouloir supprimer ce dossier ? Cette
                        action est irréversible.
                    </p>
                    <div className="flex justify-end space-x-2 mt-6">
                        <Button
                            onClick={() => setIsDeleteModalOpen(false)}
                            className="bg-gray-200 text-gray-800"
                        >
                            Annuler
                        </Button>
                        <Button
                            onClick={async () => {
                                if (casToDelete) {
                                    try {
                                        await handleDelete(casToDelete);
                                        // Fermer la modal seulement si la suppression réussit
                                        setIsDeleteModalOpen(false);
                                        setCasToDelete(null);
                                    } catch (error) {
                                        // En cas d'erreur, la modal reste ouverte
                                        console.error(
                                            "Erreur lors de la suppression:",
                                            error
                                        );
                                        // L'erreur est déjà gérée dans handleDelete
                                    }
                                }
                            }}
                            className="bg-red-600 text-white"
                            isLoading={isLoading}
                        >
                            Supprimer
                        </Button>
                    </div>
                </div>
            </Modal>
            {/* Modal d'ajout/édition reste inchangé */}
            <Modal
                isOpen={isModalOpen}
                onClose={closeModal}
                title={isEditing ? "Modifier le Dossier" : "Ajouter un Dossier"}
            >
                <form onSubmit={handleSubmit} className="space-y-6 p-1">
                    {error && <FormError message={error} />}

                    <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
                        <Input
                            id="nom"
                            label="Nom du dossier"
                            value={formData.nom}
                            onChange={(e) =>
                                setFormData({
                                    ...formData,
                                    nom: e.target.value,
                                })
                            }
                            required
                            placeholder="Nom du cas"
                        />
                        {/* <Input
                            id="nif"
                            label="NIF"
                            value={formData.nif}
                            onChange={(e) =>
                                setFormData({
                                    ...formData,
                                    nif: e.target.value,
                                })
                            }
                            required
                            placeholder="Numéro d'Identification Fiscale"
                        /> */}
                    </div>
                    {/* Champ Genre */}
                    <div>
                        <label
                            htmlFor="modal_genre"
                            className="block text-sm font-medium text-gray-700"
                        >
                            Type
                        </label>
                        <Select // Assurez-vous que le composant Select est importé et utilisé correctement
                            name="genre"
                            id="modal_genre"
                            value={formData.genre}
                            onChange={handleChange} // This function handles the state update
                            required
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                        >
                            <option value="" disabled>
                                Sélectionner un type
                            </option>
                            <option value={TypePersonne.PERSONNE_PHYSIQUE}>
                                Personne Physique
                            </option>
                            <option value={TypePersonne.PERSONNE_MORALE}>
                                Personne Morale
                            </option>
                        </Select>
                    </div>

                    {/* Champ NIF (conditionnel) */}
                    {formData.genre === TypePersonne.PERSONNE_MORALE && (
                        <div>
                            <label
                                htmlFor="modal_nif"
                                className="block text-sm font-medium text-gray-700"
                            >
                                NIF
                            </label>
                            <Input
                                id="modal_nif"
                                name="nif" // Important: name attribute for handleChange
                                value={formData.nif || ""} // Ensure controlled component
                                onChange={handleChange} // Use the main handleChange
                                required // Make it required if it's a personne morale
                                placeholder="Numéro d'Identification Fiscale"
                                className="mt-1"
                            />
                        </div>
                    )}

                    {/* Champ NIN (conditionnel) - This should be similar for PERSONNE_PHYSIQUE */}
                    {formData.genre === TypePersonne.PERSONNE_PHYSIQUE && (
                        <div>
                            <label
                                htmlFor="modal_nin"
                                className="block text-sm font-medium text-gray-700"
                            >
                                NIN
                            </label>
                            <Input
                                id="modal_nin"
                                name="nin" // Important: name attribute for handleChange
                                value={formData.nin || ""} // Ensure controlled component
                                onChange={handleChange} // Use the main handleChange
                                required // Make it required if it's a personne physique
                                placeholder="Numéro d'Identification National"
                                className="mt-1"
                            />
                        </div>
                    )}

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Commune(s)
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto border rounded p-2">
                            {availableCommunesForForm.map((commune) => (
                                <label
                                    key={commune.id}
                                    className="flex items-center space-x-2"
                                >
                                    <input
                                        type="checkbox"
                                        checked={formData.communeIds.includes(
                                            String(commune.id)
                                        )}
                                        onChange={(e) =>
                                            handleCommuneChange(
                                                String(commune.id),
                                                e.target.checked
                                            )
                                        }
                                    />
                                    <span>{commune.nom}</span>
                                </label>
                            ))}
                        </div>
                    </div>

                    {/* Champ Encrage */}
                    <div>
                        <label
                            htmlFor="encrageId"
                            className="block text-sm font-medium text-gray-700"
                        >
                            Encrage Juridique
                        </label>
                        <select
                            id="encrageId"
                            value={selectedEncrageId}
                            onChange={(e) =>
                                setSelectedEncrageId(e.target.value)
                            }
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm p-2"
                            required
                        >
                            <option value="">Sélectionner un encrage</option>
                            {encrages.map((encrage) => (
                                <option key={encrage.id} value={encrage.id}>
                                    {encrage.nom}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Champ Problématique (filtré par encrage) */}
                    <div>
                        <label
                            htmlFor="problematiqueId"
                            className="block text-sm font-medium text-gray-700"
                        >
                            Problématique Spécifique
                        </label>
                        <select
                            id="problematiqueId"
                            value={formData.problematiqueId}
                            onChange={(e) =>
                                setFormData({
                                    ...formData,
                                    problematiqueId: e.target.value,
                                })
                            }
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm p-2"
                            required
                            disabled={
                                !selectedEncrageId ||
                                filteredProblematiques.length === 0
                            }
                        >
                            <option value="">
                                {selectedEncrageId
                                    ? filteredProblematiques.length > 0
                                        ? "Sélectionner une problématique"
                                        : "Aucune problématique pour cet encrage"
                                    : "Sélectionner d'abord un encrage"}
                            </option>
                            {filteredProblematiques.map((problematique) => (
                                <option
                                    key={problematique.id}
                                    value={problematique.id}
                                >
                                    {problematique.problematique}
                                </option>
                            ))}
                        </select>
                    </div>

                    <Input
                        id="superficie"
                        label="Superficie (Ha)"
                        type="number"
                        value={formData.superficie} // Ensure this is handled as a string initially if your state is string
                        onChange={(e) =>
                            setFormData({
                                ...formData,
                                superficie: e.target.value, // Keep as string, parse on submit
                            })
                        }
                        required
                        placeholder="Ex: 120.5"
                        min="0"
                        step="any"
                    />

                    {/* NOUVEAU CHAMP: Date de dépôt du dossier */}
                    <div>
                        <Input
                            label="Date de dépôt du dossier"
                            type="date" // Important: type="date"
                            name="date_depot"
                            value={formData.date_depot || ""} // Doit être au format YYYY-MM-DD
                            onChange={handleChange}
                            placeholder="JJ/MM/AAAA" // Le placeholder n'est généralement pas affiché pour type="date"
                        />
                    </div>

                    <TextArea
                        id="observation"
                        label="Observation"
                        value={formData.observation || ""}
                        onChange={(e) =>
                            setFormData({
                                ...formData,
                                observation: e.target.value,
                            })
                        }
                        placeholder="Ajoutez des observations ici..."
                        rows={3}
                    />

                    <div className="flex justify-end space-x-3 pt-4">
                        <Button
                            variant="outline"
                            onClick={() => setIsModalOpen(false)}
                            type="button"
                        >
                            Annuler
                        </Button>
                        {currentUser?.role !== "BASIC" && (
                            <Button type="submit" isLoading={isLoading}>
                                {isEditing ? "Enregistré" : "Créer le Dossier"}
                            </Button>
                        )}
                    </div>
                </form>
            </Modal>
        </div>
    );
}
