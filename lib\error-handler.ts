/**
 * Utility functions for handling errors in the application
 */

export interface ApiError {
    message: string;
    status?: number;
    code?: string;
    details?: any;
}

/**
 * Safely handle API calls with proper error handling
 */
export async function safeApiCall<T>(
    apiCall: () => Promise<T>,
    fallbackValue?: T
): Promise<{ data: T | null; error: ApiError | null }> {
    try {
        const data = await apiCall();
        return { data, error: null };
    } catch (error) {
        const apiError = parseError(error);
        
        // Only log in development
        if (process.env.NODE_ENV === 'development') {
            console.warn('API call failed:', apiError);
        }
        
        return { 
            data: fallbackValue || null, 
            error: apiError 
        };
    }
}

/**
 * Parse different types of errors into a consistent format
 */
export function parseError(error: unknown): ApiError {
    if (error instanceof Error) {
        // Check if it's an API error with status
        const message = error.message;
        let status: number | undefined;
        
        // Extract status from common error messages
        if (message.includes('401')) {
            status = 401;
        } else if (message.includes('403')) {
            status = 403;
        } else if (message.includes('404')) {
            status = 404;
        } else if (message.includes('500')) {
            status = 500;
        }
        
        return {
            message: message,
            status,
            code: error.name
        };
    }
    
    if (typeof error === 'string') {
        return {
            message: error
        };
    }
    
    if (error && typeof error === 'object' && 'message' in error) {
        return {
            message: (error as any).message || 'Unknown error',
            status: (error as any).status,
            code: (error as any).code,
            details: error
        };
    }
    
    return {
        message: 'An unexpected error occurred',
        details: error
    };
}

/**
 * Get user-friendly error message
 */
export function getUserFriendlyErrorMessage(error: ApiError): string {
    switch (error.status) {
        case 401:
            return "Votre session a expiré. Veuillez vous reconnecter.";
        case 403:
            return "Vous n'avez pas les permissions nécessaires pour cette action.";
        case 404:
            return "La ressource demandée n'a pas été trouvée.";
        case 500:
            return "Erreur du serveur. Veuillez réessayer plus tard.";
        default:
            return error.message || "Une erreur inattendue s'est produite.";
    }
}

/**
 * Handle authentication errors
 */
export function handleAuthError(error: ApiError): void {
    if (error.status === 401) {
        // Redirect to login page
        if (typeof window !== 'undefined') {
            window.location.href = '/login';
        }
    }
}

/**
 * React hook for handling errors in components
 */
export function useErrorHandler() {
    const handleError = (error: unknown, context?: string) => {
        const apiError = parseError(error);
        const userMessage = getUserFriendlyErrorMessage(apiError);
        
        // Handle authentication errors
        handleAuthError(apiError);
        
        // Log in development
        if (process.env.NODE_ENV === 'development') {
            console.warn(`Error in ${context || 'component'}:`, apiError);
        }
        
        return {
            error: apiError,
            userMessage
        };
    };
    
    return { handleError };
}

/**
 * Wrapper for fetch API calls with better error handling
 */
export async function safeFetch<T>(
    url: string,
    options?: RequestInit
): Promise<{ data: T | null; error: ApiError | null }> {
    try {
        const response = await fetch(url, {
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            },
            ...options
        });
        
        if (!response.ok) {
            let errorMessage = `HTTP error! status: ${response.status}`;
            
            try {
                const errorText = await response.text();
                if (errorText) {
                    try {
                        const errorData = JSON.parse(errorText);
                        errorMessage = errorData?.error || errorData?.message || errorText;
                    } catch {
                        errorMessage = errorText;
                    }
                }
            } catch {
                // Use default message if we can't read the response
            }
            
            throw new Error(errorMessage);
        }
        
        if (response.status === 204) {
            return { data: null, error: null };
        }
        
        const data = await response.json();
        return { data, error: null };
        
    } catch (error) {
        const apiError = parseError(error);
        return { data: null, error: apiError };
    }
}
