type FetchOptions = {
    method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH"; // Added PATCH as an example
    body?: any;
    headers?: Record<string, string>; // Added
    credentials?: "include" | "same-origin" | "omit"; // Added
};

export async function fetchApi<T>(
    endpoint: string,
    options: FetchOptions = {}
): Promise<T> {
    const { method = "GET", body } = options;

    const response = await fetch(endpoint, {
        method,
        headers: {
            "Content-Type": "application/json",
            ...options.headers, // Allow passing custom headers
        },
        credentials: options.credentials || "include", // Include credentials (cookies) with every request
        body: body ? JSON.stringify(body) : undefined,
    });

    if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        let errorData: any = null;

        try {
            // Read the response body as text first
            const errorText = await response.text();

            if (errorText) {
                try {
                    // Try to parse the text as JSON
                    errorData = JSON.parse(errorText);
                    errorMessage =
                        errorData?.error || errorData?.message || errorText;
                } catch (parseError) {
                    // If parsing fails, use the raw text as the error message
                    errorMessage = errorText || errorMessage;
                }
            }
        } catch (readError) {
            // If we can't read the response body, use the status
            console.warn("Could not read error response body:", readError);
        }

        // Log error details for debugging (but don't throw console.error)
        if (process.env.NODE_ENV === "development") {
            console.warn("API Error Details:", {
                url: response.url,
                status: response.status,
                statusText: response.statusText,
                errorMessage,
                errorData,
            });
        }

        // Handle specific error cases
        if (response.status === 401) {
            throw new Error("Authentication required. Please log in again.");
        } else if (response.status === 403) {
            throw new Error(
                "Access denied. You don't have permission to perform this action."
            );
        } else if (response.status === 404) {
            throw new Error("Resource not found.");
        } else if (response.status >= 500) {
            throw new Error("Server error. Please try again later.");
        }

        throw new Error(errorMessage);
    }

    if (response.status === 204) {
        // No Content
        return null as T;
    }

    return response.json();
}

// Add the following apiClient export:
export const apiClient = {
    get: <T>(
        endpoint: string,
        options?: Omit<FetchOptions, "method" | "body">
    ): Promise<T> => {
        return fetchApi<T>(endpoint, { ...options, method: "GET" });
    },
    post: <T>(
        endpoint: string,
        body: any,
        options?: Omit<FetchOptions, "method" | "body">
    ): Promise<T> => {
        return fetchApi<T>(endpoint, { ...options, method: "POST", body });
    },
    put: <T>(
        endpoint: string,
        body: any,
        options?: Omit<FetchOptions, "method" | "body">
    ): Promise<T> => {
        return fetchApi<T>(endpoint, { ...options, method: "PUT", body });
    },
    delete: <T>(
        endpoint: string,
        options?: Omit<FetchOptions, "method" | "body">
    ): Promise<T> => {
        return fetchApi<T>(endpoint, { ...options, method: "DELETE" });
    },
    // You can add other HTTP methods like PATCH if needed
    // patch: <T>(endpoint: string, body: any, options?: Omit<FetchOptions, 'method' | 'body'>): Promise<T> => {
    //   return fetchApi<T>(endpoint, { ...options, method: 'PATCH', body });
    // },
};

// Types
export interface Chat {
    id: string;
    messages: Message[];
}

export interface Message {
    id: string;
    content: string;
    userId: string;
    user: {
        username: string;
    };
    createdAt: Date;
}

export interface Cas {
    id: string;
    nom: string;
    commune?: {
        id: string;
        nom: string;
    };
}

// Chat Functions
export const getChat = (casId: string): Promise<Chat> => {
    return apiClient.get(`/api/chats/${casId}`);
};

export const sendMessage = (
    casId: string,
    content: string
): Promise<Message> => {
    return apiClient.post(`/api/chats/${casId}/messages`, { content });
};

interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        pageSize: number;
        totalCount: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
    };
}

async function fetchPage(
    page: number,
    pageSize: number
): Promise<PaginatedResponse<Cas>> {
    const response = await fetch(`/api/cas?page=${page}&pageSize=${pageSize}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
        },
        credentials: "include",
    });

    if (!response.ok) {
        let errorMessage = `API request failed with status ${response.status}`;

        try {
            const errorText = await response.text();
            if (errorText) {
                try {
                    const errorData = JSON.parse(errorText);
                    errorMessage =
                        errorData?.error || errorData?.message || errorText;
                } catch {
                    errorMessage = errorText;
                }
            }
        } catch {
            // If we can't read the response, use the default message
        }

        if (process.env.NODE_ENV === "development") {
            console.warn("getCas API Error:", {
                status: response.status,
                statusText: response.statusText,
                url: response.url,
                errorMessage,
            });
        }

        throw new Error(errorMessage);
    }

    return response.json();
}

export async function getCas(): Promise<Cas[]> {
    try {
        console.log("Fetching dossiers from /api/cas...");
        const pageSize = 100; // Maximum allowed by the API
        let currentPage = 1;
        let allDossiers: Cas[] = [];
        let hasMorePages = true;
        let totalPages = 1;

        // Fetch all pages
        while (hasMorePages && currentPage <= 20) {
            // Add a safety limit of 20 pages
            console.log(`Fetching page ${currentPage}...`);
            const result = await fetchPage(currentPage, pageSize);

            if (!result.data || !Array.isArray(result.data)) {
                if (process.env.NODE_ENV === "development") {
                    console.warn(
                        "Invalid data format in page",
                        currentPage,
                        ":",
                        result
                    );
                }
                throw new Error("Invalid data format received from server");
            }

            allDossiers = [...allDossiers, ...result.data];
            totalPages = result.pagination.totalPages;
            hasMorePages =
                result.pagination.hasNextPage && currentPage < totalPages;
            currentPage++;

            // If we've fetched all pages or reached the safety limit, stop
            if (!hasMorePages || currentPage > totalPages) {
                break;
            }
        }

        console.log(
            `Fetched ${allDossiers.length} dossiers from ${
                currentPage - 1
            } pages`
        );
        return allDossiers;
    } catch (error: unknown) {
        if (process.env.NODE_ENV === "development") {
            if (error instanceof Error) {
                console.warn("Error in getCas:", {
                    message: error.message,
                    name: error.name,
                });
            } else {
                console.warn("Unknown error in getCas:", error);
            }
        }
        throw error;
    }
}
