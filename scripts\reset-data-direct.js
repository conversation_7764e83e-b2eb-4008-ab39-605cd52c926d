/**
 * Script pour réinitialiser les données directement via Prisma
 * Usage: node scripts/reset-data-direct.js
 */

const { PrismaClient } = require('@prisma/client');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

async function resetDataDirect() {
    console.log('🔄 Script de Réinitialisation des Données (Mode Direct)');
    console.log('=====================================================');
    
    const prisma = new PrismaClient();
    
    try {
        console.log('');
        console.log('Cette opération va :');
        console.log('• Réinitialiser toutes les résolutions de blocage à "ATTENTE"');
        console.log('• Réinitialiser toutes les régularisations de cas à "false"');
        console.log('');
        console.log('⚠️  ATTENTION: Cette action est IRRÉVERSIBLE !');
        console.log('');

        // Demander confirmation
        const confirmation = await new Promise((resolve) => {
            rl.question('Êtes-vous sûr de vouloir continuer ? (tapez "OUI" pour confirmer): ', resolve);
        });

        if (confirmation !== 'OUI') {
            console.log('❌ Opération annulée.');
            rl.close();
            return;
        }

        console.log('');
        console.log('🔍 Analyse des données actuelles...');

        // Statistiques avant
        const beforeStats = {
            totalCas: await prisma.cas.count(),
            casRegularises: await prisma.cas.count({
                where: { regularisation: true }
            }),
            totalBlocages: await prisma.blocage.count(),
            blocagesAcceptes: await prisma.blocage.count({
                where: { resolution: "ACCEPTE" }
            }),
            blocagesAjournes: await prisma.blocage.count({
                where: { resolution: "AJOURNE" }
            }),
            blocagesRejetes: await prisma.blocage.count({
                where: { resolution: "REJETE" }
            }),
            blocagesAttente: await prisma.blocage.count({
                where: { resolution: "ATTENTE" }
            })
        };

        console.log('📊 Statistiques actuelles:');
        console.log(`• Total cas: ${beforeStats.totalCas}`);
        console.log(`• Cas régularisés: ${beforeStats.casRegularises}`);
        console.log(`• Total blocages: ${beforeStats.totalBlocages}`);
        console.log(`• Blocages acceptés: ${beforeStats.blocagesAcceptes}`);
        console.log(`• Blocages ajournés: ${beforeStats.blocagesAjournes}`);
        console.log(`• Blocages rejetés: ${beforeStats.blocagesRejetes}`);
        console.log(`• Blocages en attente: ${beforeStats.blocagesAttente}`);
        console.log('');

        const impact = {
            casAAffecter: beforeStats.casRegularises,
            blocagesAAffecter: beforeStats.totalBlocages - beforeStats.blocagesAttente
        };

        console.log(`📈 Impact:`);
        console.log(`• ${impact.casAAffecter} cas seront affectés`);
        console.log(`• ${impact.blocagesAAffecter} blocages seront affectés`);
        console.log('');

        // Confirmation finale
        const finalConfirmation = await new Promise((resolve) => {
            rl.question('Procéder à la réinitialisation ? (tapez "CONFIRMER" pour exécuter): ', resolve);
        });

        if (finalConfirmation !== 'CONFIRMER') {
            console.log('❌ Opération annulée.');
            rl.close();
            return;
        }

        console.log('');
        console.log('🚀 Exécution de la réinitialisation...');
        const startTime = Date.now();

        // Réinitialiser les blocages
        console.log('🔄 Réinitialisation des résolutions de blocage...');
        const blocageResult = await prisma.blocage.updateMany({
            data: {
                resolution: "ATTENTE"
            }
        });
        console.log(`✅ ${blocageResult.count} blocages mis à jour`);

        // Réinitialiser les cas
        console.log('🔄 Réinitialisation des régularisations de cas...');
        const casResult = await prisma.cas.updateMany({
            data: {
                regularisation: false
            }
        });
        console.log(`✅ ${casResult.count} cas mis à jour`);

        const endTime = Date.now();
        const duration = endTime - startTime;

        // Vérifications après
        console.log('🔍 Vérification des résultats...');
        const afterStats = {
            casRegularises: await prisma.cas.count({
                where: { regularisation: true }
            }),
            blocagesNonAttente: await prisma.blocage.count({
                where: { 
                    resolution: { 
                        not: "ATTENTE" 
                    } 
                }
            }),
            blocagesAttente: await prisma.blocage.count({
                where: { resolution: "ATTENTE" }
            }),
            totalBlocages: await prisma.blocage.count()
        };

        console.log('');
        console.log('✅ Réinitialisation terminée !');
        console.log('');
        console.log('📊 Résultats:');
        console.log(`• ${casResult.count} cas mis à jour`);
        console.log(`• ${blocageResult.count} blocages mis à jour`);
        console.log(`• Temps d'exécution: ${duration}ms`);
        console.log('');
        console.log('🔍 Vérifications finales:');
        console.log(`• Cas régularisés restants: ${afterStats.casRegularises} ${afterStats.casRegularises === 0 ? '✅' : '❌'}`);
        console.log(`• Blocages non-attente restants: ${afterStats.blocagesNonAttente} ${afterStats.blocagesNonAttente === 0 ? '✅' : '❌'}`);
        console.log(`• Blocages en attente: ${afterStats.blocagesAttente}/${afterStats.totalBlocages} ${afterStats.blocagesAttente === afterStats.totalBlocages ? '✅' : '❌'}`);
        
        console.log('');
        if (afterStats.casRegularises === 0 && afterStats.blocagesNonAttente === 0) {
            console.log('🎉 Réinitialisation réussie ! Toutes les données ont été correctement réinitialisées.');
            console.log('');
            console.log('📋 Résumé:');
            console.log(`• Tous les ${beforeStats.totalCas} cas ont regularisation = false`);
            console.log(`• Tous les ${beforeStats.totalBlocages} blocages ont resolution = "ATTENTE"`);
        } else {
            console.log('⚠️ Réinitialisation terminée mais avec des anomalies. Vérifiez les données.');
        }

    } catch (error) {
        console.error('❌ Erreur lors de la réinitialisation:', error.message);
        console.error('Stack trace:', error.stack);
    } finally {
        await prisma.$disconnect();
        rl.close();
    }
}

// Exécuter le script
console.log('🚀 Démarrage du script de réinitialisation...');
resetDataDirect().catch((error) => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
});
