/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/statistiques/page";
exports.ids = ["app/dashboard/statistiques/page"];
exports.modules = {

/***/ "(rsc)/./app/components/Footer.tsx":
/*!***********************************!*\
  !*** ./app/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ Footer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Footer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\Footer.tsx",
"Footer",
);

/***/ }),

/***/ "(rsc)/./app/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./app/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navigation: () => (/* binding */ Navigation)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Navigation = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\Navigation.tsx",
"Navigation",
);

/***/ }),

/***/ "(rsc)/./app/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./app/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Sidebar: () => (/* binding */ Sidebar)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Sidebar = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\Sidebar.tsx",
"Sidebar",
);

/***/ }),

/***/ "(rsc)/./app/components/SidebarContext.tsx":
/*!*******************************************!*\
  !*** ./app/components/SidebarContext.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),
/* harmony export */   useSidebar: () => (/* binding */ useSidebar)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useSidebar = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\SidebarContext.tsx",
"useSidebar",
);const SidebarProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\SidebarContext.tsx",
"SidebarProvider",
);

/***/ }),

/***/ "(rsc)/./app/contexts/DataRefreshContext.tsx":
/*!*********************************************!*\
  !*** ./app/contexts/DataRefreshContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DataRefreshProvider: () => (/* binding */ DataRefreshProvider),
/* harmony export */   useDataRefreshContext: () => (/* binding */ useDataRefreshContext),
/* harmony export */   useOperationRefresh: () => (/* binding */ useOperationRefresh),
/* harmony export */   useRegisterDataRefresh: () => (/* binding */ useRegisterDataRefresh)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const DataRefreshProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call DataRefreshProvider() from the server but DataRefreshProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\contexts\\DataRefreshContext.tsx",
"DataRefreshProvider",
);const useDataRefreshContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useDataRefreshContext() from the server but useDataRefreshContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\contexts\\DataRefreshContext.tsx",
"useDataRefreshContext",
);const useRegisterDataRefresh = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useRegisterDataRefresh() from the server but useRegisterDataRefresh is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\contexts\\DataRefreshContext.tsx",
"useRegisterDataRefresh",
);const useOperationRefresh = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useOperationRefresh() from the server but useOperationRefresh is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\contexts\\DataRefreshContext.tsx",
"useOperationRefresh",
);

/***/ }),

/***/ "(rsc)/./app/dashboard/MainContentClient.tsx":
/*!*********************************************!*\
  !*** ./app/dashboard/MainContentClient.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\MainContentClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\MainContentClient.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _app_components_SidebarContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/components/SidebarContext */ \"(rsc)/./app/components/SidebarContext.tsx\");\n/* harmony import */ var _app_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/components/Sidebar */ \"(rsc)/./app/components/Sidebar.tsx\");\n/* harmony import */ var _MainContentClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MainContentClient */ \"(rsc)/./app/dashboard/MainContentClient.tsx\");\n\n\n\n\n\n\nasync function DashboardLayout({ children }) {\n    const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.getUser)();\n    if (!user) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/login\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.SidebarProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen bg-gradient-to-br from-gray-50 via-indigo-50 to-purple-100 font-inter  \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                    className: \"transition-all duration-300 shadow-2xl  \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {\n                        user: user\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1 min-w-0 px-1 md:px-1 py-1 md:py-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-3xl shadow-2xl p-1 md:p-1 border border-gray-100 min-h-[80vh]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MainContentClient__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/statistiques/page.tsx":
/*!*********************************************!*\
  !*** ./app/dashboard/statistiques/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\statistiques\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6b10df813244\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjZiMTBkZjgxMzI0NFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Navigation */ \"(rsc)/./app/components/Navigation.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/Footer */ \"(rsc)/./app/components/Footer.tsx\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./contexts/DataRefreshContext */ \"(rsc)/./app/contexts/DataRefreshContext.tsx\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variableName_poppins___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variableName_poppins___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variableName_poppins___WEBPACK_IMPORTED_MODULE_7__);\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Application Assainissement \",\n    description: \"Gestion des cas d'assainissement\"\n};\nasync function getUser() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_4__.cookies)(); // Await the cookies() call\n    const token = cookieStore.get(\"token\")?.value;\n    if (!token) return null;\n    // Ensure verifyToken can handle null or malformed tokens gracefully\n    try {\n        const decoded = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_5__.verifyToken)(token); // Assuming verifyToken is async\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Single RootLayout definition\nasync function RootLayout({ children }) {\n    const user = await getUser();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variableName_poppins___WEBPACK_IMPORTED_MODULE_7___default().className)} bg-slate-50 text-slate-800 min-h-screen flex flex-col`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.DataRefreshProvider, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_2__.Navigation, {\n                        user: user\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto px-1 py-5 mt-16 flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 21\n                    }, this),\n                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 30\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\layout.tsx\",\n            lineNumber: 55,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\layout.tsx\",\n        lineNumber: 54,\n        columnNumber: 9\n    }, this);\n} // All duplicated content below this line has been removed.\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUF1QztBQUNSO0FBQ0Q7QUFDSTtBQWFsQyxzQ0FBc0M7QUFDL0IsZUFBZUksWUFBWUMsS0FBYTtJQUMzQyxJQUFJO1FBQ0EsTUFBTUMsU0FBU0MsUUFBUUMsR0FBRyxDQUFDQyxVQUFVO1FBQ3JDLElBQUksQ0FBQ0gsUUFBUTtZQUNUSSxRQUFRQyxLQUFLLENBQUM7WUFDZCxPQUFPO1FBQ1g7UUFFQSxNQUFNQyxVQUFVWCwwREFBVSxDQUFDSSxPQUFPQztRQUNsQyxPQUFPTTtJQUNYLEVBQUUsT0FBT0QsT0FBTztRQUNaRCxRQUFRQyxLQUFLLENBQUMsOEJBQThCQTtRQUM1QyxPQUFPO0lBQ1g7QUFDSjtBQUVBLDZCQUE2QjtBQUN0QixlQUFlRyxhQUFhQyxRQUFnQjtJQUMvQyxJQUFJO1FBQ0EsTUFBTUMsYUFBYTtRQUNuQixNQUFNQyxpQkFBaUIsTUFBTWYsb0RBQVcsQ0FBQ2EsVUFBVUM7UUFDbkQsT0FBT0M7SUFDWCxFQUFFLE9BQU9OLE9BQU87UUFDWkQsUUFBUUMsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsTUFBTSxJQUFJUSxNQUFNO0lBQ3BCO0FBQ0o7QUFFQSwrQkFBK0I7QUFDeEIsZUFBZUMsZUFDbEJMLFFBQWdCLEVBQ2hCRSxjQUFzQjtJQUV0QixJQUFJO1FBQ0EsTUFBTUksVUFBVSxNQUFNbkIsdURBQWMsQ0FBQ2EsVUFBVUU7UUFDL0MsT0FBT0k7SUFDWCxFQUFFLE9BQU9WLE9BQU87UUFDWkQsUUFBUUMsS0FBSyxDQUFDLGlDQUFpQ0E7UUFDL0MsT0FBTztJQUNYO0FBQ0o7QUFFQSxtQkFBbUI7QUFDWixlQUFlWSxZQUNsQkMsT0FBd0M7SUFFeEMsSUFBSTtRQUNBLE1BQU1sQixTQUFTQyxRQUFRQyxHQUFHLENBQUNDLFVBQVU7UUFDckMsSUFBSSxDQUFDSCxRQUFRO1lBQ1RJLFFBQVFDLEtBQUssQ0FBQztZQUNkLE1BQU0sSUFBSVEsTUFBTTtRQUNwQjtRQUVBLE1BQU1kLFFBQVFKLHdEQUFRLENBQUN1QixTQUFTbEIsUUFBUTtZQUNwQ29CLFdBQVc7UUFDZjtRQUNBLE9BQU9yQjtJQUNYLEVBQUUsT0FBT00sT0FBTztRQUNaRCxRQUFRQyxLQUFLLENBQUMsMEJBQTBCQTtRQUN4QyxNQUFNLElBQUlRLE1BQU07SUFDcEI7QUFDSjtBQUVBLDhDQUE4QztBQUN2QyxlQUFlUTtJQUNsQixJQUFJO1FBQ0EsTUFBTUMsY0FBYyxNQUFNNUIscURBQU9BO1FBQ2pDLE1BQU1LLFFBQVF1QixZQUFZQyxHQUFHLENBQUMsVUFBVUM7UUFFeEMsSUFBSSxDQUFDekIsT0FBTztZQUNSLE9BQU87UUFDWDtRQUVBLE1BQU1tQixVQUFVLE1BQU1wQixZQUFZQztRQUNsQyxJQUFJLENBQUNtQixXQUFXLENBQUNBLFFBQVFPLEVBQUUsRUFBRTtZQUN6QixPQUFPO1FBQ1g7UUFFQSxvREFBb0Q7UUFDcEQsTUFBTUMsT0FBTyxNQUFNN0IsMkNBQU1BLENBQUM2QixJQUFJLENBQUNDLFVBQVUsQ0FBQztZQUN0Q0MsT0FBTztnQkFBRUgsSUFBSVAsUUFBUU8sRUFBRTtZQUFDO1lBQ3hCSSxRQUFRO2dCQUNKSixJQUFJO2dCQUNKSyxVQUFVO2dCQUNWQyxPQUFPO2dCQUNQQyxNQUFNO2dCQUNOQyxVQUFVO1lBQ2Q7UUFDSjtRQUVBLE9BQU9QO0lBQ1gsRUFBRSxPQUFPckIsT0FBTztRQUNaRCxRQUFRQyxLQUFLLENBQUMsdUJBQXVCQTtRQUNyQyxPQUFPO0lBQ1g7QUFDSixFQUVBLGtEQUFrRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxSb3VsYVxcRGVza3RvcFxcQVBQTElDQVRJT05TXFxhc3NhaW5pc3NlbWVudFY1XFxsaWJcXGF1dGgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29va2llcyB9IGZyb20gXCJuZXh0L2hlYWRlcnNcIjtcbmltcG9ydCBqd3QgZnJvbSBcImpzb253ZWJ0b2tlblwiO1xuaW1wb3J0IGJjcnlwdCBmcm9tIFwiYmNyeXB0anNcIjtcbmltcG9ydCB7IHByaXNtYSB9IGZyb20gXCIuL3ByaXNtYVwiO1xuXG4vLyBJbnRlcmZhY2UgZm9yIEpXVCBwYXlsb2FkXG5pbnRlcmZhY2UgSldUUGF5bG9hZCB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBlbWFpbDogc3RyaW5nO1xuICAgIHJvbGU6IHN0cmluZztcbiAgICB1c2VybmFtZTogc3RyaW5nO1xuICAgIHdpbGF5YUlkPzogbnVtYmVyO1xuICAgIGlhdD86IG51bWJlcjtcbiAgICBleHA/OiBudW1iZXI7XG59XG5cbi8vIFZlcmlmeSBKV1QgdG9rZW4gYW5kIHJldHVybiBwYXlsb2FkXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5VG9rZW4odG9rZW46IHN0cmluZyk6IFByb21pc2U8SldUUGF5bG9hZCB8IG51bGw+IHtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCBzZWNyZXQgPSBwcm9jZXNzLmVudi5KV1RfU0VDUkVUO1xuICAgICAgICBpZiAoIXNlY3JldCkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkpXVF9TRUNSRVQgaXMgbm90IGRlZmluZWRcIik7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGRlY29kZWQgPSBqd3QudmVyaWZ5KHRva2VuLCBzZWNyZXQpIGFzIEpXVFBheWxvYWQ7XG4gICAgICAgIHJldHVybiBkZWNvZGVkO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJUb2tlbiB2ZXJpZmljYXRpb24gZmFpbGVkOlwiLCBlcnJvcik7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbn1cblxuLy8gSGFzaCBwYXNzd29yZCB1c2luZyBiY3J5cHRcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBoYXNoUGFzc3dvcmQocGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgc2FsdFJvdW5kcyA9IDEyO1xuICAgICAgICBjb25zdCBoYXNoZWRQYXNzd29yZCA9IGF3YWl0IGJjcnlwdC5oYXNoKHBhc3N3b3JkLCBzYWx0Um91bmRzKTtcbiAgICAgICAgcmV0dXJuIGhhc2hlZFBhc3N3b3JkO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJQYXNzd29yZCBoYXNoaW5nIGZhaWxlZDpcIiwgZXJyb3IpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gaGFzaCBwYXNzd29yZFwiKTtcbiAgICB9XG59XG5cbi8vIFZlcmlmeSBwYXNzd29yZCBhZ2FpbnN0IGhhc2hcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB2ZXJpZnlQYXNzd29yZChcbiAgICBwYXNzd29yZDogc3RyaW5nLFxuICAgIGhhc2hlZFBhc3N3b3JkOiBzdHJpbmdcbik6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGlzVmFsaWQgPSBhd2FpdCBiY3J5cHQuY29tcGFyZShwYXNzd29yZCwgaGFzaGVkUGFzc3dvcmQpO1xuICAgICAgICByZXR1cm4gaXNWYWxpZDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiUGFzc3dvcmQgdmVyaWZpY2F0aW9uIGZhaWxlZDpcIiwgZXJyb3IpO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxufVxuXG4vLyBDcmVhdGUgSldUIHRva2VuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlVG9rZW4oXG4gICAgcGF5bG9hZDogT21pdDxKV1RQYXlsb2FkLCBcImlhdFwiIHwgXCJleHBcIj5cbik6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgc2VjcmV0ID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVDtcbiAgICAgICAgaWYgKCFzZWNyZXQpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJKV1RfU0VDUkVUIGlzIG5vdCBkZWZpbmVkXCIpO1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSldUX1NFQ1JFVCBpcyBub3QgZGVmaW5lZFwiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHRva2VuID0gand0LnNpZ24ocGF5bG9hZCwgc2VjcmV0LCB7XG4gICAgICAgICAgICBleHBpcmVzSW46IFwiMjRoXCIsIC8vIFRva2VuIGV4cGlyZXMgaW4gMjQgaG91cnNcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiB0b2tlbjtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiVG9rZW4gY3JlYXRpb24gZmFpbGVkOlwiLCBlcnJvcik7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBjcmVhdGUgdG9rZW5cIik7XG4gICAgfVxufVxuXG4vLyBLZWVwIG9ubHkgb25lIGdldFVzZXIgZnVuY3Rpb24gaW4gdGhpcyBmaWxlXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VXNlcigpIHtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCBjb29raWVTdG9yZSA9IGF3YWl0IGNvb2tpZXMoKTtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBjb29raWVTdG9yZS5nZXQoXCJ0b2tlblwiKT8udmFsdWU7XG5cbiAgICAgICAgaWYgKCF0b2tlbikge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBwYXlsb2FkID0gYXdhaXQgdmVyaWZ5VG9rZW4odG9rZW4pO1xuICAgICAgICBpZiAoIXBheWxvYWQgfHwgIXBheWxvYWQuaWQpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gUsOpY3Vww6lyZXIgbCd1dGlsaXNhdGV1ciBkZXB1aXMgbGEgYmFzZSBkZSBkb25uw6llc1xuICAgICAgICBjb25zdCB1c2VyID0gYXdhaXQgcHJpc21hLnVzZXIuZmluZFVuaXF1ZSh7XG4gICAgICAgICAgICB3aGVyZTogeyBpZDogcGF5bG9hZC5pZCB9LFxuICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgICAgICAgdXNlcm5hbWU6IHRydWUsXG4gICAgICAgICAgICAgICAgZW1haWw6IHRydWUsXG4gICAgICAgICAgICAgICAgcm9sZTogdHJ1ZSxcbiAgICAgICAgICAgICAgICB3aWxheWFJZDogdHJ1ZSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuXG4gICAgICAgIHJldHVybiB1c2VyO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZXR0aW5nIHVzZXI6XCIsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxufVxuXG4vLyBSZW1vdmUgYW55IG90aGVyIGdldFVzZXIgZnVuY3Rpb25zIGluIHRoaXMgZmlsZVxuIl0sIm5hbWVzIjpbImNvb2tpZXMiLCJqd3QiLCJiY3J5cHQiLCJwcmlzbWEiLCJ2ZXJpZnlUb2tlbiIsInRva2VuIiwic2VjcmV0IiwicHJvY2VzcyIsImVudiIsIkpXVF9TRUNSRVQiLCJjb25zb2xlIiwiZXJyb3IiLCJkZWNvZGVkIiwidmVyaWZ5IiwiaGFzaFBhc3N3b3JkIiwicGFzc3dvcmQiLCJzYWx0Um91bmRzIiwiaGFzaGVkUGFzc3dvcmQiLCJoYXNoIiwiRXJyb3IiLCJ2ZXJpZnlQYXNzd29yZCIsImlzVmFsaWQiLCJjb21wYXJlIiwiY3JlYXRlVG9rZW4iLCJwYXlsb2FkIiwic2lnbiIsImV4cGlyZXNJbiIsImdldFVzZXIiLCJjb29raWVTdG9yZSIsImdldCIsInZhbHVlIiwiaWQiLCJ1c2VyIiwiZmluZFVuaXF1ZSIsIndoZXJlIiwic2VsZWN0IiwidXNlcm5hbWUiLCJlbWFpbCIsInJvbGUiLCJ3aWxheWFJZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fstatistiques%2Fpage&page=%2Fdashboard%2Fstatistiques%2Fpage&appPaths=%2Fdashboard%2Fstatistiques%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fstatistiques%2Fpage.tsx&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fstatistiques%2Fpage&page=%2Fdashboard%2Fstatistiques%2Fpage&appPaths=%2Fdashboard%2Fstatistiques%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fstatistiques%2Fpage.tsx&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(rsc)/./app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/statistiques/page.tsx */ \"(rsc)/./app/dashboard/statistiques/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'statistiques',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/statistiques/page\",\n        pathname: \"/dashboard/statistiques\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fstatistiques%2Fpage&page=%2Fdashboard%2Fstatistiques%2Fpage&appPaths=%2Fdashboard%2Fstatistiques%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fstatistiques%2Fpage.tsx&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccontexts%5C%5CDataRefreshContext.tsx%22%2C%22ids%22%3A%5B%22DataRefreshProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccontexts%5C%5CDataRefreshContext.tsx%22%2C%22ids%22%3A%5B%22DataRefreshProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Footer.tsx */ \"(rsc)/./app/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Navigation.tsx */ \"(rsc)/./app/components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/contexts/DataRefreshContext.tsx */ \"(rsc)/./app/contexts/DataRefreshContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccontexts%5C%5CDataRefreshContext.tsx%22%2C%22ids%22%3A%5B%22DataRefreshProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebarContext.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5CMainContentClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebarContext.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5CMainContentClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Sidebar.tsx */ \"(rsc)/./app/components/Sidebar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/SidebarContext.tsx */ \"(rsc)/./app/components/SidebarContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/MainContentClient.tsx */ \"(rsc)/./app/dashboard/MainContentClient.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1JvdWxhJTVDJTVDRGVza3RvcCU1QyU1Q0FQUExJQ0FUSU9OUyU1QyU1Q2Fzc2Fpbmlzc2VtZW50VjUlNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDU2lkZWJhci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJTaWRlYmFyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1JvdWxhJTVDJTVDRGVza3RvcCU1QyU1Q0FQUExJQ0FUSU9OUyU1QyU1Q2Fzc2Fpbmlzc2VtZW50VjUlNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDU2lkZWJhckNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyU2lkZWJhclByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1JvdWxhJTVDJTVDRGVza3RvcCU1QyU1Q0FQUExJQ0FUSU9OUyU1QyU1Q2Fzc2Fpbmlzc2VtZW50VjUlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNNYWluQ29udGVudENsaWVudC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBeUo7QUFDeko7QUFDQSxrTEFBd0s7QUFDeEs7QUFDQSxzTEFBa0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlNpZGViYXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxSb3VsYVxcXFxEZXNrdG9wXFxcXEFQUExJQ0FUSU9OU1xcXFxhc3NhaW5pc3NlbWVudFY1XFxcXGFwcFxcXFxjb21wb25lbnRzXFxcXFNpZGViYXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTaWRlYmFyUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxSb3VsYVxcXFxEZXNrdG9wXFxcXEFQUExJQ0FUSU9OU1xcXFxhc3NhaW5pc3NlbWVudFY1XFxcXGFwcFxcXFxjb21wb25lbnRzXFxcXFNpZGViYXJDb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFJvdWxhXFxcXERlc2t0b3BcXFxcQVBQTElDQVRJT05TXFxcXGFzc2Fpbmlzc2VtZW50VjVcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxNYWluQ29udGVudENsaWVudC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebarContext.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5CMainContentClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5Cstatistiques%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5Cstatistiques%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/statistiques/page.tsx */ \"(rsc)/./app/dashboard/statistiques/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1JvdWxhJTVDJTVDRGVza3RvcCU1QyU1Q0FQUExJQ0FUSU9OUyU1QyU1Q2Fzc2Fpbmlzc2VtZW50VjUlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNzdGF0aXN0aXF1ZXMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQXNJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxSb3VsYVxcXFxEZXNrdG9wXFxcXEFQUExJQ0FUSU9OU1xcXFxhc3NhaW5pc3NlbWVudFY1XFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcc3RhdGlzdGlxdWVzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5Cstatistiques%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/Footer.tsx":
/*!***********************************!*\
  !*** ./app/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\nfunction Footer() {\n    const [currentYear, setCurrentYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().getFullYear());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Footer.useEffect\": ()=>{\n            // Mettre à jour l'année au cas où l'application reste ouverte longtemps\n            const updateYear = {\n                \"Footer.useEffect.updateYear\": ()=>{\n                    setCurrentYear(new Date().getFullYear());\n                }\n            }[\"Footer.useEffect.updateYear\"];\n            // Vérifier l'année chaque jour\n            const interval = setInterval(updateYear, 24 * 60 * 60 * 1000);\n            return ({\n                \"Footer.useEffect\": ()=>clearInterval(interval)\n            })[\"Footer.useEffect\"];\n        }\n    }[\"Footer.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gradient-to-r from-slate-800 to-slate-900 text-white mt-auto border-t border-slate-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-6 md:py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white\",\n                                                children: \"Assainissement Agricole\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 text-xs font-medium\",\n                                                children: \"Minist\\xe8re de l'Agriculture et du developpement rural et de la P\\xeache\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-300 text-sm leading-relaxed\",\n                                    children: \"Syst\\xe8me de gestion des dossiers d'assainissement foncier pour une meilleure administration des ressources agricoles et environnementales.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-white border-b border-slate-600 pb-2 flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-blue-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-200 text-sm font-medium\",\n                                                            children: \"DIRECTION GENERALE DE L’INVESTISSEMENT ET DU FONCIER AGRICOLES\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-400 text-xs\",\n                                                            children: \"R\\xe9publique Alg\\xe9rienne\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center group-hover:bg-blue-600 transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-400 group-hover:text-white transition-colors duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"mailto:<EMAIL>\",\n                                                            className: \"text-slate-300 hover:text-blue-400 transition-colors duration-200 text-sm font-medium\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-400 text-xs\",\n                                                            children: \"Support technique\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 31\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4 lp:space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-white border-b border-slate-600 pb-2 flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-blue-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Liens rapides\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/dashboard\",\n                                            className: \"flex items-center space-x-2 text-slate-300 hover:text-blue-400 transition-colors duration-200 text-sm group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-slate-500 group-hover:text-blue-400 transition-colors duration-200\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Tableau de bord\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/cas\",\n                                            className: \"flex items-center space-x-2 text-slate-300 hover:text-blue-400 transition-colors duration-200 text-sm group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-slate-500 group-hover:text-blue-400 transition-colors duration-200\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Gestion des cas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/dashboard/cartographie\",\n                                            className: \"flex items-center space-x-2 text-slate-300 hover:text-blue-400 transition-colors duration-200 text-sm group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-slate-500 group-hover:text-blue-400 transition-colors duration-200\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Cartographie\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/dashboard/reglementation\",\n                                            className: \"flex items-center space-x-2 text-slate-300 hover:text-blue-400 transition-colors duration-200 text-sm group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-slate-500 group-hover:text-blue-400 transition-colors duration-200\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"R\\xe9glementation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-slate-600 mt-6 md:mt-8 pt-4 md:pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-3 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-slate-400 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-slate-700 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3 text-blue-400\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"\\xa9 \",\n                                            currentYear,\n                                            \" MADRP. Tous droits r\\xe9serv\\xe9s.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-slate-400 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Version 1.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3 h-3 text-slate-500\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"D\\xe9velopp\\xe9 avec Next.js\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n            lineNumber: 24,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Footer.tsx\",\n        lineNumber: 23,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Gb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU0QztBQUVyQyxTQUFTRTtJQUNaLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHSiwrQ0FBUUEsQ0FDMUMsSUFBSUssT0FBT0MsV0FBVztJQUcxQkwsZ0RBQVNBOzRCQUFDO1lBQ04sd0VBQXdFO1lBQ3hFLE1BQU1NOytDQUFhO29CQUNmSCxlQUFlLElBQUlDLE9BQU9DLFdBQVc7Z0JBQ3pDOztZQUVBLCtCQUErQjtZQUMvQixNQUFNRSxXQUFXQyxZQUFZRixZQUFZLEtBQUssS0FBSyxLQUFLO1lBRXhEO29DQUFPLElBQU1HLGNBQWNGOztRQUMvQjsyQkFBRyxFQUFFO0lBRUwscUJBQ0ksOERBQUNHO1FBQU9DLFdBQVU7a0JBQ2QsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUVYLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBRVgsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDWCw4REFBQ0M7b0NBQUlELFdBQVU7OENBZ0JYLDRFQUFDQzs7MERBQ0csOERBQUNDO2dEQUFHRixXQUFVOzBEQUErQjs7Ozs7OzBEQUc3Qyw4REFBQ0c7Z0RBQUVILFdBQVU7MERBQW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNekQsOERBQUNHO29DQUFFSCxXQUFVOzhDQUF5Qzs7Ozs7Ozs7Ozs7O3NDQVExRCw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNYLDhEQUFDSTtvQ0FBR0osV0FBVTs7c0RBQ1YsOERBQUNLOzRDQUNHTCxXQUFVOzRDQUNWTSxNQUFLOzRDQUNMQyxRQUFPOzRDQUNQQyxTQUFRO3NEQUVSLDRFQUFDQztnREFDR0MsZUFBYztnREFDZEMsZ0JBQWU7Z0RBQ2ZDLGFBQWE7Z0RBQ2JDLEdBQUU7Ozs7Ozs7Ozs7O3NEQUdWLDhEQUFDQztzREFBSzs7Ozs7Ozs7Ozs7OzhDQUVWLDhEQUFDYjtvQ0FBSUQsV0FBVTs7c0RBNkJYLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ1gsOERBQUNDO29EQUFJRCxXQUFVOzhEQUNYLDRFQUFDSzt3REFDR0wsV0FBVTt3REFDVk0sTUFBSzt3REFDTEMsUUFBTzt3REFDUEMsU0FBUTtrRUFFUiw0RUFBQ0M7NERBQ0dDLGVBQWM7NERBQ2RDLGdCQUFlOzREQUNmQyxhQUFhOzREQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzhEQUlkLDhEQUFDWjs7c0VBQ0csOERBQUNhOzREQUFLZCxXQUFVO3NFQUFxQzs7Ozs7O3NFQUlyRCw4REFBQ0c7NERBQUVILFdBQVU7c0VBQXlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSzVDLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFVOzhEQUNYLDRFQUFDSzt3REFDR0wsV0FBVTt3REFDVk0sTUFBSzt3REFDTEMsUUFBTzt3REFDUEMsU0FBUTtrRUFFUiw0RUFBQ0M7NERBQ0dDLGVBQWM7NERBQ2RDLGdCQUFlOzREQUNmQyxhQUFhOzREQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzhEQUlkLDhEQUFDWjs7c0VBQ0csOERBQUNjOzREQUNHQyxNQUFLOzREQUNMaEIsV0FBVTtzRUFDYjs7Ozs7O3NFQUdELDhEQUFDRzs0REFBRUgsV0FBVTtzRUFBeUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTdEQsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDWCw4REFBQ0k7b0NBQUdKLFdBQVU7O3NEQUNWLDhEQUFDSzs0Q0FDR0wsV0FBVTs0Q0FDVk0sTUFBSzs0Q0FDTEMsUUFBTzs0Q0FDUEMsU0FBUTtzREFFUiw0RUFBQ0M7Z0RBQ0dDLGVBQWM7Z0RBQ2RDLGdCQUFlO2dEQUNmQyxhQUFhO2dEQUNiQyxHQUFFOzs7Ozs7Ozs7OztzREFHViw4REFBQ0M7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFViw4REFBQ0c7b0NBQUlqQixXQUFVOztzREFDWCw4REFBQ2U7NENBQ0dDLE1BQUs7NENBQ0xoQixXQUFVOzs4REFFViw4REFBQ0s7b0RBQ0dMLFdBQVU7b0RBQ1ZNLE1BQUs7b0RBQ0xDLFFBQU87b0RBQ1BDLFNBQVE7OERBRVIsNEVBQUNDO3dEQUNHQyxlQUFjO3dEQUNkQyxnQkFBZTt3REFDZkMsYUFBYTt3REFDYkMsR0FBRTs7Ozs7Ozs7Ozs7OERBR1YsOERBQUNDOzhEQUFLOzs7Ozs7Ozs7Ozs7c0RBRVYsOERBQUNDOzRDQUNHQyxNQUFLOzRDQUNMaEIsV0FBVTs7OERBRVYsOERBQUNLO29EQUNHTCxXQUFVO29EQUNWTSxNQUFLO29EQUNMQyxRQUFPO29EQUNQQyxTQUFROzhEQUVSLDRFQUFDQzt3REFDR0MsZUFBYzt3REFDZEMsZ0JBQWU7d0RBQ2ZDLGFBQWE7d0RBQ2JDLEdBQUU7Ozs7Ozs7Ozs7OzhEQUdWLDhEQUFDQzs4REFBSzs7Ozs7Ozs7Ozs7O3NEQUVWLDhEQUFDQzs0Q0FDR0MsTUFBSzs0Q0FDTGhCLFdBQVU7OzhEQUVWLDhEQUFDSztvREFDR0wsV0FBVTtvREFDVk0sTUFBSztvREFDTEMsUUFBTztvREFDUEMsU0FBUTs4REFFUiw0RUFBQ0M7d0RBQ0dDLGVBQWM7d0RBQ2RDLGdCQUFlO3dEQUNmQyxhQUFhO3dEQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs4REFHViw4REFBQ0M7OERBQUs7Ozs7Ozs7Ozs7OztzREFFViw4REFBQ0M7NENBQ0dDLE1BQUs7NENBQ0xoQixXQUFVOzs4REFFViw4REFBQ0s7b0RBQ0dMLFdBQVU7b0RBQ1ZNLE1BQUs7b0RBQ0xDLFFBQU87b0RBQ1BDLFNBQVE7OERBRVIsNEVBQUNDO3dEQUNHQyxlQUFjO3dEQUNkQyxnQkFBZTt3REFDZkMsYUFBYTt3REFDYkMsR0FBRTs7Ozs7Ozs7Ozs7OERBR1YsOERBQUNDOzhEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT3RCLDhEQUFDYjtvQkFBSUQsV0FBVTs4QkFDWCw0RUFBQ0M7d0JBQUlELFdBQVU7OzBDQUVYLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ1gsOERBQUNDO3dDQUFJRCxXQUFVO2tEQUNYLDRFQUFDSzs0Q0FDR0wsV0FBVTs0Q0FDVk0sTUFBSzs0Q0FDTEUsU0FBUTtzREFFUiw0RUFBQ0M7Z0RBQ0dTLFVBQVM7Z0RBQ1RMLEdBQUU7Z0RBQ0ZNLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSXJCLDhEQUFDTDs7NENBQUs7NENBQ0N2Qjs0Q0FBWTs7Ozs7Ozs7Ozs7OzswQ0FNdkIsOERBQUNVO2dDQUFJRCxXQUFVOztrREFDWCw4REFBQ0M7d0NBQUlELFdBQVU7OzBEQUNYLDhEQUFDQztnREFBSUQsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDYzswREFBSzs7Ozs7Ozs7Ozs7O2tEQUVWLDhEQUFDQTt3Q0FBS2QsV0FBVTtrREFBbUI7Ozs7OztrREFDbkMsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDWCw4REFBQ0s7Z0RBQ0dMLFdBQVU7Z0RBQ1ZNLE1BQUs7Z0RBQ0xFLFNBQVE7MERBRVIsNEVBQUNDO29EQUNHUyxVQUFTO29EQUNUTCxHQUFFO29EQUNGTSxVQUFTOzs7Ozs7Ozs7OzswREFHakIsOERBQUNMOzBEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXRDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFJvdWxhXFxEZXNrdG9wXFxBUFBMSUNBVElPTlNcXGFzc2Fpbmlzc2VtZW50VjVcXGFwcFxcY29tcG9uZW50c1xcRm9vdGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuXG5leHBvcnQgZnVuY3Rpb24gRm9vdGVyKCkge1xuICAgIGNvbnN0IFtjdXJyZW50WWVhciwgc2V0Q3VycmVudFllYXJdID0gdXNlU3RhdGU8bnVtYmVyPihcbiAgICAgICAgbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpXG4gICAgKTtcblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIC8vIE1ldHRyZSDDoCBqb3VyIGwnYW5uw6llIGF1IGNhcyBvw7kgbCdhcHBsaWNhdGlvbiByZXN0ZSBvdXZlcnRlIGxvbmd0ZW1wc1xuICAgICAgICBjb25zdCB1cGRhdGVZZWFyID0gKCkgPT4ge1xuICAgICAgICAgICAgc2V0Q3VycmVudFllYXIobmV3IERhdGUoKS5nZXRGdWxsWWVhcigpKTtcbiAgICAgICAgfTtcblxuICAgICAgICAvLyBWw6lyaWZpZXIgbCdhbm7DqWUgY2hhcXVlIGpvdXJcbiAgICAgICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCh1cGRhdGVZZWFyLCAyNCAqIDYwICogNjAgKiAxMDAwKTtcblxuICAgICAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gICAgfSwgW10pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tc2xhdGUtODAwIHRvLXNsYXRlLTkwMCB0ZXh0LXdoaXRlIG10LWF1dG8gYm9yZGVyLXQgYm9yZGVyLXNsYXRlLTcwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LTYgbWQ6cHktOFwiPlxuICAgICAgICAgICAgICAgIHsvKiBDb250ZW51IHByaW5jaXBhbCBkdSBmb290ZXIgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02IG1kOmdhcC04XCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBTZWN0aW9uIENvcHlyaWdodCAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwMCB0by1ibHVlLTYwMCByb3VuZGVkLXhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk0xOS40MjggMTUuNDI4YTIgMiAwIDAwLTEuMDIyLS41NDdsLTIuMzg3LS40NzdhNiA2IDAgMDAtMy44Ni41MTdsLS4zMTguMTU4YTYgNiAwIDAxLTMuODYuNTE3TDYuMDUgMTUuMjFhMiAyIDAgMDAtMS44MDYuNTQ3TTggNGg4bC0xIDF2NS4xNzJhMiAyIDAgMDAuNTg2IDEuNDE0bDUgNWMxLjI2IDEuMjYuMzY3IDMuNDE0LTEuNDE1IDMuNDE0SDQuODI4Yy0xLjc4MiAwLTIuNjc0LTIuMTU0LTEuNDE0LTMuNDE0bDUtNUEyIDIgMCAwMDkgMTAuMTcyVjVMOCA0elwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBc3NhaW5pc3NlbWVudCBBZ3JpY29sZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMzAwIHRleHQteHMgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1pbmlzdMOocmUgZGUgbCdBZ3JpY3VsdHVyZSBldCBkdVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGV2ZWxvcHBlbWVudCBydXJhbCBldCBkZSBsYSBQw6pjaGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTMwMCB0ZXh0LXNtIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFN5c3TDqG1lIGRlIGdlc3Rpb24gZGVzIGRvc3NpZXJzIGQnYXNzYWluaXNzZW1lbnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb25jaWVyIHBvdXIgdW5lIG1laWxsZXVyZSBhZG1pbmlzdHJhdGlvbiBkZXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNzb3VyY2VzIGFncmljb2xlcyBldCBlbnZpcm9ubmVtZW50YWxlcy5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIFNlY3Rpb24gQ29udGFjdCAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIGJvcmRlci1iIGJvcmRlci1zbGF0ZS02MDAgcGItMiBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ibHVlLTQwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTggMTJoLjAxTTEyIDEyaC4wMU0xNiAxMmguMDFNMjEgMTJjMCA0LjQxOC00LjAzIDgtOSA4YTkuODYzIDkuODYzIDAgMDEtNC4yNTUtLjk0OUwzIDIwbDEuMzk1LTMuNzJDMy41MTIgMTUuMDQyIDMgMTMuNTc0IDMgMTJjMC00LjQxOCA0LjAzLTggOS04czkgMy41ODIgOSA4elwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+Q29udGFjdDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBncm91cFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctc2xhdGUtNzAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ3JvdXAtaG92ZXI6YmctYmx1ZS02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNDAwIGdyb3VwLWhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMyA4bDcuODkgNC4yNmEyIDIgMCAwMDIuMjIgMEwyMSA4TTUgMTloMTRhMiAyIDAgMDAyLTJWN2EyIDIgMCAwMC0yLTJINWEyIDIgMCAwMC0yIDJ2MTBhMiAyIDAgMDAyIDJ6XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwibWFpbHRvOm0ubGxAbWEuZHpcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtMzAwIGhvdmVyOnRleHQtYmx1ZS00MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIHRleHQtc20gZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG0ucm91bGFAbWFkci5nb3YuZHpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNDAwIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBTdXBwb3J0IHRlY2huaXF1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLXNsYXRlLTcwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNDAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMTkgMjFWNWEyIDIgMCAwMC0yLTJIN2EyIDIgMCAwMC0yIDJ2MTZtMTQgMGgybS0yIDBoLTVtLTkgMEgzbTIgMGg1TTkgN2gxbS0xIDRoMW00LTRoMW0tMSA0aDFtLTUgMTB2LTVhMSAxIDAgMDExLTFoMmExIDEgMCAwMTEgMXY1bS00IDBoNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtMjAwIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBESVJFQ1RJT04gR0VORVJBTEUgREUgTOKAmUlOVkVTVElTU0VNRU5UXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRVQgRFUgRk9OQ0lFUiBBR1JJQ09MRVNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNDAwIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSw6lwdWJsaXF1ZSBBbGfDqXJpZW5uZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIGdyb3VwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1zbGF0ZS03MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBncm91cC1ob3ZlcjpiZy1ibHVlLTYwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtYmx1ZS00MDAgZ3JvdXAtaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk0zIDhsNy44OSA0LjI2YTIgMiAwIDAwMi4yMiAwTDIxIDhNNSAxOWgxNGEyIDIgMCAwMDItMlY3YTIgMiAwIDAwLTItMkg1YTIgMiAwIDAwLTIgMnYxMGEyIDIgMCAwMDIgMnpcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCJtYWlsdG86bS5sbEBtYS5kelwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS0zMDAgaG92ZXI6dGV4dC1ibHVlLTQwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgdGV4dC1zbSBmb250LW1lZGl1bVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbS5yb3VsYUBtYWRyLmdvdi5kelxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS00MDAgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFN1cHBvcnQgdGVjaG5pcXVlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PiAgXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIFNlY3Rpb24gTGllbnMgcmFwaWRlcyAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNwYWNlLXktNCBscDpzcGFjZS14LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBib3JkZXItYiBib3JkZXItc2xhdGUtNjAwIHBiLTIgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtYmx1ZS00MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk0xMy44MjggMTAuMTcyYTQgNCAwIDAwLTUuNjU2IDBsLTQgNGE0IDQgMCAxMDUuNjU2IDUuNjU2bDEuMTAyLTEuMTAxbS0uNzU4LTQuODk5YTQgNCAwIDAwNS42NTYgMGw0LTRhNCA0IDAgMDAtNS42NTYtNS42NTZsLTEuMSAxLjFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkxpZW5zIHJhcGlkZXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXNsYXRlLTMwMCBob3Zlcjp0ZXh0LWJsdWUtNDAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCB0ZXh0LXNtIGdyb3VwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1zbGF0ZS01MDAgZ3JvdXAtaG92ZXI6dGV4dC1ibHVlLTQwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk05IDE5di02YTIgMiAwIDAwLTItMkg1YTIgMiAwIDAwLTIgMnY2YTIgMiAwIDAwMiAyaDJhMiAyIDAgMDAyLTJ6bTAgMFY5YTIgMiAwIDAxMi0yaDJhMiAyIDAgMDEyIDJ2MTBtLTYgMGEyIDIgMCAwMDIgMmgyYTIgMiAwIDAwMi0ybTAgMFY1YTIgMiAwIDAxMi0yaDJhMiAyIDAgMDEyIDJ2MTRhMiAyIDAgMDEtMiAyaC0yYTIgMiAwIDAxLTItMnpcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlRhYmxlYXUgZGUgYm9yZDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9jYXNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC1zbGF0ZS0zMDAgaG92ZXI6dGV4dC1ibHVlLTQwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgdGV4dC1zbSBncm91cFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtc2xhdGUtNTAwIGdyb3VwLWhvdmVyOnRleHQtYmx1ZS00MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNOSAxMmg2bS02IDRoNm0yIDVIN2EyIDIgMCAwMS0yLTJWNWEyIDIgMCAwMTItMmg1LjU4NmExIDEgMCAwMS43MDcuMjkzbDUuNDE0IDUuNDE0YTEgMSAwIDAxLjI5My43MDdWMTlhMiAyIDAgMDEtMiAyelwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+R2VzdGlvbiBkZXMgY2FzPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZC9jYXJ0b2dyYXBoaWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC1zbGF0ZS0zMDAgaG92ZXI6dGV4dC1ibHVlLTQwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgdGV4dC1zbSBncm91cFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtc2xhdGUtNTAwIGdyb3VwLWhvdmVyOnRleHQtYmx1ZS00MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNOSAyMGwtNS40NDctMi43MjRBMSAxIDAgMDEzIDE2LjM4MlY1LjYxOGExIDEgMCAwMTEuNDQ3LS44OTRMOSA3bTAgMTNsNi0zbS02IDNWN202IDEwbDQuNTUzIDIuMjc2QTEgMSAwIDAwMjEgMTguMzgyVjcuNjE4YTEgMSAwIDAwLS41NTMtLjg5NEwxNSA0bTAgMTNWNG0wIDBMOSA3XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5DYXJ0b2dyYXBoaWU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvZGFzaGJvYXJkL3JlZ2xlbWVudGF0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHRleHQtc2xhdGUtMzAwIGhvdmVyOnRleHQtYmx1ZS00MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIHRleHQtc20gZ3JvdXBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXNsYXRlLTUwMCBncm91cC1ob3Zlcjp0ZXh0LWJsdWUtNDAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTEyIDYuMjUzdjEzbTAtMTNDMTAuODMyIDUuNDc3IDkuMjQ2IDUgNy41IDVTNC4xNjggNS40NzcgMyA2LjI1M3YxM0M0LjE2OCAxOC40NzcgNS43NTQgMTggNy41IDE4czMuMzMyLjQ3NyA0LjUgMS4yNTNtMC0xM0MxMy4xNjggNS40NzcgMTQuNzU0IDUgMTYuNSA1YzEuNzQ3IDAgMy4zMzIuNDc3IDQuNSAxLjI1M3YxM0MxOS44MzIgMTguNDc3IDE4LjI0NyAxOCAxNi41IDE4Yy0xLjc0NiAwLTMuMzMyLjQ3Ny00LjUgMS4yNTNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlLDqWdsZW1lbnRhdGlvbjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L25hdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogTGlnbmUgZGUgc8OpcGFyYXRpb24gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItc2xhdGUtNjAwIG10LTYgbWQ6bXQtOCBwdC00IG1kOnB0LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgc3BhY2UteS0zIG1kOnNwYWNlLXktMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIENvcHlyaWdodCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHRleHQtc2xhdGUtNDAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNiBoLTYgYmctc2xhdGUtNzAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zIHRleHQtYmx1ZS00MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDIwIDIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMTAgMThhOCA4IDAgMTAwLTE2IDggOCAwIDAwMCAxNnpNOS41NTUgNy4xNjhBMSAxIDAgMDA4IDh2NGExIDEgMCAwMDEuNTU1LjgzMmwzLTJhMSAxIDAgMDAwLTEuNjY0bC0zLTJ6XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGlwUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMKpIHtjdXJyZW50WWVhcn0gTUFEUlAuIFRvdXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHJvaXRzIHLDqXNlcnbDqXMuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBWZXJzaW9uIGV0IGluZm9ybWF0aW9ucyB0ZWNobmlxdWVzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgdGV4dC1zbGF0ZS00MDAgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmVlbi00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+VmVyc2lvbiAxLjA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaGlkZGVuIHNtOmlubGluZVwiPuKAojwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBzbTpmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LXNsYXRlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjAgMjBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGxSdWxlPVwiZXZlbm9kZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk0xMi4zMTYgMy4wNTFhMSAxIDAgMDEuNjMzIDEuMjY1bC00IDEyYTEgMSAwIDExLTEuODk4LS42MzJsNC0xMmExIDEgMCAwMTEuMjY1LS42MzN6TTUuNzA3IDYuMjkzYTEgMSAwIDAxMCAxLjQxNEwzLjQxNCAxMGwyLjI5MyAyLjI5M2ExIDEgMCAxMS0xLjQxNCAxLjQxNGwtMy0zYTEgMSAwIDAxMC0xLjQxNGwzLTNhMSAxIDAgMDExLjQxNCAwem04LjU4NiAwYTEgMSAwIDAxMS40MTQgMGwzIDNhMSAxIDAgMDEwIDEuNDE0bC0zIDNhMSAxIDAgMTEtMS40MTQtMS40MTRMMTYuNTg2IDEwbC0yLjI5My0yLjI5M2ExIDEgMCAwMTAtMS40MTR6XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGlwUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkTDqXZlbG9wcMOpIGF2ZWMgTmV4dC5qczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvb3Rlcj5cbiAgICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiRm9vdGVyIiwiY3VycmVudFllYXIiLCJzZXRDdXJyZW50WWVhciIsIkRhdGUiLCJnZXRGdWxsWWVhciIsInVwZGF0ZVllYXIiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsImZvb3RlciIsImNsYXNzTmFtZSIsImRpdiIsImgzIiwicCIsImg0Iiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwic3BhbiIsImEiLCJocmVmIiwibmF2IiwiZmlsbFJ1bGUiLCJjbGlwUnVsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/LoadingSpinner.tsx":
/*!*******************************************!*\
  !*** ./app/components/LoadingSpinner.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction LoadingSpinner({ className = \"w-5 h-5\", color = \"light\" }) {\n    const colorClasses = {\n        light: \"border-white/80 border-t-transparent\",\n        dark: \"border-gray-700 border-t-transparent\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)(`border-2 rounded-full animate-spin`, colorClasses[color], className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\LoadingSpinner.tsx\",\n        lineNumber: 18,\n        columnNumber: 9\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingSpinner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Mb2FkaW5nU3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXlDO0FBT2xDLFNBQVNDLGVBQWUsRUFDM0JDLFlBQVksU0FBUyxFQUNyQkMsUUFBUSxPQUFPLEVBQ0c7SUFDbEIsTUFBTUMsZUFBZTtRQUNqQkMsT0FBTztRQUNQQyxNQUFNO0lBQ1Y7SUFFQSxxQkFDSSw4REFBQ0M7UUFDR0wsV0FBV0YsdURBQU9BLENBQ2QsQ0FBQyxrQ0FBa0MsQ0FBQyxFQUNwQ0ksWUFBWSxDQUFDRCxNQUFNLEVBQ25CRDs7Ozs7O0FBSWhCO0FBRUEsaUVBQWVELGNBQWNBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcYXBwXFxjb21wb25lbnRzXFxMb2FkaW5nU3Bpbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiO1xuXG5pbnRlcmZhY2UgTG9hZGluZ1NwaW5uZXJQcm9wcyB7XG4gICAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICAgIGNvbG9yPzogXCJsaWdodFwiIHwgXCJkYXJrXCI7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBMb2FkaW5nU3Bpbm5lcih7XG4gICAgY2xhc3NOYW1lID0gXCJ3LTUgaC01XCIsXG4gICAgY29sb3IgPSBcImxpZ2h0XCIsXG59OiBMb2FkaW5nU3Bpbm5lclByb3BzKSB7XG4gICAgY29uc3QgY29sb3JDbGFzc2VzID0ge1xuICAgICAgICBsaWdodDogXCJib3JkZXItd2hpdGUvODAgYm9yZGVyLXQtdHJhbnNwYXJlbnRcIixcbiAgICAgICAgZGFyazogXCJib3JkZXItZ3JheS03MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnRcIixcbiAgICB9O1xuXG4gICAgcmV0dXJuIChcbiAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPXt0d01lcmdlKFxuICAgICAgICAgICAgICAgIGBib3JkZXItMiByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluYCxcbiAgICAgICAgICAgICAgICBjb2xvckNsYXNzZXNbY29sb3JdLFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICAgICAgKX1cbiAgICAgICAgLz5cbiAgICApO1xufVxuXG5leHBvcnQgZGVmYXVsdCBMb2FkaW5nU3Bpbm5lcjtcbiJdLCJuYW1lcyI6WyJ0d01lcmdlIiwiTG9hZGluZ1NwaW5uZXIiLCJjbGFzc05hbWUiLCJjb2xvciIsImNvbG9yQ2xhc3NlcyIsImxpZ2h0IiwiZGFyayIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./app/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\nfunction NavLink({ href, children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const isActive = pathname === href;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: href,\n        className: `px-3 py-2 rounded-md text-sm font-medium ${isActive ? \"bg-gray-900 text-white\" : \"text-gray-300 hover:bg-gray-700 hover:text-white\"}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n        lineNumber: 28,\n        columnNumber: 9\n    }, this);\n}\n// Composant client pour le bouton de déconnexion\nfunction LogoutButton() {\n    // Méthode simple et directe pour la déconnexion\n    function handleLogout() {\n        // Redirection directe vers la page de déconnexion\n        window.location.href = \"/api/auth/logout\";\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleLogout,\n        className: \"flex items-center px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6 mr-1.5 text-gray-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                lineNumber: 61,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n            lineNumber: 54,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n        lineNumber: 50,\n        columnNumber: 9\n    }, this);\n}\nfunction Navigation({ user }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    console.log(\"user\", user);\n    // Always show navigation bar on all pages\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-md border-b border-gray-100 fixed top-0 left-0 right-0 z-20 \",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-8 lg:px-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between h-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex items-center gap-4 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/fan.gif\",\n                                alt: \"Logo Minist\\xe8re de l'Agriculture\",\n                                className: \"h-20 w-20   bg-transparent     transition-transform duration-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl md:text-2xl font-extrabold text-primary-700 tracking-tight group-hover:text-primary-600 transition-colors duration-200\",\n                                children: \"Assainissement du Foncier Agricole\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Bonjour, \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: user.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 46\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoutButton, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 29\n                            }, this) : \"\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center gap-4 group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo_v2_madr.png\",\n                                    alt: \"Logo Minist\\xe8re de l'Agriculture\",\n                                    className: \"h-16 w-16 rounded-full bg-white border border-gray-200 shadow group-hover:scale-105 transition-transform duration-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n                lineNumber: 84,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n            lineNumber: 83,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Navigation.tsx\",\n        lineNumber: 82,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/RoleBasedAccess.tsx":
/*!********************************************!*\
  !*** ./app/components/RoleBasedAccess.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminOnly: () => (/* binding */ AdminOnly),\n/* harmony export */   DeleteAccess: () => (/* binding */ DeleteAccess),\n/* harmony export */   EditorAccess: () => (/* binding */ EditorAccess),\n/* harmony export */   NonViewerAccess: () => (/* binding */ NonViewerAccess),\n/* harmony export */   PermissionButton: () => (/* binding */ PermissionButton),\n/* harmony export */   ReadOnlyMessage: () => (/* binding */ ReadOnlyMessage),\n/* harmony export */   RoleGuard: () => (/* binding */ RoleGuard),\n/* harmony export */   UserRoleBadge: () => (/* binding */ UserRoleBadge),\n/* harmony export */   WriteAccess: () => (/* binding */ WriteAccess),\n/* harmony export */   withRoleAccess: () => (/* binding */ withRoleAccess)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hooks/usePermissions */ \"(ssr)/./lib/hooks/usePermissions.ts\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_LockClosedIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,LockClosedIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_LockClosedIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,LockClosedIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js\");\n/* __next_internal_client_entry_do_not_use__ RoleGuard,WriteAccess,DeleteAccess,AdminOnly,EditorAccess,NonViewerAccess,UserRoleBadge,ReadOnlyMessage,PermissionButton,withRoleAccess auto */ \n\n\n\n/**\n * Component that conditionally renders children based on user roles or permissions\n */ function RoleGuard({ children, roles, permissions, fallback = null, requireAll = false }) {\n    const { user, permissions: userPermissions, loading } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_2__.usePermissions)();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse bg-gray-200 h-4 w-16 rounded\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n            lineNumber: 34,\n            columnNumber: 13\n        }, this);\n    }\n    if (!user || !userPermissions) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    // Check roles\n    if (roles) {\n        const roleArray = Array.isArray(roles) ? roles : [\n            roles\n        ];\n        const hasRole = requireAll ? roleArray.every((role)=>user.role === role) : roleArray.some((role)=>user.role === role);\n        if (!hasRole) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallback\n            }, void 0, false);\n        }\n    }\n    // Check permissions\n    if (permissions) {\n        const hasPermission = requireAll ? permissions.every((permission)=>userPermissions[permission]) : permissions.some((permission)=>userPermissions[permission]);\n        if (!hasPermission) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallback\n            }, void 0, false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n/**\n * Component that shows content only to users who can write\n */ function WriteAccess({ children, fallback = null }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoleGuard, {\n        permissions: [\n            \"canWrite\"\n        ],\n        fallback: fallback,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n        lineNumber: 79,\n        columnNumber: 9\n    }, this);\n}\n/**\n * Component that shows content only to users who can delete\n */ function DeleteAccess({ children, fallback = null }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoleGuard, {\n        permissions: [\n            \"canDelete\"\n        ],\n        fallback: fallback,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n        lineNumber: 96,\n        columnNumber: 9\n    }, this);\n}\n/**\n * Component that shows content only to admins\n */ function AdminOnly({ children, fallback = null }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoleGuard, {\n        roles: \"ADMIN\",\n        fallback: fallback,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n        lineNumber: 113,\n        columnNumber: 9\n    }, this);\n}\n/**\n * Component that shows content only to editors and admins\n */ function EditorAccess({ children, fallback = null }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoleGuard, {\n        roles: [\n            \"ADMIN\",\n            \"EDITOR\"\n        ],\n        fallback: fallback,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n        lineNumber: 130,\n        columnNumber: 9\n    }, this);\n}\n/**\n * Component that hides content from viewers\n */ function NonViewerAccess({ children, fallback = null }) {\n    const { isViewer } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_2__.usePermissions)();\n    if (isViewer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n/**\n * Badge component to show user's current role and read-only status\n */ function UserRoleBadge({ className = \"\" }) {\n    const { user, isReadOnly, loading } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_2__.usePermissions)();\n    if (loading || !user) {\n        return null;\n    }\n    const getRoleColor = (role)=>{\n        switch(role){\n            case \"ADMIN\":\n                return \"bg-red-100 text-red-800 border-red-200\";\n            case \"EDITOR\":\n                return \"bg-green-100 text-green-800 border-green-200\";\n            case \"BASIC\":\n                return \"bg-blue-100 text-blue-800 border-blue-200\";\n            case \"VIEWER\":\n                return \"bg-gray-100 text-gray-800 border-gray-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center space-x-2 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRoleColor(user.role)}`,\n                children: user.role\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n                lineNumber: 182,\n                columnNumber: 13\n            }, this),\n            isReadOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 border border-orange-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_LockClosedIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 21\n                    }, this),\n                    \"Lecture seule\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n                lineNumber: 190,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n        lineNumber: 181,\n        columnNumber: 9\n    }, this);\n}\n/**\n * Component that shows a read-only message for restricted actions\n */ function ReadOnlyMessage({ message = \"Cette action n'est pas disponible en mode lecture seule\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center space-x-2 text-sm text-gray-600 bg-gray-50 border border-gray-200 rounded-md p-3 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_LockClosedIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4 text-gray-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n                lineNumber: 213,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n                lineNumber: 214,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n        lineNumber: 210,\n        columnNumber: 9\n    }, this);\n}\nfunction PermissionButton({ requirePermission = \"canWrite\", requireRole, children, disabledMessage, className = \"\", disabled, title, ...props }) {\n    const { user, permissions: userPermissions } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_2__.usePermissions)();\n    const hasPermission = userPermissions?.[requirePermission] ?? false;\n    const hasRole = requireRole ? Array.isArray(requireRole) ? requireRole.includes(user?.role) : user?.role === requireRole : true;\n    const isDisabled = disabled || !hasPermission || !hasRole;\n    const buttonTitle = isDisabled && disabledMessage ? disabledMessage : title;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ...props,\n        disabled: isDisabled,\n        title: buttonTitle,\n        className: `${className} ${isDisabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n        lineNumber: 253,\n        columnNumber: 9\n    }, this);\n}\n/**\n * Higher-order component that wraps a component with role-based access control\n */ function withRoleAccess(Component, roles, permissions, fallback) {\n    return function WrappedComponent(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoleGuard, {\n            roles: roles,\n            permissions: permissions,\n            fallback: fallback,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n                lineNumber: 282,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\RoleBasedAccess.tsx\",\n            lineNumber: 277,\n            columnNumber: 13\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/RoleBasedAccess.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./app/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _SidebarContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SidebarContext */ \"(ssr)/./app/components/SidebarContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ScaleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightStartOnRectangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\nfunction Sidebar({ user }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isCollapsed, setIsCollapsed } = (0,_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const navItems = [\n        {\n            name: \"Tableau de bord\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Gestion Dossiers\",\n            href: \"/dashboard/cas\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Cartographie\",\n            href: \"/dashboard/cartographie\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: \"Réglementation\",\n            href: \"/dashboard/reglementation\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: \"Statistiques\",\n            href: \"/dashboard/statistiques\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Stats Simplifiées\",\n            href: \"/dashboard/statistiques-simple\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Stats Ultra-Simple\",\n            href: \"/dashboard/stats-ultra-simple\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Analyse Complète\",\n            href: \"/dashboard/statistiques-nouvelle\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Espace d'échange\",\n            href: \"/dashboard/echanges\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        ...user?.role === \"ADMIN\" ? [\n            {\n                name: \"Utilisateurs\",\n                href: \"/users\",\n                icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n            },\n            {\n                name: \"Performance\",\n                href: \"/admin/performance\",\n                icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n            }\n        ] : []\n    ];\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `h-[calc(100vh-2rem)] bg-gradient-to-b   from-sky-900 to-indigo-900/90 text-white flex flex-col transition-all duration-300 shadow-2xl rounded-r-3xl backdrop-blur-md border-r border-indigo-200/30 z-30 ${isCollapsed ? \"w-16\" : \"w-56\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-1 font-extrabold text-xl tracking-wide border-b border-indigo-700 flex items-center gap-2\",\n                    children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"bg-gradient-to-r from-sky-400 to-indigo-400 bg-clip-text text-transparent drop-shadow-lg\",\n                        children: \"Assainissement\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-3 border-b border-indigo-800\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-base font-semibold text-white\",\n                            children: \"Menu\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsCollapsed(!isCollapsed),\n                            className: \"p-2 rounded-md text-indigo-200 hover:bg-indigo-700 hover:text-white focus:outline-none\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-7 h-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-7 h-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-grow p-0 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `mb-4 pb-2 border-b border-indigo-800 ${isCollapsed ? \"hidden\" : \"block\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 rounded-full bg-gradient-to-r from-sky-400 to-indigo-500 flex items-center justify-center text-white font-bold text-lg shadow-lg border-2 border-white\",\n                                        children: user?.username ? user.username.charAt(0).toUpperCase() : \"?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 29\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-semibold text-white text-sm\",\n                                                children: user?.username || \"Utilisateur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-[11px] text-indigo-200 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `inline-block w-2 h-2 rounded-full mr-1.5 ${user?.role === \"ADMIN\" ? \"bg-red-400\" : user?.role === \"EDITOR\" ? \"bg-green-400\" : user?.role === \"VIEWER\" ? \"bg-gray-400\" : \"bg-blue-400\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    user?.role || \"BASIC\",\n                                                    user?.role === \"VIEWER\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-3 w-3 ml-1 text-orange-300\",\n                                                        title: \"Mode lecture seule\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: navItems.map((item)=>{\n                                    const isActive = item.href === \"/dashboard\" ? pathname === \"/dashboard\" : pathname === item.href || pathname.startsWith(`${item.href}/`);\n                                    const IconComponent = item.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            title: item.name,\n                                            className: `flex items-center p-2 rounded-xl transition-colors duration-150 text-base font-medium gap-2 ${isActive ? \"bg-gradient-to-r from-sky-500 to-indigo-500 text-white shadow-md\" : \"text-indigo-100 hover:bg-indigo-700 hover:text-white\"} ${isCollapsed ? \"justify-center\" : \"\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: `w-7 h-7 ${!isCollapsed ? \"mr-2\" : \"\"} drop-shadow-lg`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 45\n                                                }, this),\n                                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 53\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 37\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `p-2 border-t border-indigo-800  ${isCollapsed ? \"flex justify-center\" : \"\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = \"/api/auth/logout\",\n                        title: \"Se d\\xe9connecter\",\n                        className: `w-full flex items-center p-1 text-base rounded-xl transition-colors duration-150 ${isCollapsed ? \"justify-center\" : \"\"} text-indigo-100 hover:text-white hover:bg-indigo-700 font-semibold gap-2`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: `w-6 h-6 ${!isCollapsed ? \"mr-2\" : \"\"}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 25\n                            }, this),\n                            !isCollapsed && \"Se déconnecter\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 100,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 95,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/SidebarContext.tsx":
/*!*******************************************!*\
  !*** ./app/components/SidebarContext.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),\n/* harmony export */   useSidebar: () => (/* binding */ useSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useSidebar,SidebarProvider auto */ \n\nconst SidebarContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isCollapsed: false,\n    setIsCollapsed: (v)=>{}\n});\nconst useSidebar = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SidebarContext);\nfunction SidebarProvider({ children }) {\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContext.Provider, {\n        value: {\n            isCollapsed,\n            setIsCollapsed\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\SidebarContext.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9TaWRlYmFyQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVtRTtBQUVuRSxNQUFNSSwrQkFBaUJILG9EQUFhQSxDQUFDO0lBQUVJLGFBQWE7SUFBT0MsZ0JBQWdCLENBQUNDLEtBQWdCO0FBQUU7QUFDdkYsTUFBTUMsYUFBYSxJQUFNTixpREFBVUEsQ0FBQ0UsZ0JBQWdCO0FBRXBELFNBQVNLLGdCQUFnQixFQUFFQyxRQUFRLEVBQWlDO0lBQ3pFLE1BQU0sQ0FBQ0wsYUFBYUMsZUFBZSxHQUFHSCwrQ0FBUUEsQ0FBQztJQUMvQyxxQkFDRSw4REFBQ0MsZUFBZU8sUUFBUTtRQUFDQyxPQUFPO1lBQUVQO1lBQWFDO1FBQWU7a0JBQzNESTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcYXBwXFxjb21wb25lbnRzXFxTaWRlYmFyQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5jb25zdCBTaWRlYmFyQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoeyBpc0NvbGxhcHNlZDogZmFsc2UsIHNldElzQ29sbGFwc2VkOiAodjogYm9vbGVhbikgPT4ge30gfSk7XHJcbmV4cG9ydCBjb25zdCB1c2VTaWRlYmFyID0gKCkgPT4gdXNlQ29udGV4dChTaWRlYmFyQ29udGV4dCk7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gU2lkZWJhclByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcclxuICBjb25zdCBbaXNDb2xsYXBzZWQsIHNldElzQ29sbGFwc2VkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICByZXR1cm4gKFxyXG4gICAgPFNpZGViYXJDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7IGlzQ29sbGFwc2VkLCBzZXRJc0NvbGxhcHNlZCB9fT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9TaWRlYmFyQ29udGV4dC5Qcm92aWRlcj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsIlNpZGViYXJDb250ZXh0IiwiaXNDb2xsYXBzZWQiLCJzZXRJc0NvbGxhcHNlZCIsInYiLCJ1c2VTaWRlYmFyIiwiU2lkZWJhclByb3ZpZGVyIiwiY2hpbGRyZW4iLCJQcm92aWRlciIsInZhbHVlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/SidebarContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/contexts/DataRefreshContext.tsx":
/*!*********************************************!*\
  !*** ./app/contexts/DataRefreshContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataRefreshProvider: () => (/* binding */ DataRefreshProvider),\n/* harmony export */   useDataRefreshContext: () => (/* binding */ useDataRefreshContext),\n/* harmony export */   useOperationRefresh: () => (/* binding */ useOperationRefresh),\n/* harmony export */   useRegisterDataRefresh: () => (/* binding */ useRegisterDataRefresh)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ DataRefreshProvider,useDataRefreshContext,useRegisterDataRefresh,useOperationRefresh auto */ \n\nconst DataRefreshContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction DataRefreshProvider({ children }) {\n    const callbacksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const registerRefreshCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[registerRefreshCallback]\": (key, callback)=>{\n            callbacksRef.current[key] = callback;\n            console.log(`Callback enregistré pour: ${key}`);\n        }\n    }[\"DataRefreshProvider.useCallback[registerRefreshCallback]\"], []);\n    const unregisterRefreshCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[unregisterRefreshCallback]\": (key)=>{\n            delete callbacksRef.current[key];\n            console.log(`Callback désenregistré pour: ${key}`);\n        }\n    }[\"DataRefreshProvider.useCallback[unregisterRefreshCallback]\"], []);\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshData]\": async (keys)=>{\n            const keysToRefresh = keys || Object.keys(callbacksRef.current);\n            console.log(`Rafraîchissement des données pour: ${keysToRefresh.join(', ')}`);\n            try {\n                const promises = keysToRefresh.map({\n                    \"DataRefreshProvider.useCallback[refreshData].promises\": (key)=>{\n                        const callback = callbacksRef.current[key];\n                        if (callback) {\n                            console.log(`Exécution du callback pour: ${key}`);\n                            return callback();\n                        }\n                        return Promise.resolve();\n                    }\n                }[\"DataRefreshProvider.useCallback[refreshData].promises\"]);\n                await Promise.all(promises);\n                console.log('Rafraîchissement terminé avec succès');\n            } catch (error) {\n                console.error('Erreur lors du rafraîchissement des données:', error);\n                throw error;\n            }\n        }\n    }[\"DataRefreshProvider.useCallback[refreshData]\"], []);\n    const refreshAllData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshAllData]\": async ()=>{\n            console.log('Rafraîchissement de toutes les données');\n            return refreshData();\n        }\n    }[\"DataRefreshProvider.useCallback[refreshAllData]\"], [\n        refreshData\n    ]);\n    // Fonctions spécialisées pour différents types de données\n    const refreshCas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshCas]\": async ()=>{\n            const casKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshCas].casKeys\": (key)=>key.includes('cas') || key.includes('dossier')\n            }[\"DataRefreshProvider.useCallback[refreshCas].casKeys\"]);\n            return refreshData(casKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshCas]\"], [\n        refreshData\n    ]);\n    const refreshCommunes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshCommunes]\": async ()=>{\n            const communeKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshCommunes].communeKeys\": (key)=>key.includes('commune')\n            }[\"DataRefreshProvider.useCallback[refreshCommunes].communeKeys\"]);\n            return refreshData(communeKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshCommunes]\"], [\n        refreshData\n    ]);\n    const refreshEncrages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshEncrages]\": async ()=>{\n            const encrageKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshEncrages].encrageKeys\": (key)=>key.includes('encrage')\n            }[\"DataRefreshProvider.useCallback[refreshEncrages].encrageKeys\"]);\n            return refreshData(encrageKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshEncrages]\"], [\n        refreshData\n    ]);\n    const refreshProblematiques = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshProblematiques]\": async ()=>{\n            const problematiqueKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshProblematiques].problematiqueKeys\": (key)=>key.includes('problematique')\n            }[\"DataRefreshProvider.useCallback[refreshProblematiques].problematiqueKeys\"]);\n            return refreshData(problematiqueKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshProblematiques]\"], [\n        refreshData\n    ]);\n    const refreshBlocages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshBlocages]\": async ()=>{\n            const blocageKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshBlocages].blocageKeys\": (key)=>key.includes('blocage')\n            }[\"DataRefreshProvider.useCallback[refreshBlocages].blocageKeys\"]);\n            return refreshData(blocageKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshBlocages]\"], [\n        refreshData\n    ]);\n    const refreshStatistics = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshStatistics]\": async ()=>{\n            const statsKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshStatistics].statsKeys\": (key)=>key.includes('stat') || key.includes('dashboard')\n            }[\"DataRefreshProvider.useCallback[refreshStatistics].statsKeys\"]);\n            return refreshData(statsKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshStatistics]\"], [\n        refreshData\n    ]);\n    const triggerRefreshAfterOperation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[triggerRefreshAfterOperation]\": async (operationType, dataType)=>{\n            console.log(`Déclenchement du rafraîchissement après ${operationType} de ${dataType}`);\n            // Définir quelles données doivent être rafraîchies selon le type d'opération\n            const refreshMap = {\n                'cas': refreshCas,\n                'dossier': refreshCas,\n                'commune': refreshCommunes,\n                'encrage': refreshEncrages,\n                'problematique': refreshProblematiques,\n                'blocage': refreshBlocages,\n                'statistics': refreshStatistics,\n                'dashboard': refreshStatistics\n            };\n            // Rafraîchir les données spécifiques\n            const refreshFunction = refreshMap[dataType.toLowerCase()];\n            if (refreshFunction) {\n                await refreshFunction();\n            }\n            // Pour certaines opérations, rafraîchir aussi les statistiques\n            if ([\n                'cas',\n                'dossier',\n                'blocage'\n            ].includes(dataType.toLowerCase())) {\n                await refreshStatistics();\n            }\n            // Si c'est une opération sur les cas, rafraîchir aussi les données liées\n            if ([\n                'cas',\n                'dossier'\n            ].includes(dataType.toLowerCase())) {\n                await Promise.all([\n                    refreshCommunes(),\n                    refreshEncrages(),\n                    refreshProblematiques()\n                ]);\n            }\n        }\n    }[\"DataRefreshProvider.useCallback[triggerRefreshAfterOperation]\"], [\n        refreshCas,\n        refreshCommunes,\n        refreshEncrages,\n        refreshProblematiques,\n        refreshBlocages,\n        refreshStatistics\n    ]);\n    const value = {\n        registerRefreshCallback,\n        unregisterRefreshCallback,\n        refreshData,\n        refreshAllData,\n        refreshCas,\n        refreshCommunes,\n        refreshEncrages,\n        refreshProblematiques,\n        refreshBlocages,\n        refreshStatistics,\n        triggerRefreshAfterOperation\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DataRefreshContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\contexts\\\\DataRefreshContext.tsx\",\n        lineNumber: 169,\n        columnNumber: 9\n    }, this);\n}\nfunction useDataRefreshContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(DataRefreshContext);\n    if (context === undefined) {\n        throw new Error('useDataRefreshContext must be used within a DataRefreshProvider');\n    }\n    return context;\n}\n// Hook spécialisé pour enregistrer automatiquement les callbacks\nfunction useRegisterDataRefresh(key, callback, dependencies = []) {\n    const { registerRefreshCallback, unregisterRefreshCallback } = useDataRefreshContext();\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"useRegisterDataRefresh.useEffect\": ()=>{\n            registerRefreshCallback(key, callback);\n            return ({\n                \"useRegisterDataRefresh.useEffect\": ()=>{\n                    unregisterRefreshCallback(key);\n                }\n            })[\"useRegisterDataRefresh.useEffect\"];\n        }\n    }[\"useRegisterDataRefresh.useEffect\"], [\n        key,\n        registerRefreshCallback,\n        unregisterRefreshCallback,\n        callback,\n        ...dependencies\n    ]);\n}\n// Hook pour déclencher facilement les rafraîchissements après les opérations\nfunction useOperationRefresh() {\n    const { triggerRefreshAfterOperation } = useDataRefreshContext();\n    const afterCreate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useOperationRefresh.useCallback[afterCreate]\": (dataType)=>{\n            return triggerRefreshAfterOperation('create', dataType);\n        }\n    }[\"useOperationRefresh.useCallback[afterCreate]\"], [\n        triggerRefreshAfterOperation\n    ]);\n    const afterUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useOperationRefresh.useCallback[afterUpdate]\": (dataType)=>{\n            return triggerRefreshAfterOperation('update', dataType);\n        }\n    }[\"useOperationRefresh.useCallback[afterUpdate]\"], [\n        triggerRefreshAfterOperation\n    ]);\n    const afterDelete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useOperationRefresh.useCallback[afterDelete]\": (dataType)=>{\n            return triggerRefreshAfterOperation('delete', dataType);\n        }\n    }[\"useOperationRefresh.useCallback[afterDelete]\"], [\n        triggerRefreshAfterOperation\n    ]);\n    return {\n        afterCreate,\n        afterUpdate,\n        afterDelete\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/contexts/DataRefreshContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/MainContentClient.tsx":
/*!*********************************************!*\
  !*** ./app/dashboard/MainContentClient.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainContentClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_components_SidebarContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/components/SidebarContext */ \"(ssr)/./app/components/SidebarContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction MainContentClient({ children }) {\n    const { isCollapsed } = (0,_app_components_SidebarContext__WEBPACK_IMPORTED_MODULE_1__.useSidebar)();\n    // Suppression du padding left pour coller le contenu à la sidebar\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-0 pt-1 md:pt-1 transition-all duration-300 w-full`,\n        style: {\n            minHeight: \"100vh\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\MainContentClient.tsx\",\n        lineNumber: 13,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZGFzaGJvYXJkL01haW5Db250ZW50Q2xpZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUU2RDtBQUU5QyxTQUFTQyxrQkFBa0IsRUFDdENDLFFBQVEsRUFHWDtJQUNHLE1BQU0sRUFBRUMsV0FBVyxFQUFFLEdBQUdILDBFQUFVQTtJQUNsQyxrRUFBa0U7SUFDbEUscUJBQ0ksOERBQUNJO1FBQ0dDLFdBQVcsQ0FBQyx5REFBeUQsQ0FBQztRQUN0RUMsT0FBTztZQUFFQyxXQUFXO1FBQVE7a0JBRTNCTDs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcYXBwXFxkYXNoYm9hcmRcXE1haW5Db250ZW50Q2xpZW50LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCB7IHVzZVNpZGViYXIgfSBmcm9tIFwiQC9hcHAvY29tcG9uZW50cy9TaWRlYmFyQ29udGV4dFwiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWFpbkNvbnRlbnRDbGllbnQoe1xyXG4gICAgY2hpbGRyZW4sXHJcbn06IHtcclxuICAgIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pIHtcclxuICAgIGNvbnN0IHsgaXNDb2xsYXBzZWQgfSA9IHVzZVNpZGViYXIoKTtcclxuICAgIC8vIFN1cHByZXNzaW9uIGR1IHBhZGRpbmcgbGVmdCBwb3VyIGNvbGxlciBsZSBjb250ZW51IMOgIGxhIHNpZGViYXJcclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2BzcGFjZS15LTAgcHQtMSBtZDpwdC0xIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB3LWZ1bGxgfVxyXG4gICAgICAgICAgICBzdHlsZT17eyBtaW5IZWlnaHQ6IFwiMTAwdmhcIiB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlU2lkZWJhciIsIk1haW5Db250ZW50Q2xpZW50IiwiY2hpbGRyZW4iLCJpc0NvbGxhcHNlZCIsImRpdiIsImNsYXNzTmFtZSIsInN0eWxlIiwibWluSGVpZ2h0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/MainContentClient.tsx\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/statistiques/page.tsx":
/*!*********************************************!*\
  !*** ./app/dashboard/statistiques/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatistiquesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./lib/api-client.ts\");\n/* harmony import */ var chart_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! chart.js */ \"(ssr)/./node_modules/chart.js/dist/chart.js\");\n/* harmony import */ var _app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/components/LoadingSpinner */ \"(ssr)/./app/components/LoadingSpinner.tsx\");\n/* harmony import */ var _app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/components/RoleBasedAccess */ \"(ssr)/./app/components/RoleBasedAccess.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nchart_js__WEBPACK_IMPORTED_MODULE_5__.Chart.register(chart_js__WEBPACK_IMPORTED_MODULE_5__.CategoryScale, chart_js__WEBPACK_IMPORTED_MODULE_5__.LinearScale, chart_js__WEBPACK_IMPORTED_MODULE_5__.BarElement, chart_js__WEBPACK_IMPORTED_MODULE_5__.Title, chart_js__WEBPACK_IMPORTED_MODULE_5__.Tooltip, chart_js__WEBPACK_IMPORTED_MODULE_5__.Legend, chart_js__WEBPACK_IMPORTED_MODULE_5__.ArcElement);\nfunction StatistiquesPage() {\n    const { user } = usePermissions();\n    const [analyseData, setAnalyseData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedWilaya, setSelectedWilaya] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Charger les données d'analyse\n    const loadAnalyse = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const url = selectedWilaya ? `/api/stats/analyse-complete?wilayaId=${selectedWilaya}` : \"/api/stats/analyse-complete\";\n            console.log(\"📊 Chargement de l'analyse depuis:\", url);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(url);\n            if (response.success && response.data) {\n                setAnalyseData(response.data);\n                console.log(\"✅ Analyse chargée:\", response.data);\n            } else {\n                setError(response.error || \"Erreur lors du chargement de l'analyse\");\n            }\n        } catch (err) {\n            console.error(\"Erreur lors du chargement de l'analyse:\", err);\n            setError(err.message || \"Erreur inconnue\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StatistiquesPage.useEffect\": ()=>{\n            loadAnalyse();\n        }\n    }[\"StatistiquesPage.useEffect\"], [\n        selectedWilaya\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingSpinner, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 132,\n            columnNumber: 13\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md border border-gray-200 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-red-600 mb-2\",\n                            children: \"Erreur\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadAnalyse,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                            children: \"R\\xe9essayer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 142,\n            columnNumber: 13\n        }, this);\n    }\n    if (!analyseData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md border border-gray-200 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-2\",\n                            children: \"Aucune donn\\xe9e\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Aucune statistique \\xe0 afficher pour le moment.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadAnalyse,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                            children: \"Actualiser\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 164,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 163,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Analyse des Dossiers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_4__.UserRoleBadge, {\n                                className: \"mt-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: [\n                                    analyseData.totalCas.toLocaleString(),\n                                    \" cas analys\\xe9s sur\",\n                                    \" \",\n                                    analyseData.totalWilayas,\n                                    \" DSA\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedWilaya,\n                                onChange: (e)=>setSelectedWilaya(e.target.value),\n                                className: \"border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 w-64\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Toutes les DSA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 25\n                                    }, this),\n                                    analyseData?.availableWilayas?.map((wilaya)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: wilaya.id.toString(),\n                                            children: wilaya.name\n                                        }, wilaya.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 29\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadAnalyse,\n                                className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: \"Actualiser\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Analyse des Cas par Statut et DSA\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 17\n                    }, this),\n                    analyseData.tableauStatuts.map((statutData)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: [\n                                            \"Statut: \",\n                                            statutData.statut\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"min-w-full divide-y divide-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"DSA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"Total\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"R\\xe9gularis\\xe9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"Ajourn\\xe9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"Rejet\\xe9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"Non examin\\xe9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"Structure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    className: \"bg-white divide-y divide-gray-200\",\n                                                    children: statutData.wilayas.map((wilaya)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                    children: wilaya.dsaName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                    children: wilaya.total.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-green-600\",\n                                                                    children: wilaya.regularise.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-yellow-600\",\n                                                                    children: wilaya.ajourne.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-red-600\",\n                                                                    children: wilaya.rejete.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-600\",\n                                                                    children: wilaya.nonExamine.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    \"Physique:\",\n                                                                                    \" \",\n                                                                                    wilaya.structures?.PHYSIQUE || 0\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 57\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    \"Morale:\",\n                                                                                    \" \",\n                                                                                    wilaya.structures?.MORALE || 0\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 295,\n                                                                                columnNumber: 57\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 53\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, wilaya.wilayaId, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 45\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, statutData.statut, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 21\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md border border-gray-200 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"R\\xe9sum\\xe9 de l'Analyse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-600\",\n                                            children: analyseData.totalCas.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Total des dossiers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-green-600\",\n                                            children: analyseData.chartStatuts.datasets[0].data[0].toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"R\\xe9gularis\\xe9s\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-yellow-600\",\n                                            children: analyseData.chartStatuts.datasets[0].data[1].toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Ajourn\\xe9s\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-red-600\",\n                                            children: analyseData.chartStatuts.datasets[0].data[2].toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Rejet\\xe9s\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 185,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/statistiques/page.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   fetchApi: () => (/* binding */ fetchApi),\n/* harmony export */   getCas: () => (/* binding */ getCas),\n/* harmony export */   getChat: () => (/* binding */ getChat),\n/* harmony export */   sendMessage: () => (/* binding */ sendMessage)\n/* harmony export */ });\nasync function fetchApi(endpoint, options = {}) {\n    const { method = \"GET\", body } = options;\n    const response = await fetch(endpoint, {\n        method,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...options.headers\n        },\n        credentials: options.credentials || \"include\",\n        body: body ? JSON.stringify(body) : undefined\n    });\n    if (!response.ok) {\n        // Read the response body as text first\n        const errorText = await response.text();\n        let errorData;\n        try {\n            // Try to parse the text as JSON\n            errorData = JSON.parse(errorText);\n        } catch (e) {\n            // If parsing fails, use the raw text as the error message\n            errorData = {\n                error: errorText\n            };\n        }\n        const errorMessage = errorData?.error || errorData?.message || `HTTP error! status: ${response.status}`;\n        // Ajouter des détails de debug\n        console.error(\"🚨 Erreur API détaillée:\", {\n            url: response.url,\n            status: response.status,\n            statusText: response.statusText,\n            errorData,\n            errorText: errorText.substring(0, 500)\n        });\n        throw new Error(errorMessage);\n    }\n    if (response.status === 204) {\n        // No Content\n        return null;\n    }\n    return response.json();\n}\n// Add the following apiClient export:\nconst apiClient = {\n    get: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"GET\"\n        });\n    },\n    post: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"POST\",\n            body\n        });\n    },\n    put: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"PUT\",\n            body\n        });\n    },\n    delete: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"DELETE\"\n        });\n    }\n};\n// Chat Functions\nconst getChat = (casId)=>{\n    return apiClient.get(`/api/chats/${casId}`);\n};\nconst sendMessage = (casId, content)=>{\n    return apiClient.post(`/api/chats/${casId}/messages`, {\n        content\n    });\n};\nasync function fetchPage(page, pageSize) {\n    const response = await fetch(`/api/cas?page=${page}&pageSize=${pageSize}`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        credentials: \"include\"\n    });\n    if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"API Error response:\", errorText);\n        throw new Error(`API request failed with status ${response.status}`);\n    }\n    return response.json();\n}\nasync function getCas() {\n    try {\n        console.log(\"Fetching dossiers from /api/cas...\");\n        const pageSize = 100; // Maximum allowed by the API\n        let currentPage = 1;\n        let allDossiers = [];\n        let hasMorePages = true;\n        let totalPages = 1;\n        // Fetch all pages\n        while(hasMorePages && currentPage <= 20){\n            // Add a safety limit of 20 pages\n            console.log(`Fetching page ${currentPage}...`);\n            const result = await fetchPage(currentPage, pageSize);\n            if (!result.data || !Array.isArray(result.data)) {\n                console.error(\"Invalid data format in page\", currentPage, \":\", result);\n                throw new Error(\"Invalid data format received from server\");\n            }\n            allDossiers = [\n                ...allDossiers,\n                ...result.data\n            ];\n            totalPages = result.pagination.totalPages;\n            hasMorePages = result.pagination.hasNextPage && currentPage < totalPages;\n            currentPage++;\n            // If we've fetched all pages or reached the safety limit, stop\n            if (!hasMorePages || currentPage > totalPages) {\n                break;\n            }\n        }\n        console.log(`Fetched ${allDossiers.length} dossiers from ${currentPage - 1} pages`);\n        return allDossiers;\n    } catch (error) {\n        if (error instanceof Error) {\n            console.error(\"Error in getCas:\", {\n                message: error.message,\n                name: error.name,\n                stack: error.stack\n            });\n        } else {\n            console.error(\"Unknown error in getCas:\", error);\n        }\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/client-permissions.ts":
/*!***********************************!*\
  !*** ./lib/client-permissions.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canDelete: () => (/* binding */ canDelete),\n/* harmony export */   canManageUsers: () => (/* binding */ canManageUsers),\n/* harmony export */   canSendMessages: () => (/* binding */ canSendMessages),\n/* harmony export */   canUploadFiles: () => (/* binding */ canUploadFiles),\n/* harmony export */   canWrite: () => (/* binding */ canWrite),\n/* harmony export */   getPermissionsByRole: () => (/* binding */ getPermissionsByRole),\n/* harmony export */   getRoleInfo: () => (/* binding */ getRoleInfo),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   isReadOnly: () => (/* binding */ isReadOnly)\n/* harmony export */ });\n// Client-side permissions - safe to use in Client Components\n/* __next_internal_client_entry_do_not_use__ getPermissionsByRole,hasPermission,canWrite,canDelete,canManageUsers,canUploadFiles,canSendMessages,isReadOnly,getRoleInfo auto */ /**\n * Get user permissions based on their role (client-side)\n */ function getPermissionsByRole(role) {\n    switch(role){\n        case \"ADMIN\":\n            return {\n                canRead: true,\n                canWrite: true,\n                canDelete: true,\n                canManageUsers: true,\n                canUploadFiles: true,\n                canSendMessages: true,\n                isReadOnly: false\n            };\n        case \"EDITOR\":\n            return {\n                canRead: true,\n                canWrite: true,\n                canDelete: true,\n                canManageUsers: false,\n                canUploadFiles: true,\n                canSendMessages: true,\n                isReadOnly: false\n            };\n        case \"BASIC\":\n            return {\n                canRead: true,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: true,\n                isReadOnly: true\n            };\n        case \"VIEWER\":\n            return {\n                canRead: true,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: false,\n                isReadOnly: true\n            };\n        default:\n            return {\n                canRead: false,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: false,\n                isReadOnly: true\n            };\n    }\n}\n/**\n * Check if a role has a specific permission\n */ function hasPermission(role, permission) {\n    const permissions = getPermissionsByRole(role);\n    return permissions[permission];\n}\n/**\n * Check if a role can perform write operations\n */ function canWrite(role) {\n    return hasPermission(role, \"canWrite\");\n}\n/**\n * Check if a role can perform delete operations\n */ function canDelete(role) {\n    return hasPermission(role, \"canDelete\");\n}\n/**\n * Check if a role can manage users\n */ function canManageUsers(role) {\n    return hasPermission(role, \"canManageUsers\");\n}\n/**\n * Check if a role can upload files\n */ function canUploadFiles(role) {\n    return hasPermission(role, \"canUploadFiles\");\n}\n/**\n * Check if a role can send messages\n */ function canSendMessages(role) {\n    return hasPermission(role, \"canSendMessages\");\n}\n/**\n * Check if a role is read-only\n */ function isReadOnly(role) {\n    return hasPermission(role, \"isReadOnly\");\n}\n/**\n * Get role display information\n */ function getRoleInfo(role) {\n    switch(role){\n        case \"ADMIN\":\n            return {\n                label: \"Administrateur\",\n                color: \"bg-red-100 text-red-800 border-red-200\",\n                description: \"Accès complet à toutes les fonctionnalités\"\n            };\n        case \"EDITOR\":\n            return {\n                label: \"Éditeur\",\n                color: \"bg-green-100 text-green-800 border-green-200\",\n                description: \"Peut créer et modifier des dossiers\"\n            };\n        case \"BASIC\":\n            return {\n                label: \"Utilisateur\",\n                color: \"bg-blue-100 text-blue-800 border-blue-200\",\n                description: \"Accès en lecture seule avec messagerie\"\n            };\n        case \"VIEWER\":\n            return {\n                label: \"Lecteur\",\n                color: \"bg-gray-100 text-gray-800 border-gray-200\",\n                description: \"Accès en lecture seule uniquement\"\n            };\n        default:\n            return {\n                label: \"Inconnu\",\n                color: \"bg-gray-100 text-gray-800 border-gray-200\",\n                description: \"Rôle non défini\"\n            };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/client-permissions.ts\n");

/***/ }),

/***/ "(ssr)/./lib/hooks/usePermissions.ts":
/*!*************************************!*\
  !*** ./lib/hooks/usePermissions.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCanDelete: () => (/* binding */ useCanDelete),\n/* harmony export */   useCanManageUsers: () => (/* binding */ useCanManageUsers),\n/* harmony export */   useCanSendMessages: () => (/* binding */ useCanSendMessages),\n/* harmony export */   useCanUploadFiles: () => (/* binding */ useCanUploadFiles),\n/* harmony export */   useCanWrite: () => (/* binding */ useCanWrite),\n/* harmony export */   useHasPermission: () => (/* binding */ useHasPermission),\n/* harmony export */   useHasRole: () => (/* binding */ useHasRole),\n/* harmony export */   useIsReadOnly: () => (/* binding */ useIsReadOnly),\n/* harmony export */   usePermissions: () => (/* binding */ usePermissions)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_client_permissions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/client-permissions */ \"(ssr)/./lib/client-permissions.ts\");\n/* __next_internal_client_entry_do_not_use__ usePermissions,useHasPermission,useHasRole,useCanWrite,useCanDelete,useCanManageUsers,useCanUploadFiles,useCanSendMessages,useIsReadOnly auto */ \n\n/**\n * Hook to get current user permissions and role-based access control\n */ function usePermissions() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePermissions.useEffect\": ()=>{\n            const fetchUserAndPermissions = {\n                \"usePermissions.useEffect.fetchUserAndPermissions\": async ()=>{\n                    try {\n                        // Use fetch instead of apiClient to avoid circular dependencies\n                        const response = await fetch(\"/api/auth/me\", {\n                            method: \"GET\",\n                            credentials: \"include\"\n                        });\n                        if (response.ok) {\n                            const userData = await response.json();\n                            setUser(userData);\n                            const userPermissions = (0,_lib_client_permissions__WEBPACK_IMPORTED_MODULE_1__.getPermissionsByRole)(userData.role);\n                            setPermissions(userPermissions);\n                        } else {\n                            setUser(null);\n                            setPermissions(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to fetch user permissions:\", error);\n                        setUser(null);\n                        setPermissions(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"usePermissions.useEffect.fetchUserAndPermissions\"];\n            fetchUserAndPermissions();\n        }\n    }[\"usePermissions.useEffect\"], []);\n    return {\n        user,\n        permissions,\n        loading,\n        isAdmin: user?.role === \"ADMIN\",\n        isEditor: user?.role === \"EDITOR\",\n        isBasic: user?.role === \"BASIC\",\n        isViewer: user?.role === \"VIEWER\",\n        canRead: permissions?.canRead ?? false,\n        canWrite: permissions?.canWrite ?? false,\n        canDelete: permissions?.canDelete ?? false,\n        canManageUsers: permissions?.canManageUsers ?? false,\n        canUploadFiles: permissions?.canUploadFiles ?? false,\n        canSendMessages: permissions?.canSendMessages ?? false,\n        isReadOnly: permissions?.isReadOnly ?? true\n    };\n}\n/**\n * Hook to check if user has specific permission\n */ function useHasPermission(permission) {\n    const { permissions } = usePermissions();\n    return permissions?.[permission] ?? false;\n}\n/**\n * Hook to check if user has any of the specified roles\n */ function useHasRole(roles) {\n    const { user } = usePermissions();\n    if (!user) return false;\n    const roleArray = Array.isArray(roles) ? roles : [\n        roles\n    ];\n    return roleArray.includes(user.role);\n}\n/**\n * Hook to check if user can perform write operations\n */ function useCanWrite() {\n    return useHasPermission(\"canWrite\");\n}\n/**\n * Hook to check if user can perform delete operations\n */ function useCanDelete() {\n    return useHasPermission(\"canDelete\");\n}\n/**\n * Hook to check if user can manage other users\n */ function useCanManageUsers() {\n    return useHasPermission(\"canManageUsers\");\n}\n/**\n * Hook to check if user can upload files\n */ function useCanUploadFiles() {\n    return useHasPermission(\"canUploadFiles\");\n}\n/**\n * Hook to check if user can send messages\n */ function useCanSendMessages() {\n    return useHasPermission(\"canSendMessages\");\n}\n/**\n * Hook to check if user is in read-only mode\n */ function useIsReadOnly() {\n    return useHasPermission(\"isReadOnly\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/hooks/usePermissions.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccontexts%5C%5CDataRefreshContext.tsx%22%2C%22ids%22%3A%5B%22DataRefreshProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccontexts%5C%5CDataRefreshContext.tsx%22%2C%22ids%22%3A%5B%22DataRefreshProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Footer.tsx */ \"(ssr)/./app/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Navigation.tsx */ \"(ssr)/./app/components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/contexts/DataRefreshContext.tsx */ \"(ssr)/./app/contexts/DataRefreshContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccontexts%5C%5CDataRefreshContext.tsx%22%2C%22ids%22%3A%5B%22DataRefreshProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebarContext.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5CMainContentClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebarContext.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5CMainContentClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Sidebar.tsx */ \"(ssr)/./app/components/Sidebar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/SidebarContext.tsx */ \"(ssr)/./app/components/SidebarContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/MainContentClient.tsx */ \"(ssr)/./app/dashboard/MainContentClient.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1JvdWxhJTVDJTVDRGVza3RvcCU1QyU1Q0FQUExJQ0FUSU9OUyU1QyU1Q2Fzc2Fpbmlzc2VtZW50VjUlNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDU2lkZWJhci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJTaWRlYmFyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1JvdWxhJTVDJTVDRGVza3RvcCU1QyU1Q0FQUExJQ0FUSU9OUyU1QyU1Q2Fzc2Fpbmlzc2VtZW50VjUlNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDU2lkZWJhckNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyU2lkZWJhclByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1JvdWxhJTVDJTVDRGVza3RvcCU1QyU1Q0FQUExJQ0FUSU9OUyU1QyU1Q2Fzc2Fpbmlzc2VtZW50VjUlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNNYWluQ29udGVudENsaWVudC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBeUo7QUFDeko7QUFDQSxrTEFBd0s7QUFDeEs7QUFDQSxzTEFBa0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlNpZGViYXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxSb3VsYVxcXFxEZXNrdG9wXFxcXEFQUExJQ0FUSU9OU1xcXFxhc3NhaW5pc3NlbWVudFY1XFxcXGFwcFxcXFxjb21wb25lbnRzXFxcXFNpZGViYXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTaWRlYmFyUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxSb3VsYVxcXFxEZXNrdG9wXFxcXEFQUExJQ0FUSU9OU1xcXFxhc3NhaW5pc3NlbWVudFY1XFxcXGFwcFxcXFxjb21wb25lbnRzXFxcXFNpZGViYXJDb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFJvdWxhXFxcXERlc2t0b3BcXFxcQVBQTElDQVRJT05TXFxcXGFzc2Fpbmlzc2VtZW50VjVcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxNYWluQ29udGVudENsaWVudC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CSidebarContext.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5CMainContentClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5Cstatistiques%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5Cstatistiques%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/statistiques/page.tsx */ \"(ssr)/./app/dashboard/statistiques/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1JvdWxhJTVDJTVDRGVza3RvcCU1QyU1Q0FQUExJQ0FUSU9OUyU1QyU1Q2Fzc2Fpbmlzc2VtZW50VjUlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNzdGF0aXN0aXF1ZXMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQXNJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxSb3VsYVxcXFxEZXNrdG9wXFxcXEFQUExJQ0FUSU9OU1xcXFxhc3NhaW5pc3NlbWVudFY1XFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcc3RhdGlzdGlxdWVzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5Cstatistiques%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/@heroicons","vendor-chunks/tailwind-merge","vendor-chunks/chart.js","vendor-chunks/@kurkle"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fstatistiques%2Fpage&page=%2Fdashboard%2Fstatistiques%2Fpage&appPaths=%2Fdashboard%2Fstatistiques%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fstatistiques%2Fpage.tsx&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();