import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyToken } from "@/lib/auth";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token manquant" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload || userPayload.role !== "ADMIN") {
            return NextResponse.json(
                { error: "Accès non autorisé - Admin requis" },
                { status: 403 }
            );
        }

        console.log("🧪 Test de l'analyse complète - Vérification de la cohérence des données");
        const startTime = performance.now();

        // 1. Compter tous les cas dans la base de données
        const totalCasDB = await prisma.cas.count();
        console.log(`📊 Total cas dans la DB: ${totalCasDB}`);

        // 2. Tester l'API d'analyse complète
        const analyseResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/stats/analyse-complete`, {
            headers: {
                'Cookie': `token=${token}`
            }
        });

        if (!analyseResponse.ok) {
            throw new Error(`Erreur API analyse: ${analyseResponse.status}`);
        }

        const analyseData = await analyseResponse.json();
        
        // 3. Vérifications de cohérence
        const tests = {
            totalCasConsistency: {
                description: "Vérification que tous les cas sont analysés",
                dbTotal: totalCasDB,
                analyseTotal: analyseData.data.totalCas,
                passed: totalCasDB === analyseData.data.totalCas,
                message: totalCasDB === analyseData.data.totalCas 
                    ? "✅ Tous les cas sont analysés" 
                    : `❌ Incohérence: DB=${totalCasDB}, Analyse=${analyseData.data.totalCas}`
            },
            
            secteurInclusion: {
                description: "Vérification que le secteur est inclus dans l'analyse par contrainte",
                secteurFound: analyseData.data.tableauContraintes.some((dsa: any) => 
                    dsa.encrages.some((encrage: any) => encrage.secteur && encrage.secteur !== "Secteur non défini")
                ),
                passed: false,
                message: ""
            },
            
            fusionTableStatuts: {
                description: "Vérification de la structure fusionnée des statuts",
                hasMultipleStatuts: analyseData.data.tableauStatuts.length > 1,
                passed: true,
                message: `✅ ${analyseData.data.tableauStatuts.length} types de statuts analysés`
            },
            
            dataIntegrity: {
                description: "Vérification de l'intégrité des données",
                totalFromStatuts: 0,
                totalFromContraintes: 0,
                passed: false,
                message: ""
            }
        };

        // Calcul des totaux pour vérification
        tests.dataIntegrity.totalFromStatuts = analyseData.data.tableauStatuts.reduce((sum: number, statut: any) => 
            sum + statut.wilayas.reduce((wilayaSum: number, wilaya: any) => wilayaSum + wilaya.total, 0), 0
        );

        tests.dataIntegrity.totalFromContraintes = analyseData.data.tableauContraintes.reduce((sum: number, dsa: any) => 
            sum + dsa.encrages.reduce((encrageSum: number, encrage: any) => encrageSum + encrage.totalCas, 0), 0
        );

        tests.dataIntegrity.passed = tests.dataIntegrity.totalFromStatuts === tests.dataIntegrity.totalFromContraintes;
        tests.dataIntegrity.message = tests.dataIntegrity.passed 
            ? "✅ Cohérence entre statuts et contraintes" 
            : `❌ Incohérence: Statuts=${tests.dataIntegrity.totalFromStatuts}, Contraintes=${tests.dataIntegrity.totalFromContraintes}`;

        tests.secteurInclusion.passed = tests.secteurInclusion.secteurFound;
        tests.secteurInclusion.message = tests.secteurInclusion.passed 
            ? "✅ Secteur inclus dans l'analyse par contrainte" 
            : "❌ Secteur manquant dans l'analyse par contrainte";

        // 4. Statistiques détaillées
        const detailedStats = {
            casParWilaya: analyseData.data.tableauStatuts[0]?.wilayas.length || 0,
            encragesAnalyses: analyseData.data.tableauContraintes.reduce((sum: number, dsa: any) => sum + dsa.encrages.length, 0),
            problematiquesAnalysees: analyseData.data.tableauContraintes.reduce((sum: number, dsa: any) => 
                sum + dsa.encrages.reduce((encrageSum: number, encrage: any) => encrageSum + encrage.problematiques.length, 0), 0
            ),
            secteursUniques: new Set(
                analyseData.data.tableauContraintes.flatMap((dsa: any) => 
                    dsa.encrages.map((encrage: any) => encrage.secteur)
                ).filter((secteur: string) => secteur && secteur !== "Secteur non défini")
            ).size
        };

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        const allTestsPassed = Object.values(tests).every(test => test.passed);

        return NextResponse.json({
            success: true,
            message: "Test de l'analyse complète terminé",
            allTestsPassed,
            tests,
            detailedStats,
            performance: {
                duration,
                timestamp: new Date().toISOString()
            },
            recommendations: allTestsPassed ? [
                "✅ Toutes les vérifications sont passées",
                "✅ L'analyse inclut tous les dossiers de la base de données",
                "✅ Le secteur est correctement inclus dans l'analyse par contrainte",
                "✅ Les données sont cohérentes entre les différentes vues"
            ] : [
                "⚠️ Certaines vérifications ont échoué",
                "🔍 Vérifiez les logs pour plus de détails",
                "🛠️ Corrigez les problèmes identifiés avant la mise en production"
            ]
        });

    } catch (error: any) {
        console.error('❌ Erreur dans test analyse complète:', error);
        return NextResponse.json({
            success: false,
            error: "Erreur lors du test de l'analyse complète",
            details: error.message
        }, { status: 500 });
    }
}
