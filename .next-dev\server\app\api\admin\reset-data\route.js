/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/reset-data/route";
exports.ids = ["app/api/admin/reset-data/route"];
exports.modules = {

/***/ "(rsc)/./app/api/admin/reset-data/route.ts":
/*!*******************************************!*\
  !*** ./app/api/admin/reset-data/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n\nasync function POST(request) {\n    try {\n        // Vérification de l'authentification\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Token manquant\"\n            }, {\n                status: 401\n            });\n        }\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)(token);\n        if (!userPayload || userPayload.role !== \"ADMIN\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Accès non autorisé - Admin requis\"\n            }, {\n                status: 403\n            });\n        }\n        console.log(\"🔄 Début de la réinitialisation des données...\");\n        const startTime = performance.now();\n        // Étape 1: Compter les données avant réinitialisation\n        const beforeStats = {\n            totalCas: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count(),\n            casRegularises: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                where: {\n                    regularisation: true\n                }\n            }),\n            totalBlocages: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count(),\n            blocagesAcceptes: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                where: {\n                    resolution: \"ACCEPTE\"\n                }\n            }),\n            blocagesAjournes: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                where: {\n                    resolution: \"AJOURNE\"\n                }\n            }),\n            blocagesRejetes: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                where: {\n                    resolution: \"REJETE\"\n                }\n            }),\n            blocagesAttente: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                where: {\n                    resolution: \"ATTENTE\"\n                }\n            })\n        };\n        console.log(\"📊 Statistiques avant réinitialisation:\", beforeStats);\n        // Étape 2: Réinitialiser toutes les résolutions de blocage à \"ATTENTE\"\n        console.log(\"🔄 Réinitialisation des résolutions de blocage...\");\n        const blocageUpdateResult = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.updateMany({\n            data: {\n                resolution: \"ATTENTE\"\n            }\n        });\n        console.log(`✅ ${blocageUpdateResult.count} résolutions de blocage mises à jour vers ATTENTE`);\n        // Étape 3: Réinitialiser toutes les régularisations de cas à false\n        console.log(\"🔄 Réinitialisation des régularisations de cas...\");\n        const casUpdateResult = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.updateMany({\n            data: {\n                regularisation: false\n            }\n        });\n        console.log(`✅ ${casUpdateResult.count} régularisations de cas mises à jour vers false`);\n        // Étape 4: Vérifier les résultats après réinitialisation\n        const afterStats = {\n            totalCas: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count(),\n            casRegularises: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                where: {\n                    regularisation: true\n                }\n            }),\n            totalBlocages: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count(),\n            blocagesAcceptes: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                where: {\n                    resolution: \"ACCEPTE\"\n                }\n            }),\n            blocagesAjournes: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                where: {\n                    resolution: \"AJOURNE\"\n                }\n            }),\n            blocagesRejetes: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                where: {\n                    resolution: \"REJETE\"\n                }\n            }),\n            blocagesAttente: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count({\n                where: {\n                    resolution: \"ATTENTE\"\n                }\n            })\n        };\n        console.log(\"📊 Statistiques après réinitialisation:\", afterStats);\n        const endTime = performance.now();\n        const duration = Math.round(endTime - startTime);\n        // Vérifications de cohérence\n        const verifications = {\n            allCasNonRegularises: afterStats.casRegularises === 0,\n            allBlocagesEnAttente: afterStats.blocagesAttente === afterStats.totalBlocages,\n            aucunBlocageAccepte: afterStats.blocagesAcceptes === 0,\n            aucunBlocageAjourne: afterStats.blocagesAjournes === 0,\n            aucunBlocageRejete: afterStats.blocagesRejetes === 0\n        };\n        const allVerificationsPassed = Object.values(verifications).every((v)=>v === true);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Réinitialisation des données terminée avec succès\",\n            allVerificationsPassed,\n            operations: {\n                blocagesUpdated: blocageUpdateResult.count,\n                casUpdated: casUpdateResult.count\n            },\n            statistics: {\n                before: beforeStats,\n                after: afterStats\n            },\n            verifications: {\n                allCasNonRegularises: verifications.allCasNonRegularises ? \"✅ Tous les cas sont non régularisés\" : \"❌ Certains cas restent régularisés\",\n                allBlocagesEnAttente: verifications.allBlocagesEnAttente ? \"✅ Tous les blocages sont en attente\" : \"❌ Certains blocages ne sont pas en attente\",\n                aucunBlocageAccepte: verifications.aucunBlocageAccepte ? \"✅ Aucun blocage accepté\" : \"❌ Des blocages restent acceptés\",\n                aucunBlocageAjourne: verifications.aucunBlocageAjourne ? \"✅ Aucun blocage ajourné\" : \"❌ Des blocages restent ajournés\",\n                aucunBlocageRejete: verifications.aucunBlocageRejete ? \"✅ Aucun blocage rejeté\" : \"❌ Des blocages restent rejetés\"\n            },\n            performance: {\n                duration,\n                timestamp: new Date().toISOString()\n            },\n            summary: {\n                message: allVerificationsPassed ? \"🎉 Réinitialisation réussie ! Toutes les données ont été correctement réinitialisées.\" : \"⚠️ Réinitialisation terminée mais avec des anomalies. Vérifiez les détails.\",\n                casAffectes: casUpdateResult.count,\n                blocagesAffectes: blocageUpdateResult.count,\n                tempsExecution: `${duration}ms`\n            }\n        });\n    } catch (error) {\n        console.error('❌ Erreur lors de la réinitialisation:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Erreur lors de la réinitialisation des données\",\n            details: error.message,\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/reset-data/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freset-data%2Froute&page=%2Fapi%2Fadmin%2Freset-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freset-data%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freset-data%2Froute&page=%2Fapi%2Fadmin%2Freset-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freset-data%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_admin_reset_data_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/reset-data/route.ts */ \"(rsc)/./app/api/admin/reset-data/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/reset-data/route\",\n        pathname: \"/api/admin/reset-data\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/reset-data/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\admin\\\\reset-data\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_admin_reset_data_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freset-data%2Froute&page=%2Fapi%2Fadmin%2Freset-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freset-data%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freset-data%2Froute&page=%2Fapi%2Fadmin%2Freset-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freset-data%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();